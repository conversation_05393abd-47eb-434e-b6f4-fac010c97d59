<template>
  <view class="video-player-container">
    <video
      v-if="videoUrl"
      :src="videoUrl"
      :poster="posterUrl"
      autoplay
      controls
      object-fit="contain"
      class="fullscreen-video"
      @error="handleVideoError"
      @ended="closeSubNVue" 
      @fullscreenchange="handleFullscreenChange"
    ></video>
    <!-- 使用 cover-view 添加关闭按钮 -->
    <cover-view class="close-button" @tap="closeSubNVue">×</cover-view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        videoUrl: '',
        posterUrl: '',
        // 获取状态栏高度，用于定位关闭按钮
        statusBarHeight: uni.getSystemInfoSync().statusBarHeight + 'px' 
      };
    },
    onLoad() {
      // 监听从父页面传递过来的数据
      uni.$on('videoPlayerData', (data) => {
        console.log('subNVue received data:', data);
        this.videoUrl = data.url || '';
        this.posterUrl = data.poster || '';
      });
      
      // 通知父页面 subNVue 已准备好接收数据（可选，但更健壮）
      // uni.$emit('subNVueReady'); 
    },
    beforeDestroy() {
      // 组件销毁前移除监听
      uni.$off('videoPlayerData');
    },
    methods: {
      handleVideoError(e) {
        console.error('subNVue 视频加载或播放错误:', e.detail.errMsg);
        // 可以通知父页面关闭 subNVue
        this.closeSubNVue();
      },
      closeSubNVue() {
        console.log('subNVue closing');
        // 获取当前 subNVue 实例并隐藏
        const currentWebview = plus.webview.currentWebview();
        currentWebview.hide('auto'); // 使用 'auto' 或其他动画效果
        // 清空视频地址，防止下次打开残留
        this.videoUrl = ''; 
        this.posterUrl = '';
        // 可以通知父页面 subNVue 已关闭（如果需要）
        // uni.$emit('subNVueClosed');
      },
      handleFullscreenChange(e) {
          // 在 nvue 中，全屏变化可能需要特殊处理或传递给父页面
          console.log('subNVue fullscreen change:', e.detail.fullScreen);
          // 如果退出全屏，也关闭 subNVue
          if (!e.detail.fullScreen) {
              // 延迟一点关闭，避免原生控件退出和hide冲突
              setTimeout(() => {
                  this.closeSubNVue();
              }, 100); 
          }
      }
    }
  }
</script>

<style scoped>
  .video-player-container {
    /* #ifndef APP-PLUS-NVUE */
    display: flex;
    /* #endif */
    flex: 1;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #000000;
    align-items: center;
    justify-content: center;
  }
  .fullscreen-video {
    width: 750rpx; /* nvue 中建议使用 rpx 或 px */
    flex: 1; /* 让视频尽可能撑满 */
    height: 100%; /* 尝试让高度也撑满 */
  }
  .close-button {
    position: absolute;
    top: calc(env(safe-area-inset-top) + 20rpx); /* 适配刘海屏 */
    /* top: calc(statusBarHeight + 20rpx); */ /* 使用计算的状态栏高度 */
    right: 30rpx;
    width: 60rpx;
    height: 60rpx;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 30rpx;
    color: #FFFFFF;
    font-size: 40rpx;
    /* #ifndef APP-PLUS-NVUE */
    display: flex;
    /* #endif */
    justify-content: center;
    align-items: center;
    text-align: center; /* nvue 中 cover-view 内文字居中 */
    line-height: 60rpx; /* nvue 中 cover-view 内文字垂直居中 */
  }
</style>