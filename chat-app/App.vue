<script>
	
// 导入设置 API 基础 URL 的函数
import { SetBaseUrl } from "./api/http.js";
// 导入使用 Token 登录的 API 函数
import { TokenLogin,getConfig } from "./api/api.js";
import { initConnectionService } from './utils/connectionService.js';

// import { decryptAESBase64, encryptAESBase64 } from '../utils/decrypt.js'

export default {
  /**
   * App 启动时触发（全局只触发一次）
   */

  data() {
  	return {
  		tk:''
  	}
  },
  onLaunch: function (options) {
	
	
	
	  
	


	console.log("App Launch",options);
	if(options.path=='pages/test/voice-call')return
	// console.log(decryptAESBase64('sum1KTLFwlr8uJUvAUrVow=='))
    // 从本地缓存中获取之前存储的 baseUrl 和 token
    let bsul = uni.getStorageSync("baseUrl");
    this.tk = uni.getStorageSync("token");

    // 检查 baseUrl 和 token 是否都存在
    if (bsul && this.tk) {
      // 如果存在，则设置 API 的基础 URL
      SetBaseUrl(bsul);
      // 显示加载提示
      uni.showLoading({ title: '自动登录中...' });
      // 尝试使用存储的 Token 进行自动登录
      TokenLogin().then((res) => {
        // 自动登录成功后的处理
        // console.log("Token 登录成功:", res);
		
		// 自动登录成功，初始化连接服务
		initConnectionService(this.tk)
			.then((success) => {
				if (success) {
					console.log('自动登录后连接服务初始化成功');
				} else {
					console.error('自动登录后连接服务初始化失败');
				}
			})
			.catch((error) => {
				console.error('自动登录后连接服务初始化异常:', error);
			});


        // 跳转到聊天列表页面 (Tab 页)
        uni.switchTab({
          url: "/pages/chats/chats",
        });
      }).catch(err => {
        // Token 登录失败的处理
        console.error("Token 登录失败:", err);
        // 清除可能无效的 token 和 baseUrl
        uni.removeStorageSync('token');
        uni.removeStorageSync('baseUrl');
        // 跳转到登录页面
        uni.redirectTo({
          url: "/pages/login/login",
        });
      }).finally(() => {
        // 无论成功或失败，都隐藏加载提示
        uni.hideLoading();
      });
    } else {
      // 如果本地没有 baseUrl 或 token，则直接跳转到登录页面
      console.log("未找到本地 Token 或 BaseUrl，跳转到登录页");
	  uni.navigateTo({
	  	url:"/pages/login/login",
	  })
      // uni.redirectTo({
      //   url: "/pages/login/login",
      // });
    }
  },
  /**
   * App 从后台进入前台显示时触发
   */
  onShow: function () {
    console.log("App Show");
    // 可以在这里处理 App 从后台恢复时需要执行的操作
	// uni.$on('chat-message-received', (message) => {
	// 		console.log('全局消息处理',message)
	// 		if(message.typecode2==9&&message.toid==uni.getStorageSync("userId")){
	// 			console.log('语音通话请求')
	// 			uni.navigateTo({
	// 			  url: '/pages/call/voice-call?newMessage=' + JSON.stringify(message)+'&toId='+message.fromid+"&isCaller=false&id="+message.id,
	// 			  success: () => {
	// 			    console.log('成功跳转到语音通话页面');
	// 			  },
	// 			  fail: (err) => {
	// 			    console.error('打开语音通话页面失败', err);
	// 			    // 如果失败，尝试再次检查pages.json配置
	// 			    uni.showToast({
	// 			      title: '打开语音通话失败',
	// 			      icon: 'none',
	// 			      duration: 3000
	// 			    });
	// 			  }
	// 			});
				
	// 		}
	// 	})
  },
  /**
   * App 从前台进入后台时触发
   */
  onHide: function () {
	  // plus.runtime.setBadgeNumber(19)
    console.log("App Hide");
	// closeSQL()
    // 可以在这里处理 App 进入后台时需要执行的操作，例如保存状态
  },
  methods:{
	  // init方法已移除，数据库和WebSocket初始化逻辑已移到connectionService中
  }
};
</script>

<style lang="scss">
/* 引入 uview-plus 的全局 SCSS 样式 */
@import "@/uni_modules/uview-plus/index.scss";
/* 你可以在这里添加其他的全局样式 */

/* 移除微信风格气泡菜单容器样式 */
/*
.bubble-menu-container {
  position: absolute;
  z-index: 99999;
  pointer-events: auto;
  background-color: red !important;
  border: 1px solid blue;
}
*/

/* bubble-menu 样式定义菜单本身的外观，保持不变 */
.bubble-menu {
  background: rgba(0, 0, 0, 0.7);
  border-radius: 12rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  padding: 20rpx;
  height: 140rpx;
  min-width: 300rpx;
}

.self-menu {
  /* 可能需要调整尖角位置，如果要做尖角的话 */
  /* 如果 popup type=center，这个类可能不再需要 */
}

.bubble-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 14rpx;
  height: 100%;
  min-width: 60rpx;
}

.menu-icon-wrapper {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  margin-bottom: 10rpx;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
}

.menu-text {
  font-size: 20rpx;
  color: #fff;
  text-align: center;
}

.bubble-menu-item:active .menu-icon-wrapper {
  background-color: rgba(255, 255, 255, 0.25);
}
</style>
