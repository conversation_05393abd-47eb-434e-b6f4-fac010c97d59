// composables/useScrollHeight.js
import { ref, onMounted } from 'vue'

/**
 * 计算可滚动区域高度
 * @param {Array} fixedHeights 固定高度的组件高度数组（单位rpx）
 * @returns {Object} 包含 scrollHeight 和 recalculate 方法的对象
 */
export function useScrollHeight(fixedHeights = []) {
  const scrollHeight = ref('0px')
  
  const calculateHeight = () => {
    // 获取系统信息
    const systemInfo = uni.getSystemInfoSync()
    
    // 将所有固定高度从rpx转换为px
    const fixedPxHeights = fixedHeights.map(rpx => {
      return (rpx / 750) * systemInfo.windowWidth
    })
    
    // 计算总固定高度
    const totalFixedHeight = fixedPxHeights.reduce((sum, height) => sum + height, 0)
    
    // 计算剩余可用高度
    const availableHeight = systemInfo.windowHeight - totalFixedHeight
    
    scrollHeight.value = `${availableHeight}px`
  }
  
  // 初始化计算
  onMounted(calculateHeight)
  
  // 返回高度值和重新计算的方法
  return {
    scrollHeight,
    recalculate: calculateHeight
  }
}