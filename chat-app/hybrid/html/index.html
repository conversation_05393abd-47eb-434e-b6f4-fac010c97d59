<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC 语音通话</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .title {
            font-size: 24px;
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
        }
        
        .status {
            color: #666;
            font-size: 14px;
        }
        
        .info-box {
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .panel, .incoming-call {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        
        button {
            padding: 10px 15px;
            border-radius: 4px;
            border: none;
            color: white;
            font-weight: bold;
            margin-right: 10px;
            margin-bottom: 10px;
            cursor: pointer;
        }
        
        .call-btn {
            background-color: #07c160;
        }
        
        .cancel-btn {
            background-color: #ff976a;
        }
        
        .accept-btn {
            background-color: #07c160;
        }
        
        .reject-btn {
            background-color: #ee0a24;
        }
        
        .send-btn {
            background-color: #1989fa;
        }
        
        .end-call-btn {
            background-color: #ee0a24;
        }
        
        .btn-success {
            background-color: #07c160;
        }
        
        .btn-danger {
            background-color: #ee0a24;
        }
        
        .btn-secondary {
            background-color: #ff976a;
        }
        
        .waiting {
            text-align: center;
        }
        
        .call-buttons {
            display: flex;
            justify-content: center;
            margin-top: 15px;
        }
        
        .call-container {
            display: flex;
            flex-direction: column;
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .audio-controls {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 15px;
            gap: 10px;
        }
        
        .message-list {
            flex: 1;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
            overflow-y: auto;
            max-height: 300px;
        }
        
        .message {
            margin-bottom: 10px;
            padding: 8px;
            background-color: #e6f7ff;
            border-radius: 4px;
        }
        
        .time {
            color: #999;
            font-size: 12px;
            margin-right: 5px;
        }
        
        .sender {
            font-weight: bold;
            margin-right: 5px;
        }
        
        .input-area {
            display: flex;
            gap: 10px;
        }
        
        .message-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        audio {
            display: none;
        }
        
        #remoteAudio {
            width: 100%;
            margin-top: 10px;
        }
        
        #localAudio {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">WebRTC语音通话</div>
            <div class="status" id="connectionStatus">状态: 未连接</div>
        </div>

        <div class="info-box" id="infoBox" style="display: none;">
            <div id="localIdText"></div>
            <div id="remoteIdText" style="display: none;"></div>
            <div id="roomIdText" style="display: none;"></div>
        </div>

        <!-- 呼叫面板 -->
        <div class="panel" id="requestPanel">
            <div id="callRequestForm">
                <input id="targetUserId" placeholder="输入对方ID">
                <button id="callBtn" class="btn call-btn">发起通话</button>
            </div>
            <div id="waitingPanel" class="waiting" style="display: none;">
                <div>等待对方响应...</div>
                <button id="cancelBtn" class="btn cancel-btn">取消</button>
            </div>
        </div>

        <!-- 来电显示 -->
        <div class="incoming-call" id="incomingCall" style="display: none;">
            <div id="incomingCallText"></div>
            <div class="call-buttons">
                <button id="acceptBtn" class="btn accept-btn">接听</button>
                <button id="rejectBtn" class="btn reject-btn">拒绝</button>
            </div>
        </div>

        <!-- 通话中界面 -->
        <div class="call-container" id="callContainer" style="display: none;">
            <!-- 音频控制 -->
            <div class="audio-controls">
                <button id="muteBtn" class="btn btn-success">静音</button>
                <button id="speakerBtn" class="btn btn-success">扬声器开</button>
            </div>

            <!-- 消息列表 -->
            <div class="message-list" id="messageList">
                <!-- 消息将在这里动态添加 -->
            </div>

            <!-- 消息输入 -->
            <div class="input-area">
                <input id="messageInput" placeholder="输入消息..." disabled class="message-input">
                <button id="sendBtn" disabled class="btn send-btn">发送</button>
                <button id="endCallBtn" class="btn end-call-btn">结束通话</button>
            </div>
        </div>

        <!-- 音频元素 -->
        <audio id="remoteAudio" autoplay playsinline></audio>
        <audio id="localAudio" muted playsinline></audio>
        <audio id="ringtone" loop></audio>
    </div>

    <script>
        // 应用状态
        const state = {
            connectionStatus: '未连接',
            localId: null,
            remoteId: null,
            roomId: null,
            ws: null,
            peerConnection: null,
            dataChannel: null,
            messages: [],
            isInCall: false,
            showRequestPanel: true,
            hasSentRequest: false,
            incomingRequest: null,
            isMuted: false,
            isSpeakerOn: true,
            localStream: null,
            remoteStream: null,
            callRequestTimer: null,
			pendingMessages:[]
        };

        // DOM 元素
        const elements = {
            connectionStatus: document.getElementById('connectionStatus'),
            infoBox: document.getElementById('infoBox'),
            localIdText: document.getElementById('localIdText'),
            remoteIdText: document.getElementById('remoteIdText'),
            roomIdText: document.getElementById('roomIdText'),
            requestPanel: document.getElementById('requestPanel'),
            callRequestForm: document.getElementById('callRequestForm'),
            waitingPanel: document.getElementById('waitingPanel'),
            incomingCall: document.getElementById('incomingCall'),
            incomingCallText: document.getElementById('incomingCallText'),
            callContainer: document.getElementById('callContainer'),
            messageList: document.getElementById('messageList'),
            targetUserId: document.getElementById('targetUserId'),
            callBtn: document.getElementById('callBtn'),
            cancelBtn: document.getElementById('cancelBtn'),
            acceptBtn: document.getElementById('acceptBtn'),
            rejectBtn: document.getElementById('rejectBtn'),
            muteBtn: document.getElementById('muteBtn'),
            speakerBtn: document.getElementById('speakerBtn'),
            messageInput: document.getElementById('messageInput'),
            sendBtn: document.getElementById('sendBtn'),
            endCallBtn: document.getElementById('endCallBtn'),
            remoteAudio: document.getElementById('remoteAudio'),
            localAudio: document.getElementById('localAudio'),
            ringtone: document.getElementById('ringtone')
        };

        // 初始化应用
        function initApp() {
			const urlParams = new URLSearchParams(window.location.search);
			const userId = urlParams.get('userId')
            // 生成随机用户ID
            state.localId = userId
            elements.localIdText.textContent = `我的ID: ${state.localId}`;
            elements.infoBox.style.display = 'block';

            // 初始化音频元素
            initAudioElements();

            // 检测设备兼容性
            checkCompatibility();

            // 初始化连接
            initConnection();

            // 绑定事件
            bindEvents();
        }

        // 初始化音频元素
        function initAudioElements() {
            // 设置铃声源
            elements.ringtone.src = 'http://localhost:5173/static/ringtone.mp3';
        }

        // 检测设备兼容性
        function checkCompatibility() {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                alert('您的设备不支持语音通话功能');
                return false;
            }
            return true;
        }

        // 初始化连接
        function initConnection() {
            updateStatus('正在连接信令服务器...');
            initWebSocket();
        }

        // 初始化WebSocket
        function initWebSocket() {
            // 从URL获取token参数
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token') || 'default_token';
            
            state.ws = new WebSocket(`ws://**************:82/api/msgSocket?token=${token}`);

            state.ws.onopen = () => {
                updateStatus('信令服务器已连接');
                console.log('WebSocket连接成功');
            };

            state.ws.onmessage = async (event) => {
                try {
                    console.log(event.data);
                    const message = JSON.parse(event.data);
                    if (message.msg) {
                        console.log('解密前:', message.msg);
                    }
                    await handleServerMessage(message);
                } catch (error) {
                    console.error('消息处理错误:', error);
                }
            };

            state.ws.onerror = (error) => {
                console.error('WebSocket错误:', error);
                updateStatus('连接错误');
            };

            state.ws.onclose = () => {
                if (state.connectionStatus !== '已断开连接') {
                    updateStatus('连接已断开');
                    // 尝试重新连接
                    setTimeout(() => initWebSocket(), 3000);
                }
            };
        }

        // 更新状态显示
        function updateStatus(status) {
            state.connectionStatus = status;
            elements.connectionStatus.textContent = `状态: ${status}`;
        }

        // 处理服务器消息
        async function handleServerMessage(message) {
            switch (message.typecode2) {
                case 9: // 通话请求
                    handleIncomingCall(message);
                    break;
                case 10: // 通话响应
                    handleCallResponse(message);
                    break;
                case 11: // 房间分配
                    await handleRoomAssignment(message);
                    break;
                case 12: // 来电通知
                    handleIncomingCall(message);
                    break;
                case 13: // 通话拒绝
                    handleCallRejected(message);
                    break;
                default:
                    console.warn('未知消息类型:', message);
            }
        }

        // 发送通话请求
        async function sendCallRequest() {
            const targetUserId = elements.targetUserId.value.trim();
            if (!targetUserId) {
                alert('请输入对方ID');
                return;
            }
        
            state.hasSentRequest = true;
            state.remoteId = targetUserId;
            sendToServer({
                typecode: 1,
                typecode2: 9,
                fromid: Number(state.localId),
                toid: Number(targetUserId),
                msg: null
            });
            // 更新UI
            elements.callRequestForm.style.display = 'none';
            elements.waitingPanel.style.display = 'block';
            elements.remoteIdText.textContent = `对方ID: ${state.remoteId}`;
            elements.remoteIdText.style.display = 'block';
        
            // 获取麦克风权限
			try{
				console.log(navigator.mediaDevices.getUserMedia)
			}catch(e){
				console.log('navigator.mediaDevices',e)
				//TODO handle the exception
			}
			if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
			  console.log('您的环境不支持getUserMedia API');
			}
			navigator.mediaDevices.enumerateDevices()
			  .then(devices => {
			    const audioInputs = devices.filter(d => d.kind === 'audioinput');
			    console.log('检测到的音频设备:', audioInputs);
			    
			    if (audioInputs.length === 0) {
			      console.error('系统未识别到任何音频输入设备');
			    } else {
			      audioInputs.forEach(device => {
			        console.log(`设备: ${device.label} (ID: ${device.deviceId})`);
			      });
			    }
			  });
            try {
				console.log(111)
                state.localStream = await navigator.mediaDevices.getUserMedia({
                    audio:  { optional: [{ sourceId: 'default' }] } 
                }).then(res=>{
					console.log(222)
					console.log(res)
				}).catch(handleError);
				
                // 将本地音频流绑定到audio元素（用于测试）
                // elements.localAudio.srcObject = state.localStream;
				console.log(333)
                // 初始化PeerConnection
                // await initWebRTC(true);
        
                // 发送呼叫请求（不在这里创建Offer）
                
        
                // 设置30秒超时
                // state.callRequestTimer = setTimeout(() => {
                //     if (!state.isInCall) {
                //         alert('请求超时');
                //         cancelRequest();
                //     }
                // }, 30000);
        
            } catch (error) {
				for(let key in error){
					console.log(key,error[key],'------')
				}
                console.error('呼叫失败:', JSON.stringify(JSON.parse(error)));
                alert('获取麦克风权限失败');
                cancelRequest();
            }
        }
		function handleError(err) {
		  console.log('错误:', err);
		  console.log(err.name)
		  alert(err.name)
		}
        // 取消请求
        function cancelRequest() {
            state.hasSentRequest = false;
            clearTimeout(state.callRequestTimer);
            updateStatus('已取消请求');
            
            // 更新UI
            elements.callRequestForm.style.display = 'block';
            elements.waitingPanel.style.display = 'none';
            elements.remoteIdText.style.display = 'none';
            
            // 清理资源
            cleanupMedia();
        }

        // 处理来电
        function handleIncomingCall(message) {
            // 如果已经在通话中，自动拒绝新来电
            if (state.isInCall) {
                sendToServer({
                    typecode: 1,
                    typecode2: 13,
                    fromid: Number(state.localId),
                    toid: Number(message.fromid),
                    msg: null
                });
                return;
            }

            state.incomingRequest = {
                from: message.fromid,
                data: message
            };
            state.remoteId = message.fromid;
            
            // 更新UI
            elements.requestPanel.style.display = 'none';
            elements.incomingCallText.textContent = `${message.fromid} 请求通话`;
            elements.incomingCall.style.display = 'block';
            elements.remoteIdText.textContent = `对方ID: ${state.remoteId}`;
            elements.remoteIdText.style.display = 'block';

            // 播放铃声
            playRingtone();
        }

        // 播放铃声
        function playRingtone() {
            elements.ringtone.play().catch(e => {
                console.error('播放铃声失败:', e);
            });
        }

        // 停止铃声
        function stopRingtone() {
            elements.ringtone.pause();
            elements.ringtone.currentTime = 0;
        }

        // 接听来电
        async function acceptCall() {
            stopRingtone();
            state.isInCall = true;
			state.incomingRequest=null
            // 更新UI
            elements.incomingCall.style.display = 'none';
            elements.callContainer.style.display = 'block';
            
            // 获取麦克风权限
            try {
                state.localStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        channelCount: 1
                    },
                    video: false
                });

                // 将本地音频流绑定到audio元素（用于测试）
                elements.localAudio.srcObject = state.localStream;

                // 初始化PeerConnection
                // await initWebRTC(false);

                // 发送接听响应
                sendToServer({
                    typecode: 1,
                    typecode2: 10,
                    fromid: Number(state.localId),
                    toid: Number(state.remoteId),
                    msg: JSON.stringify({ apply: 0 })
                });

            } catch (error) {
                console.error('接听失败:', error);
                alert('接听失败');
                rejectCall();
            }
        }

        // 拒绝来电
        function rejectCall() {
            stopRingtone();
            sendToServer({
                typecode: 1,
                typecode2: 13,
                fromid: Number(state.localId),
                toid: Number(state.remoteId),
                msg: null
            });
            resetCallState();
        }

        // 处理被拒绝
        function handleCallRejected() {
            alert('对方拒绝了通话');
			state.hasSentRequest=false
			clearTimeout(state.callRequestTimer)
            resetCallState();
        }
		
		// 处理WebRTC消息
		async function handleWebRTCMessage(message) {
		    console.log('收到WebRTC消息:', message.type, message);
		    
		    try {
		        switch (message.type) {
		            case 'offer':
		                if (!state.peerConnection) {
		                    await initWebRTC(false);
		                }
		                await handleOffer(message.data);
		                break;
		                
		            case 'answer':
		                await handleAnswer(message.data);
		                break;
		                
		            case 'candidate':
		                await handleCandidate(message.data);
		                break;
		                
		            default:
		                console.warn('未知的WebRTC消息类型:', message.type);
		        }
		    } catch (error) {
		        console.error('处理WebRTC消息失败:', error);
		        updateStatus('消息处理错误');
		        
		        if (error.toString().includes('InvalidStateError')) {
		            setTimeout(() => handleWebRTCMessage(message), 500);
		        }
		    }
		}
		// 处理房间分配
		async function handleRoomAssignment(message) {
		    try {
		        message.msg = JSON.parse(message.msg);
				console.log('分配房间',message.msg)
		        state.roomId = message.msg.room;
		        updateStatus(`已加入房间: ${state.roomId}`);
		        elements.roomIdText.textContent = `房间号: ${state.roomId}`;
		        elements.roomIdText.style.display = 'block';
		
		        if (message.msg.roomMsg) {
		            await handleWebRTCMessage(message.msg.roomMsg);
		        }else{
					await this.initWebRTC()
				}
		    } catch (error) {
		        console.error('房间处理错误:', error);
		    }
		}
		
        // 初始化WebRTC连接
        async function initWebRTC(isCaller) {
			state.isInCall = true
			state.showRequestPanel = false
            const configuration = {
                iceServers: [
                    {
                        urls: [
                            'stun:**************:82',
                            'turn:**************:82?transport=udp'
                        ],
                        username: 'your_username',
                        credential: 'your_username'
                    }
                ],
                iceTransportPolicy: 'all'
            };
        
            try {
                // 1. 创建连接
                state.peerConnection = new RTCPeerConnection(configuration);
        
                // 2. 设置ICE候选处理
                state.peerConnection.onicecandidate = (event) => {
                    if (event.candidate) {
                        sendWebRTCMessage({
                            type: 'candidate',
                            data: event.candidate
                        });
                    }
                };
        
                // 3. 设置连接状态监听
                state.peerConnection.onconnectionstatechange = () => {
                    console.log('连接状态:', state.peerConnection.connectionState);
                    if (state.peerConnection.connectionState === 'disconnected') {
                        handleDisconnect();
                    }
                };
        
                // 4. 添加本地音频轨道
                if (state.localStream) {
                    state.localStream.getTracks().forEach(track => {
                        state.peerConnection.addTrack(track, state.localStream);
                    });
                }
        
                // 5. 设置远程流监听
                state.peerConnection.ontrack = (event) => {
                    if (!state.remoteStream) {
                        state.remoteStream = new MediaStream();
                    }
                    
                    event.streams[0].getTracks().forEach(track => {
                        state.remoteStream.addTrack(track);
                    });
        
                    // 将远程音频流绑定到audio元素
                    elements.remoteAudio.srcObject = state.remoteStream;
                    elements.remoteAudio.play().catch(e => {
                        console.error('远程音频播放失败:', e);
                    });
                };
	
				setupDataChannels();
                // 6. 设置数据通道
                // if (isCaller) {
                //     setupDataChannels();
                // } else {
                //     state.peerConnection.ondatachannel = (event) => {
                //         state.dataChannel = event.channel;
                //         setupDataChannelEvents();
                //     };
                // }
				if (state.hasSentRequest) {
					elements.callContainer.style.display = 'block';
					elements.requestPanel.style.display = 'none';
				  await this.createOffer()
				}else{
					
				}
				
				
                // 注意：这里移除了创建Offer的逻辑，改为在handleRoomAssignment中处理
        
            } catch (error) {
                console.error('WebRTC初始化失败:', error);
                alert('连接建立失败');
                cleanup();
            }
        }

        // 创建Offer
        async function createOffer() {
            try {
                const offer = await state.peerConnection.createOffer({
                    offerToReceiveAudio: true,
                    offerToReceiveVideo: false
                });
                
                await state.peerConnection.setLocalDescription(offer);
                
                sendWebRTCMessage({
                    type: 'offer',
                    data: offer
                });
                
            } catch (error) {
                console.error('创建Offer失败:', error);
                throw error;
            }
        }

        
        
        // 处理Offer
        async function handleOffer(offer) {
            try {
				
                await state.peerConnection.setRemoteDescription(
                    new RTCSessionDescription(offer)
                );
                
                const answer = await state.peerConnection.createAnswer();
                await state.peerConnection.setLocalDescription(answer);
                
                sendWebRTCMessage({
                    type: 'answer',
                    data: answer
                });
                
            } catch (error) {
                console.error('处理Offer失败:', error);
                throw error;
            }
        }

        // 处理Answer
        async function handleAnswer(answer) {
            try {
                if (state.peerConnection.signalingState !== 'have-local-offer') {
                    console.warn('非预期状态:', state.peerConnection.signalingState);
                    setTimeout(() => handleAnswer(answer), 500);
                    return;
                }
                
                await state.peerConnection.setRemoteDescription(answer);
                console.log('Answer设置成功');
                
            } catch (error) {
                console.error('处理Answer失败:', error);
                throw error;
            }
        }

        // 处理ICE候选
        async function handleCandidate(candidate) {
            try {
				if (!state.peerConnection.remoteDescription) {
				  setTimeout(() => handleCandidate(candidate), 100)
				  return
				}
				
                if (state.peerConnection && candidate) {
                    await state.peerConnection.addIceCandidate(
                        new RTCIceCandidate(candidate)
                    );
                }
            } catch (error) {
                console.error('添加候选失败:', error);
            }
        }

        // 设置数据通道
        function setupDataChannels() {
			if (state.hasSentRequest) {
				state.dataChannel = state.peerConnection.createDataChannel('chat', {
					ordered: true,
					maxPacketLifeTime: 3000
				});
				setupDataChannelEvents();
            }
            // 监听数据通道（如果是接收方）
            state.peerConnection.ondatachannel = (event) => {
				state.dataChannel = event.channel
				setupDataChannelEvents()
            }
        }

        // 设置数据通道事件
        function setupDataChannelEvents() {
            state.dataChannel.onopen = () => {
                console.log('数据通道已打开');
                updateStatus('通话已连接');
                elements.sendBtn.disabled = false;
                elements.messageInput.disabled = false;
				flushPendingMessages()
            };
            
            state.dataChannel.onclose = () => {
                console.log('数据通道已关闭');
                handleDisconnect();
            };
            
            state.dataChannel.onmessage = (event) => {
                addMessage(event.data, state.remoteId);
            };
        }
		// 发送待处理消息
		function flushPendingMessages() {
			let isDataChannelReady= state.dataChannel && state.dataChannel.readyState === 'open'
		  while (state.pendingMessages.length > 0 && isDataChannelReady) {
		    const msg = state.pendingMessages.shift()
		    state.dataChannel.send(msg)
		    state.addMessage(msg, '我')
		  }
		}
        // 发送消息
        function sendMessage() {
            const message = elements.messageInput.value.trim();
            if (!message) return;

            if (state.dataChannel && state.dataChannel.readyState === 'open') {
                try {
                    state.dataChannel.send(message);
                    addMessage(message, '我');
                    elements.messageInput.value = '';
                } catch (error) {
                    console.error('发送失败:', error);
                }
            }else{
				state.pendingMessages.push(message)
			}
        }

        // 添加消息到聊天
        function addMessage(content, sender) {
            const messageElement = document.createElement('div');
            messageElement.className = 'message';
            
            const timeElement = document.createElement('span');
            timeElement.className = 'time';
            timeElement.textContent = `[${new Date().toLocaleTimeString()}] `;
            
            const senderElement = document.createElement('span');
            senderElement.className = 'sender';
            senderElement.textContent = `${sender}: `;
            
            const contentElement = document.createElement('span');
            contentElement.className = 'content';
            contentElement.textContent = content;
            
            messageElement.appendChild(timeElement);
            messageElement.appendChild(senderElement);
            messageElement.appendChild(contentElement);
            
            elements.messageList.appendChild(messageElement);
            elements.messageList.scrollTop = elements.messageList.scrollHeight;
        }

        // 发送WebRTC消息
        function sendWebRTCMessage(message) {
            sendToServer({
                typecode: 1,
                typecode2: 11,
                fromid: Number(state.localId),
                toid: Number(state.remoteId),
                msg: JSON.stringify({
                    room: state.roomId,
                    roomMsg: message
                })
            });
        }

        // 发送普通消息
        function sendToServer(message) {
            if (state.ws && state.ws.readyState === WebSocket.OPEN) {
                state.ws.send(JSON.stringify(message));
            }
        }

        // 处理断开连接
        function handleDisconnect() {
            alert('连接已断开');
            cleanup();
            resetCallState();
        }

        // 结束通话
        function endCall() {
            sendToServer({
                typecode: 1,
                typecode2: 13,
                fromid: Number(state.localId),
                toid: Number(state.remoteId),
                msg: null
            });
            
            cleanup();
            resetCallState();
            alert('通话已结束');
        }

        // 清理资源
        function cleanup() {
            // 停止媒体流
            if (state.localStream) {
                state.localStream.getTracks().forEach(track => track.stop());
                state.localStream = null;
            }
            
            if (state.remoteStream) {
                state.remoteStream.getTracks().forEach(track => track.stop());
                state.remoteStream = null;
            }
            
            // 清理音频元素
            elements.remoteAudio.srcObject = null;
            elements.localAudio.srcObject = null;
            elements.ringtone.pause();
            elements.ringtone.currentTime = 0;
            
            // 关闭 PeerConnection
            if (state.peerConnection) {
                state.peerConnection.close();
                state.peerConnection = null;
            }
            
            // 关闭数据通道
            if (state.dataChannel) {
                state.dataChannel.close();
                state.dataChannel = null;
            }
            
            // 清理定时器
            clearTimeout(state.callRequestTimer);
        }

        // 清理媒体资源
        function cleanupMedia() {
            if (state.localStream) {
                state.localStream.getTracks().forEach(track => track.stop());
                state.localStream = null;
            }
        }

        // 重置通话状态
        function resetCallState() {
            state.isInCall = false;
            state.hasSentRequest = false;
            state.incomingRequest = null;
            state.remoteId = null;
            state.roomId = null;
            
            // 更新UI
            elements.requestPanel.style.display = 'block';
            elements.callRequestForm.style.display = 'block';
            elements.waitingPanel.style.display = 'none';
            elements.incomingCall.style.display = 'none';
            elements.callContainer.style.display = 'none';
            elements.remoteIdText.style.display = 'none';
            elements.roomIdText.style.display = 'none';
            elements.messageList.innerHTML = '';
            elements.messageInput.value = '';
            elements.messageInput.disabled = true;
            elements.sendBtn.disabled = true;
			state.pendingMessages = []
            
            updateStatus('准备就绪');
        }

        // 静音切换
        function toggleMute() {
            state.isMuted = !state.isMuted;
            
            if (state.localStream) {
                state.localStream.getAudioTracks().forEach(track => {
                    track.enabled = !state.isMuted;
                });
            }
            
            elements.muteBtn.textContent = state.isMuted ? '取消静音' : '静音';
            elements.muteBtn.className = state.isMuted ? 'btn btn-danger' : 'btn btn-success';
            
            alert(state.isMuted ? '已静音' : '已取消静音');
        }

        // 扬声器切换
        function toggleSpeaker() {
            state.isSpeakerOn = !state.isSpeakerOn;
            
            // 在实际应用中，这里需要调用平台特定的API来控制扬声器
            // 这里只是一个UI切换示例
            elements.speakerBtn.textContent = state.isSpeakerOn ? '扬声器开' : '扬声器关';
            elements.speakerBtn.className = state.isSpeakerOn ? 'btn btn-success' : 'btn btn-secondary';
            
            alert(state.isSpeakerOn ? '扬声器已开启' : '扬声器已关闭');
        }

        // 绑定事件
        function bindEvents() {
            elements.callBtn.addEventListener('click', sendCallRequest);
            elements.cancelBtn.addEventListener('click', cancelRequest);
            elements.acceptBtn.addEventListener('click', acceptCall);
            elements.rejectBtn.addEventListener('click', rejectCall);
            elements.muteBtn.addEventListener('click', toggleMute);
            elements.speakerBtn.addEventListener('click', toggleSpeaker);
            elements.sendBtn.addEventListener('click', sendMessage);
            elements.endCallBtn.addEventListener('click', endCall);
            
            elements.messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        }

        // 初始化应用
        window.onload = initApp;
    </script>
</body>
</html>