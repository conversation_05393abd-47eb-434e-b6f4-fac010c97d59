{"requires": true, "lockfileVersion": 1, "dependencies": {"@dcloudio/uni-components": {"version": "3.0.0-alpha-3000020210521001", "resolved": "https://registry.npmmirror.com/@dcloudio/uni-components/-/uni-components-3.0.0-alpha-3000020210521001.tgz", "integrity": "sha512-FxCzOPgOqE5V9owpogIZc0UvvfSlrzL77YVoKFWXB6BRJwAzc2bEpzA0+vUA+2V+RajWN6HrK9/czKjsArTS1A=="}, "@dcloudio/uni-ui": {"version": "1.5.7", "resolved": false, "integrity": "sha512-DugxSIrQrze1FLdUOj9a+JEQ0bHGjnJTcGUK1mN/MivKg7nuKJBRWk5Ipa9sUdoBznX6ndz5h2e7Uao6x1CdCw=="}, "@types/json-schema": {"version": "7.0.15", "resolved": "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.15.tgz", "integrity": "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==", "dev": true}, "@types/webrtc": {"version": "0.0.46", "resolved": "https://registry.npmmirror.com/@types/webrtc/-/webrtc-0.0.46.tgz", "integrity": "sha512-cvCnjKy0ma9ODWbVYBTMMB+WkocwLcscCwn2/caDVdb9MWaesS8PYGapIIHFsAaIBXRFlH1Fc7ZjIBO6pH0HKA=="}, "ajv": {"version": "6.12.6", "resolved": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ajv-keywords": {"version": "3.5.2", "resolved": "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "integrity": "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==", "dev": true}, "anymatch": {"version": "3.1.3", "resolved": "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz", "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==", "dev": true, "requires": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}}, "big.js": {"version": "5.2.2", "resolved": "https://registry.npmmirror.com/big.js/-/big.js-5.2.2.tgz", "integrity": "sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==", "dev": true}, "binary-extensions": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.3.0.tgz", "integrity": "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==", "dev": true}, "braces": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/braces/-/braces-3.0.3.tgz", "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "dev": true, "requires": {"fill-range": "^7.1.1"}}, "chokidar": {"version": "3.6.0", "resolved": "https://registry.npmmirror.com/chokidar/-/chokidar-3.6.0.tgz", "integrity": "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==", "dev": true, "requires": {"anymatch": "~3.1.2", "braces": "~3.0.2", "fsevents": "~2.3.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}}, "clipboard": {"version": "2.0.11", "resolved": false, "integrity": "sha512-C+0bbOqkezLIsmWSvlsXS0Q0bmkugu7jcfMIACB+RDEntIzQIkdr148we28AfSloQLRdZlYL/QYyrq05j/3Faw==", "requires": {"good-listener": "^1.2.2", "select": "^1.1.2", "tiny-emitter": "^2.0.0"}}, "color-name": {"version": "1.1.4", "resolved": "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="}, "crypto-js": {"version": "4.2.0", "resolved": "https://registry.npmmirror.com/crypto-js/-/crypto-js-4.2.0.tgz", "integrity": "sha512-KALDyEYgpY+Rlob/iriUtjV6d5Eq+Y191A5g4UqLAi8CyGP9N1+FdVbkc1SxKc2r4YAYqG8JzO2KGL+AizD70Q=="}, "dayjs": {"version": "1.11.13", "resolved": false, "integrity": "sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg=="}, "delegate": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/delegate/-/delegate-3.2.0.tgz", "integrity": "sha512-IofjkYBZaZivn0V8nnsMJGBr4jVLxHDheKSW88PyxS5QC4Vo9ZbZVvhzlSxY87fVq3STR6r+4cGepyHkcWOQSw=="}, "emojis-list": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/emojis-list/-/emojis-list-3.0.0.tgz", "integrity": "sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==", "dev": true}, "fast-deep-equal": {"version": "3.1.3", "resolved": "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==", "dev": true}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==", "dev": true}, "fill-range": {"version": "7.1.1", "resolved": "https://registry.npmmirror.com/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "dev": true, "requires": {"to-regex-range": "^5.0.1"}}, "fsevents": {"version": "2.3.3", "resolved": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "dev": true, "optional": true}, "glob-parent": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "dev": true, "requires": {"is-glob": "^4.0.1"}}, "good-listener": {"version": "1.2.2", "resolved": "https://registry.npmmirror.com/good-listener/-/good-listener-1.2.2.tgz", "integrity": "sha512-goW1b+d9q/HIwbVYZzZ6SsTr4IgE+WA44A0GmPIQstuOrgsFcT7VEJ48nmr9GaRtNu0XTKacFLGnBPAM6Afouw==", "requires": {"delegate": "^3.1.2"}}, "immutable": {"version": "4.3.7", "resolved": "https://registry.npmmirror.com/immutable/-/immutable-4.3.7.tgz", "integrity": "sha512-1hqclzwYwjRDFLjcFxOM5AYkkG0rpFPpr1RLPMEuGczoS7YA8gLhy8SWXYRAA/XwfEHpfo3cw5JGioS32fnMRw==", "dev": true}, "install": {"version": "0.13.0", "resolved": "https://registry.npmmirror.com/install/-/install-0.13.0.tgz", "integrity": "sha512-zDml/jzr2PKU9I8J/xyZBQn8rPCAY//UOYNmR01XwNwyfhEWObo2SWfSl1+0tm1u6PhxLwDnfsT/6jB7OUxqFA=="}, "is-binary-path": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz", "integrity": "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==", "dev": true, "requires": {"binary-extensions": "^2.0.0"}}, "is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==", "dev": true}, "is-glob": {"version": "4.0.3", "resolved": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-number": {"version": "7.0.0", "resolved": "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "dev": true}, "json-schema-traverse": {"version": "0.4.1", "resolved": "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==", "dev": true}, "json5": {"version": "2.2.3", "resolved": "https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "dev": true}, "klona": {"version": "2.0.6", "resolved": "https://registry.npmmirror.com/klona/-/klona-2.0.6.tgz", "integrity": "sha512-dhG34DXATL5hSxJbIexCft8FChFXtmskoZYnoPWjXQuebWYCNkVeV3KkGegCK9CP1oswI/vQibS2GY7Em/sJJA==", "dev": true}, "loader-utils": {"version": "2.0.4", "resolved": "https://registry.npmmirror.com/loader-utils/-/loader-utils-2.0.4.tgz", "integrity": "sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==", "dev": true, "requires": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}}, "neo-async": {"version": "2.6.2", "resolved": "https://registry.npmmirror.com/neo-async/-/neo-async-2.6.2.tgz", "integrity": "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==", "dev": true}, "nim-web-sdk-ng": {"version": "10.9.0", "resolved": "https://registry.npmmirror.com/nim-web-sdk-ng/-/nim-web-sdk-ng-10.9.0.tgz", "integrity": "sha512-ciIVpuGxXYF5Itf7qKavSnpCR1k2T99WFxri1odi0quj7+0wdsHKSaAhtQCvIE9j7mkcg3VDu/oHaJocOWcYGw=="}, "normalize-path": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==", "dev": true}, "npm": {"version": "11.3.0", "resolved": "https://registry.npmmirror.com/npm/-/npm-11.3.0.tgz", "integrity": "sha512-luthFIP0nFX3+nTfYbWI3p4hP4CiVnKOZ5jdxnF2x7B+Shz8feiSJCLLzgJUNxQ2cDdTaVUiH6RRsMT++vIMZg==", "requires": {"@isaacs/string-locale-compare": "^1.1.0", "@npmcli/arborist": "^9.0.2", "@npmcli/config": "^10.2.0", "@npmcli/fs": "^4.0.0", "@npmcli/map-workspaces": "^4.0.2", "@npmcli/package-json": "^6.1.1", "@npmcli/promise-spawn": "^8.0.2", "@npmcli/redact": "^3.1.1", "@npmcli/run-script": "^9.1.0", "@sigstore/tuf": "^3.0.0", "abbrev": "^3.0.0", "archy": "~1.0.0", "cacache": "^19.0.1", "chalk": "^5.4.1", "ci-info": "^4.2.0", "cli-columns": "^4.0.0", "fastest-levenshtein": "^1.0.16", "fs-minipass": "^3.0.3", "glob": "^10.4.5", "graceful-fs": "^4.2.11", "hosted-git-info": "^8.0.2", "ini": "^5.0.0", "init-package-json": "^8.0.0", "is-cidr": "^5.1.1", "json-parse-even-better-errors": "^4.0.0", "libnpmaccess": "^10.0.0", "libnpmdiff": "^8.0.2", "libnpmexec": "^10.1.1", "libnpmfund": "^7.0.2", "libnpmorg": "^8.0.0", "libnpmpack": "^9.0.2", "libnpmpublish": "^11.0.0", "libnpmsearch": "^9.0.0", "libnpmteam": "^8.0.0", "libnpmversion": "^8.0.0", "make-fetch-happen": "^14.0.3", "minimatch": "^9.0.5", "minipass": "^7.1.1", "minipass-pipeline": "^1.2.4", "ms": "^2.1.2", "node-gyp": "^11.2.0", "nopt": "^8.1.0", "normalize-package-data": "^7.0.0", "npm-audit-report": "^6.0.0", "npm-install-checks": "^7.1.1", "npm-package-arg": "^12.0.2", "npm-pick-manifest": "^10.0.0", "npm-profile": "^11.0.1", "npm-registry-fetch": "^18.0.2", "npm-user-validate": "^3.0.0", "p-map": "^7.0.3", "pacote": "^21.0.0", "parse-conflict-json": "^4.0.0", "proc-log": "^5.0.0", "qrcode-terminal": "^0.12.0", "read": "^4.1.0", "semver": "^7.7.1", "spdx-expression-parse": "^4.0.0", "ssri": "^12.0.0", "supports-color": "^10.0.0", "tar": "^6.2.1", "text-table": "~0.2.0", "tiny-relative-date": "^1.3.0", "treeverse": "^3.0.0", "validate-npm-package-name": "^6.0.0", "which": "^5.0.0"}, "dependencies": {"@isaacs/cliui": {"version": "8.0.2", "bundled": true, "requires": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "dependencies": {"ansi-regex": {"version": "6.1.0", "bundled": true}, "ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "requires": {"color-convert": "^2.0.1"}, "dependencies": {"color-convert": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "requires": {"color-name": "~1.1.4"}}}}, "emoji-regex": {"version": "9.2.2", "bundled": true}, "string-width": {"version": "5.1.2", "bundled": true, "requires": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}}, "string-width-cjs": {"version": "npm:string-width@4.2.3", "resolved": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="}, "emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="}, "strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "requires": {"ansi-regex": "^5.0.1"}}}}, "strip-ansi": {"version": "7.1.0", "bundled": true, "requires": {"ansi-regex": "^6.0.1"}}, "strip-ansi-cjs": {"version": "npm:strip-ansi@6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "requires": {"ansi-regex": "^5.0.1"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="}}}, "wrap-ansi-cjs": {"version": "npm:wrap-ansi@7.0.0", "resolved": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ=="}, "emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="}, "string-width": {"version": "4.2.3", "resolved": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "requires": {"ansi-regex": "^5.0.1"}}}}}}, "@isaacs/fs-minipass": {"version": "4.0.1", "bundled": true, "requires": {"minipass": "^7.0.4"}}, "@isaacs/string-locale-compare": {"version": "1.1.0", "bundled": true}, "@npmcli/agent": {"version": "3.0.0", "bundled": true, "requires": {"agent-base": "^7.1.0", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.1", "lru-cache": "^10.0.1", "socks-proxy-agent": "^8.0.3"}}, "@npmcli/arborist": {"version": "9.0.2", "bundled": true, "requires": {"@isaacs/string-locale-compare": "^1.1.0", "@npmcli/fs": "^4.0.0", "@npmcli/installed-package-contents": "^3.0.0", "@npmcli/map-workspaces": "^4.0.1", "@npmcli/metavuln-calculator": "^9.0.0", "@npmcli/name-from-folder": "^3.0.0", "@npmcli/node-gyp": "^4.0.0", "@npmcli/package-json": "^6.0.1", "@npmcli/query": "^4.0.0", "@npmcli/redact": "^3.0.0", "@npmcli/run-script": "^9.0.1", "bin-links": "^5.0.0", "cacache": "^19.0.1", "common-ancestor-path": "^1.0.1", "hosted-git-info": "^8.0.0", "json-stringify-nice": "^1.1.4", "lru-cache": "^10.2.2", "minimatch": "^9.0.4", "nopt": "^8.0.0", "npm-install-checks": "^7.1.0", "npm-package-arg": "^12.0.0", "npm-pick-manifest": "^10.0.0", "npm-registry-fetch": "^18.0.1", "pacote": "^21.0.0", "parse-conflict-json": "^4.0.0", "proc-log": "^5.0.0", "proggy": "^3.0.0", "promise-all-reject-late": "^1.0.0", "promise-call-limit": "^3.0.1", "read-package-json-fast": "^4.0.0", "semver": "^7.3.7", "ssri": "^12.0.0", "treeverse": "^3.0.0", "walk-up-path": "^4.0.0"}}, "@npmcli/config": {"version": "10.2.0", "bundled": true, "requires": {"@npmcli/map-workspaces": "^4.0.1", "@npmcli/package-json": "^6.0.1", "ci-info": "^4.0.0", "ini": "^5.0.0", "nopt": "^8.1.0", "proc-log": "^5.0.0", "semver": "^7.3.5", "walk-up-path": "^4.0.0"}}, "@npmcli/fs": {"version": "4.0.0", "bundled": true, "requires": {"semver": "^7.3.5"}}, "@npmcli/git": {"version": "6.0.3", "bundled": true, "requires": {"@npmcli/promise-spawn": "^8.0.0", "ini": "^5.0.0", "lru-cache": "^10.0.1", "npm-pick-manifest": "^10.0.0", "proc-log": "^5.0.0", "promise-retry": "^2.0.1", "semver": "^7.3.5", "which": "^5.0.0"}}, "@npmcli/installed-package-contents": {"version": "3.0.0", "bundled": true, "requires": {"npm-bundled": "^4.0.0", "npm-normalize-package-bin": "^4.0.0"}}, "@npmcli/map-workspaces": {"version": "4.0.2", "bundled": true, "requires": {"@npmcli/name-from-folder": "^3.0.0", "@npmcli/package-json": "^6.0.0", "glob": "^10.2.2", "minimatch": "^9.0.0"}}, "@npmcli/metavuln-calculator": {"version": "9.0.0", "bundled": true, "requires": {"cacache": "^19.0.0", "json-parse-even-better-errors": "^4.0.0", "pacote": "^21.0.0", "proc-log": "^5.0.0", "semver": "^7.3.5"}}, "@npmcli/name-from-folder": {"version": "3.0.0", "bundled": true}, "@npmcli/node-gyp": {"version": "4.0.0", "bundled": true}, "@npmcli/package-json": {"version": "6.1.1", "bundled": true, "requires": {"@npmcli/git": "^6.0.0", "glob": "^10.2.2", "hosted-git-info": "^8.0.0", "json-parse-even-better-errors": "^4.0.0", "proc-log": "^5.0.0", "semver": "^7.5.3", "validate-npm-package-license": "^3.0.4"}}, "@npmcli/promise-spawn": {"version": "8.0.2", "bundled": true, "requires": {"which": "^5.0.0"}}, "@npmcli/query": {"version": "4.0.0", "bundled": true, "requires": {"postcss-selector-parser": "^6.1.2"}}, "@npmcli/redact": {"version": "3.1.1", "bundled": true}, "@npmcli/run-script": {"version": "9.1.0", "bundled": true, "requires": {"@npmcli/node-gyp": "^4.0.0", "@npmcli/package-json": "^6.0.0", "@npmcli/promise-spawn": "^8.0.0", "node-gyp": "^11.0.0", "proc-log": "^5.0.0", "which": "^5.0.0"}}, "@pkgjs/parseargs": {"version": "0.11.0", "bundled": true, "optional": true}, "@sigstore/bundle": {"version": "3.1.0", "bundled": true, "requires": {"@sigstore/protobuf-specs": "^0.4.0"}}, "@sigstore/core": {"version": "2.0.0", "bundled": true}, "@sigstore/protobuf-specs": {"version": "0.4.0", "bundled": true}, "@sigstore/sign": {"version": "3.1.0", "bundled": true, "requires": {"@sigstore/bundle": "^3.1.0", "@sigstore/core": "^2.0.0", "@sigstore/protobuf-specs": "^0.4.0", "make-fetch-happen": "^14.0.2", "proc-log": "^5.0.0", "promise-retry": "^2.0.1"}}, "@sigstore/tuf": {"version": "3.1.0", "bundled": true, "requires": {"@sigstore/protobuf-specs": "^0.4.0", "tuf-js": "^3.0.1"}}, "@sigstore/verify": {"version": "2.1.0", "bundled": true, "requires": {"@sigstore/bundle": "^3.1.0", "@sigstore/core": "^2.0.0", "@sigstore/protobuf-specs": "^0.4.0"}}, "@tufjs/canonical-json": {"version": "2.0.0", "bundled": true}, "@tufjs/models": {"version": "3.0.1", "bundled": true, "requires": {"@tufjs/canonical-json": "2.0.0", "minimatch": "^9.0.5"}}, "abbrev": {"version": "3.0.0", "bundled": true}, "agent-base": {"version": "7.1.3", "bundled": true}, "ansi-regex": {"version": "5.0.1", "bundled": true}, "ansi-styles": {"version": "6.2.1", "bundled": true}, "aproba": {"version": "2.0.0", "bundled": true}, "archy": {"version": "1.0.0", "bundled": true}, "balanced-match": {"version": "1.0.2", "bundled": true}, "bin-links": {"version": "5.0.0", "bundled": true, "requires": {"cmd-shim": "^7.0.0", "npm-normalize-package-bin": "^4.0.0", "proc-log": "^5.0.0", "read-cmd-shim": "^5.0.0", "write-file-atomic": "^6.0.0"}}, "binary-extensions": {"version": "3.0.0", "bundled": true}, "brace-expansion": {"version": "2.0.1", "bundled": true, "requires": {"balanced-match": "^1.0.0"}}, "cacache": {"version": "19.0.1", "bundled": true, "requires": {"@npmcli/fs": "^4.0.0", "fs-minipass": "^3.0.0", "glob": "^10.2.2", "lru-cache": "^10.0.1", "minipass": "^7.0.3", "minipass-collect": "^2.0.1", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "p-map": "^7.0.2", "ssri": "^12.0.0", "tar": "^7.4.3", "unique-filename": "^4.0.0"}, "dependencies": {"chownr": {"version": "3.0.0", "bundled": true}, "minizlib": {"version": "3.0.2", "bundled": true, "requires": {"minipass": "^7.1.2"}}, "mkdirp": {"version": "3.0.1", "bundled": true}, "tar": {"version": "7.4.3", "bundled": true, "requires": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "mkdirp": "^3.0.1", "yallist": "^5.0.0"}}, "yallist": {"version": "5.0.0", "bundled": true}}}, "chalk": {"version": "5.4.1", "bundled": true}, "chownr": {"version": "2.0.0", "bundled": true}, "ci-info": {"version": "4.2.0", "bundled": true}, "cidr-regex": {"version": "4.1.3", "bundled": true, "requires": {"ip-regex": "^5.0.0"}}, "cli-columns": {"version": "4.0.0", "bundled": true, "requires": {"string-width": "^4.2.3", "strip-ansi": "^6.0.1"}}, "cmd-shim": {"version": "7.0.0", "bundled": true}, "common-ancestor-path": {"version": "1.0.1", "bundled": true}, "cross-spawn": {"version": "7.0.6", "bundled": true, "requires": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "dependencies": {"which": {"version": "2.0.2", "bundled": true, "requires": {"isexe": "^2.0.0"}}}}, "cssesc": {"version": "3.0.0", "bundled": true}, "debug": {"version": "4.4.0", "bundled": true, "requires": {"ms": "^2.1.3"}}, "diff": {"version": "7.0.0", "bundled": true}, "eastasianwidth": {"version": "0.2.0", "bundled": true}, "emoji-regex": {"version": "8.0.0", "bundled": true}, "encoding": {"version": "0.1.13", "bundled": true, "optional": true, "requires": {"iconv-lite": "^0.6.2"}}, "env-paths": {"version": "2.2.1", "bundled": true}, "err-code": {"version": "2.0.3", "bundled": true}, "exponential-backoff": {"version": "3.1.2", "bundled": true}, "fastest-levenshtein": {"version": "1.0.16", "bundled": true}, "foreground-child": {"version": "3.3.1", "bundled": true, "requires": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}}, "fs-minipass": {"version": "3.0.3", "bundled": true, "requires": {"minipass": "^7.0.3"}}, "glob": {"version": "10.4.5", "bundled": true, "requires": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}}, "graceful-fs": {"version": "4.2.11", "bundled": true}, "hosted-git-info": {"version": "8.0.2", "bundled": true, "requires": {"lru-cache": "^10.0.1"}}, "http-cache-semantics": {"version": "4.1.1", "bundled": true}, "http-proxy-agent": {"version": "7.0.2", "bundled": true, "requires": {"agent-base": "^7.1.0", "debug": "^4.3.4"}}, "https-proxy-agent": {"version": "7.0.6", "bundled": true, "requires": {"agent-base": "^7.1.2", "debug": "4"}}, "iconv-lite": {"version": "0.6.3", "bundled": true, "optional": true, "requires": {"safer-buffer": ">= 2.1.2 < 3.0.0"}}, "ignore-walk": {"version": "7.0.0", "bundled": true, "requires": {"minimatch": "^9.0.0"}}, "imurmurhash": {"version": "0.1.4", "bundled": true}, "ini": {"version": "5.0.0", "bundled": true}, "init-package-json": {"version": "8.0.0", "bundled": true, "requires": {"@npmcli/package-json": "^6.1.0", "npm-package-arg": "^12.0.0", "promzard": "^2.0.0", "read": "^4.0.0", "semver": "^7.3.5", "validate-npm-package-license": "^3.0.4", "validate-npm-package-name": "^6.0.0"}}, "ip-address": {"version": "9.0.5", "bundled": true, "requires": {"jsbn": "1.1.0", "sprintf-js": "^1.1.3"}}, "ip-regex": {"version": "5.0.0", "bundled": true}, "is-cidr": {"version": "5.1.1", "bundled": true, "requires": {"cidr-regex": "^4.1.1"}}, "is-fullwidth-code-point": {"version": "3.0.0", "bundled": true}, "isexe": {"version": "2.0.0", "bundled": true}, "jackspeak": {"version": "3.4.3", "bundled": true, "requires": {"@isaacs/cliui": "^8.0.2", "@pkgjs/parseargs": "^0.11.0"}}, "jsbn": {"version": "1.1.0", "bundled": true}, "json-parse-even-better-errors": {"version": "4.0.0", "bundled": true}, "json-stringify-nice": {"version": "1.1.4", "bundled": true}, "jsonparse": {"version": "1.3.1", "bundled": true}, "just-diff": {"version": "6.0.2", "bundled": true}, "just-diff-apply": {"version": "5.5.0", "bundled": true}, "libnpmaccess": {"version": "10.0.0", "bundled": true, "requires": {"npm-package-arg": "^12.0.0", "npm-registry-fetch": "^18.0.1"}}, "libnpmdiff": {"version": "8.0.2", "bundled": true, "requires": {"@npmcli/arborist": "^9.0.2", "@npmcli/installed-package-contents": "^3.0.0", "binary-extensions": "^3.0.0", "diff": "^7.0.0", "minimatch": "^9.0.4", "npm-package-arg": "^12.0.0", "pacote": "^21.0.0", "tar": "^6.2.1"}}, "libnpmexec": {"version": "10.1.1", "bundled": true, "requires": {"@npmcli/arborist": "^9.0.2", "@npmcli/package-json": "^6.1.1", "@npmcli/run-script": "^9.0.1", "ci-info": "^4.0.0", "npm-package-arg": "^12.0.0", "pacote": "^21.0.0", "proc-log": "^5.0.0", "read": "^4.0.0", "read-package-json-fast": "^4.0.0", "semver": "^7.3.7", "walk-up-path": "^4.0.0"}}, "libnpmfund": {"version": "7.0.2", "bundled": true, "requires": {"@npmcli/arborist": "^9.0.2"}}, "libnpmorg": {"version": "8.0.0", "bundled": true, "requires": {"aproba": "^2.0.0", "npm-registry-fetch": "^18.0.1"}}, "libnpmpack": {"version": "9.0.2", "bundled": true, "requires": {"@npmcli/arborist": "^9.0.2", "@npmcli/run-script": "^9.0.1", "npm-package-arg": "^12.0.0", "pacote": "^21.0.0"}}, "libnpmpublish": {"version": "11.0.0", "bundled": true, "requires": {"ci-info": "^4.0.0", "normalize-package-data": "^7.0.0", "npm-package-arg": "^12.0.0", "npm-registry-fetch": "^18.0.1", "proc-log": "^5.0.0", "semver": "^7.3.7", "sigstore": "^3.0.0", "ssri": "^12.0.0"}}, "libnpmsearch": {"version": "9.0.0", "bundled": true, "requires": {"npm-registry-fetch": "^18.0.1"}}, "libnpmteam": {"version": "8.0.0", "bundled": true, "requires": {"aproba": "^2.0.0", "npm-registry-fetch": "^18.0.1"}}, "libnpmversion": {"version": "8.0.0", "bundled": true, "requires": {"@npmcli/git": "^6.0.1", "@npmcli/run-script": "^9.0.1", "json-parse-even-better-errors": "^4.0.0", "proc-log": "^5.0.0", "semver": "^7.3.7"}}, "lru-cache": {"version": "10.4.3", "bundled": true}, "make-fetch-happen": {"version": "14.0.3", "bundled": true, "requires": {"@npmcli/agent": "^3.0.0", "cacache": "^19.0.1", "http-cache-semantics": "^4.1.1", "minipass": "^7.0.2", "minipass-fetch": "^4.0.0", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^1.0.0", "proc-log": "^5.0.0", "promise-retry": "^2.0.1", "ssri": "^12.0.0"}, "dependencies": {"negotiator": {"version": "1.0.0", "bundled": true}}}, "minimatch": {"version": "9.0.5", "bundled": true, "requires": {"brace-expansion": "^2.0.1"}}, "minipass": {"version": "7.1.2", "bundled": true}, "minipass-collect": {"version": "2.0.1", "bundled": true, "requires": {"minipass": "^7.0.3"}}, "minipass-fetch": {"version": "4.0.1", "bundled": true, "requires": {"encoding": "^0.1.13", "minipass": "^7.0.3", "minipass-sized": "^1.0.3", "minizlib": "^3.0.1"}, "dependencies": {"minizlib": {"version": "3.0.2", "bundled": true, "requires": {"minipass": "^7.1.2"}}}}, "minipass-flush": {"version": "1.0.5", "bundled": true, "requires": {"minipass": "^3.0.0"}, "dependencies": {"minipass": {"version": "3.3.6", "bundled": true, "requires": {"yallist": "^4.0.0"}}}}, "minipass-pipeline": {"version": "1.2.4", "bundled": true, "requires": {"minipass": "^3.0.0"}, "dependencies": {"minipass": {"version": "3.3.6", "bundled": true, "requires": {"yallist": "^4.0.0"}}}}, "minipass-sized": {"version": "1.0.3", "bundled": true, "requires": {"minipass": "^3.0.0"}, "dependencies": {"minipass": {"version": "3.3.6", "bundled": true, "requires": {"yallist": "^4.0.0"}}}}, "minizlib": {"version": "2.1.2", "bundled": true, "requires": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "dependencies": {"minipass": {"version": "3.3.6", "bundled": true, "requires": {"yallist": "^4.0.0"}}}}, "mkdirp": {"version": "1.0.4", "bundled": true}, "ms": {"version": "2.1.3", "bundled": true}, "mute-stream": {"version": "2.0.0", "bundled": true}, "node-gyp": {"version": "11.2.0", "bundled": true, "requires": {"env-paths": "^2.2.0", "exponential-backoff": "^3.1.1", "graceful-fs": "^4.2.6", "make-fetch-happen": "^14.0.3", "nopt": "^8.0.0", "proc-log": "^5.0.0", "semver": "^7.3.5", "tar": "^7.4.3", "tinyglobby": "^0.2.12", "which": "^5.0.0"}, "dependencies": {"chownr": {"version": "3.0.0", "bundled": true}, "minizlib": {"version": "3.0.2", "bundled": true, "requires": {"minipass": "^7.1.2"}}, "mkdirp": {"version": "3.0.1", "bundled": true}, "tar": {"version": "7.4.3", "bundled": true, "requires": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "mkdirp": "^3.0.1", "yallist": "^5.0.0"}}, "yallist": {"version": "5.0.0", "bundled": true}}}, "nopt": {"version": "8.1.0", "bundled": true, "requires": {"abbrev": "^3.0.0"}}, "normalize-package-data": {"version": "7.0.0", "bundled": true, "requires": {"hosted-git-info": "^8.0.0", "semver": "^7.3.5", "validate-npm-package-license": "^3.0.4"}}, "npm-audit-report": {"version": "6.0.0", "bundled": true}, "npm-bundled": {"version": "4.0.0", "bundled": true, "requires": {"npm-normalize-package-bin": "^4.0.0"}}, "npm-install-checks": {"version": "7.1.1", "bundled": true, "requires": {"semver": "^7.1.1"}}, "npm-normalize-package-bin": {"version": "4.0.0", "bundled": true}, "npm-package-arg": {"version": "12.0.2", "bundled": true, "requires": {"hosted-git-info": "^8.0.0", "proc-log": "^5.0.0", "semver": "^7.3.5", "validate-npm-package-name": "^6.0.0"}}, "npm-packlist": {"version": "10.0.0", "bundled": true, "requires": {"ignore-walk": "^7.0.0"}}, "npm-pick-manifest": {"version": "10.0.0", "bundled": true, "requires": {"npm-install-checks": "^7.1.0", "npm-normalize-package-bin": "^4.0.0", "npm-package-arg": "^12.0.0", "semver": "^7.3.5"}}, "npm-profile": {"version": "11.0.1", "bundled": true, "requires": {"npm-registry-fetch": "^18.0.0", "proc-log": "^5.0.0"}}, "npm-registry-fetch": {"version": "18.0.2", "bundled": true, "requires": {"@npmcli/redact": "^3.0.0", "jsonparse": "^1.3.1", "make-fetch-happen": "^14.0.0", "minipass": "^7.0.2", "minipass-fetch": "^4.0.0", "minizlib": "^3.0.1", "npm-package-arg": "^12.0.0", "proc-log": "^5.0.0"}, "dependencies": {"minizlib": {"version": "3.0.2", "bundled": true, "requires": {"minipass": "^7.1.2"}}}}, "npm-user-validate": {"version": "3.0.0", "bundled": true}, "p-map": {"version": "7.0.3", "bundled": true}, "package-json-from-dist": {"version": "1.0.1", "bundled": true}, "pacote": {"version": "21.0.0", "bundled": true, "requires": {"@npmcli/git": "^6.0.0", "@npmcli/installed-package-contents": "^3.0.0", "@npmcli/package-json": "^6.0.0", "@npmcli/promise-spawn": "^8.0.0", "@npmcli/run-script": "^9.0.0", "cacache": "^19.0.0", "fs-minipass": "^3.0.0", "minipass": "^7.0.2", "npm-package-arg": "^12.0.0", "npm-packlist": "^10.0.0", "npm-pick-manifest": "^10.0.0", "npm-registry-fetch": "^18.0.0", "proc-log": "^5.0.0", "promise-retry": "^2.0.1", "sigstore": "^3.0.0", "ssri": "^12.0.0", "tar": "^6.1.11"}}, "parse-conflict-json": {"version": "4.0.0", "bundled": true, "requires": {"json-parse-even-better-errors": "^4.0.0", "just-diff": "^6.0.0", "just-diff-apply": "^5.2.0"}}, "path-key": {"version": "3.1.1", "bundled": true}, "path-scurry": {"version": "1.11.1", "bundled": true, "requires": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}}, "postcss-selector-parser": {"version": "6.1.2", "bundled": true, "requires": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}}, "proc-log": {"version": "5.0.0", "bundled": true}, "proggy": {"version": "3.0.0", "bundled": true}, "promise-all-reject-late": {"version": "1.0.1", "bundled": true}, "promise-call-limit": {"version": "3.0.2", "bundled": true}, "promise-retry": {"version": "2.0.1", "bundled": true, "requires": {"err-code": "^2.0.2", "retry": "^0.12.0"}}, "promzard": {"version": "2.0.0", "bundled": true, "requires": {"read": "^4.0.0"}}, "qrcode-terminal": {"version": "0.12.0", "bundled": true}, "read": {"version": "4.1.0", "bundled": true, "requires": {"mute-stream": "^2.0.0"}}, "read-cmd-shim": {"version": "5.0.0", "bundled": true}, "read-package-json-fast": {"version": "4.0.0", "bundled": true, "requires": {"json-parse-even-better-errors": "^4.0.0", "npm-normalize-package-bin": "^4.0.0"}}, "retry": {"version": "0.12.0", "bundled": true}, "safer-buffer": {"version": "2.1.2", "bundled": true, "optional": true}, "semver": {"version": "7.7.1", "bundled": true}, "shebang-command": {"version": "2.0.0", "bundled": true, "requires": {"shebang-regex": "^3.0.0"}}, "shebang-regex": {"version": "3.0.0", "bundled": true}, "signal-exit": {"version": "4.1.0", "bundled": true}, "sigstore": {"version": "3.1.0", "bundled": true, "requires": {"@sigstore/bundle": "^3.1.0", "@sigstore/core": "^2.0.0", "@sigstore/protobuf-specs": "^0.4.0", "@sigstore/sign": "^3.1.0", "@sigstore/tuf": "^3.1.0", "@sigstore/verify": "^2.1.0"}}, "smart-buffer": {"version": "4.2.0", "bundled": true}, "socks": {"version": "2.8.4", "bundled": true, "requires": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}}, "socks-proxy-agent": {"version": "8.0.5", "bundled": true, "requires": {"agent-base": "^7.1.2", "debug": "^4.3.4", "socks": "^2.8.3"}}, "spdx-correct": {"version": "3.2.0", "bundled": true, "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}, "dependencies": {"spdx-expression-parse": {"version": "3.0.1", "bundled": true, "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}}}, "spdx-exceptions": {"version": "2.5.0", "bundled": true}, "spdx-expression-parse": {"version": "4.0.0", "bundled": true, "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.21", "bundled": true}, "sprintf-js": {"version": "1.1.3", "bundled": true}, "ssri": {"version": "12.0.0", "bundled": true, "requires": {"minipass": "^7.0.3"}}, "string-width": {"version": "4.2.3", "bundled": true, "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "string-width-cjs": {"version": "npm:string-width-cjs@4.2.3", "bundled": true, "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "bundled": true, "requires": {"ansi-regex": "^5.0.1"}}, "strip-ansi-cjs": {"version": "npm:strip-ansi-cjs@6.0.1", "bundled": true, "requires": {"ansi-regex": "^5.0.1"}}, "supports-color": {"version": "10.0.0", "bundled": true}, "tar": {"version": "6.2.1", "bundled": true, "requires": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "dependencies": {"fs-minipass": {"version": "2.1.0", "bundled": true, "requires": {"minipass": "^3.0.0"}, "dependencies": {"minipass": {"version": "3.3.6", "bundled": true, "requires": {"yallist": "^4.0.0"}}}}, "minipass": {"version": "5.0.0", "bundled": true}}}, "text-table": {"version": "0.2.0", "bundled": true}, "tiny-relative-date": {"version": "1.3.0", "bundled": true}, "tinyglobby": {"version": "0.2.12", "bundled": true, "requires": {"fdir": "^6.4.3", "picomatch": "^4.0.2"}, "dependencies": {"fdir": {"version": "6.4.3", "bundled": true}, "picomatch": {"version": "4.0.2", "bundled": true}}}, "treeverse": {"version": "3.0.0", "bundled": true}, "tuf-js": {"version": "3.0.1", "bundled": true, "requires": {"@tufjs/models": "3.0.1", "debug": "^4.3.6", "make-fetch-happen": "^14.0.1"}}, "unique-filename": {"version": "4.0.0", "bundled": true, "requires": {"unique-slug": "^5.0.0"}}, "unique-slug": {"version": "5.0.0", "bundled": true, "requires": {"imurmurhash": "^0.1.4"}}, "util-deprecate": {"version": "1.0.2", "bundled": true}, "validate-npm-package-license": {"version": "3.0.4", "bundled": true, "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}, "dependencies": {"spdx-expression-parse": {"version": "3.0.1", "bundled": true, "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}}}, "validate-npm-package-name": {"version": "6.0.0", "bundled": true}, "walk-up-path": {"version": "4.0.0", "bundled": true}, "which": {"version": "5.0.0", "bundled": true, "requires": {"isexe": "^3.1.1"}, "dependencies": {"isexe": {"version": "3.1.1", "bundled": true}}}, "wrap-ansi": {"version": "8.1.0", "bundled": true, "requires": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "dependencies": {"ansi-regex": {"version": "6.1.0", "bundled": true}, "emoji-regex": {"version": "9.2.2", "bundled": true}, "string-width": {"version": "5.1.2", "bundled": true, "requires": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}}, "strip-ansi": {"version": "7.1.0", "bundled": true, "requires": {"ansi-regex": "^6.0.1"}}}}, "wrap-ansi-cjs": {"version": "npm:wrap-ansi-cjs@7.0.0", "bundled": true, "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "dependencies": {"ansi-styles": {"version": "4.3.0", "bundled": true}}}, "write-file-atomic": {"version": "6.0.0", "bundled": true, "requires": {"imurmurhash": "^0.1.4", "signal-exit": "^4.0.1"}}, "yallist": {"version": "4.0.0", "bundled": true}}}, "picomatch": {"version": "2.3.1", "resolved": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true}, "pinyin-pro": {"version": "3.26.0", "resolved": "https://registry.npmmirror.com/pinyin-pro/-/pinyin-pro-3.26.0.tgz", "integrity": "sha512-HcBZZb0pvm0/JkPhZHWA5Hqp2cWHXrrW/WrV+OtaYYM+kf35ffvZppIUuGmyuQ7gDr1JDJKMkbEE+GN0wfMoGg=="}, "punycode": {"version": "2.3.1", "resolved": "https://registry.npmmirror.com/punycode/-/punycode-2.3.1.tgz", "integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==", "dev": true}, "readdirp": {"version": "3.6.0", "resolved": "https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz", "integrity": "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==", "dev": true, "requires": {"picomatch": "^2.2.1"}}, "sass": {"version": "1.63.2", "resolved": false, "integrity": "sha512-u56TU0AIFqMtauKl/OJ1AeFsXqRHkgO7nCWmHaDwfxDo9GUMSqBA4NEh6GMuh1CYVM7zuROYtZrHzPc2ixK+ww==", "dev": true, "requires": {"chokidar": ">=3.0.0 <4.0.0", "immutable": "^4.0.0", "source-map-js": ">=0.6.2 <2.0.0"}}, "sass-loader": {"version": "10.4.1", "resolved": false, "integrity": "sha512-aX/iJZTTpNUNx/OSYzo2KsjIUQHqvWsAhhUijFjAPdZTEhstjZI9zTNvkTTwsx+uNUJqUwOw5gacxQMx4hJxGQ==", "dev": true, "requires": {"klona": "^2.0.4", "loader-utils": "^2.0.0", "neo-async": "^2.6.2", "schema-utils": "^3.0.0", "semver": "^7.3.2"}}, "schema-utils": {"version": "3.3.0", "resolved": "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.3.0.tgz", "integrity": "sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==", "dev": true, "requires": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}}, "sdp": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/sdp/-/sdp-3.2.0.tgz", "integrity": "sha512-d7wDPgDV3DDiqulJjKiV2865wKsJ34YI+NDREbm+FySq6WuKOikwyNQcm+doLAZ1O6ltdO0SeKle2xMpN3Brgw=="}, "select": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/select/-/select-1.1.2.tgz", "integrity": "sha512-OwpTSOfy6xSs1+pwcNrv0RBMOzI39Lp3qQKUTPVVPRjCdNa5JH/oPRiqsesIskK8TVgmRiHwO4KXlV2Li9dANA=="}, "semver": {"version": "7.7.1", "resolved": "https://registry.npmmirror.com/semver/-/semver-7.7.1.tgz", "integrity": "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==", "dev": true}, "source-map-js": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "dev": true}, "tiny-emitter": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/tiny-emitter/-/tiny-emitter-2.1.0.tgz", "integrity": "sha512-NB6Dk1A9xgQPMoGqC5CVXn123gWyte215ONT5Pp5a0yt4nlEoO1ZWeCwpncaekPHXO60i47ihFnZPiRPjRMq4Q=="}, "to-regex-range": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "dev": true, "requires": {"is-number": "^7.0.0"}}, "uri-js": {"version": "4.4.1", "resolved": "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "dev": true, "requires": {"punycode": "^2.1.0"}}, "vk-uview-ui": {"version": "1.5.2", "resolved": false, "integrity": "sha512-RRZgiEAc8qaUerSi7lSbrkCoLBgRUwFQHwP1V44pJO7Js+7HeHFEkkpPrtkOi14hl4CntR4qIhIDvaKmIJqVsw=="}, "webrtc-adapter": {"version": "9.0.3", "resolved": "https://registry.npmmirror.com/webrtc-adapter/-/webrtc-adapter-9.0.3.tgz", "integrity": "sha512-5fALBcroIl31OeXAdd1YUntxiZl1eHlZZWzNg3U4Fn+J9/cGL3eT80YlrsWGvj2ojuz1rZr2OXkgCzIxAZ7vRQ==", "requires": {"sdp": "^3.2.0"}}}}