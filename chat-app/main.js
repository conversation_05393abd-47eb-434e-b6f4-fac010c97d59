import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import uviewPlus from '@/uni_modules/uview-plus'
import CallMessage from './pages/call/callMessage.vue' // 注意组件名使用PascalCase
import './uni.promisify.adaptor'
import store from './store'

// 配置
Vue.config.productionTip = false
App.mpType = 'app'

// 全局注册组件
Vue.component('call-message', CallMessage) // 模板中使用短横线命名

// 挂载到Vue原型上的通话服务
Vue.prototype.$voiceCall = {
  instance: null,
  
  show(options) {
    // 如果已有实例，直接更新数据
    if (this.instance) {
      this.instance.show(options)
      return
    }
    
    // 动态创建组件实例
    const InstanceConstructor = Vue.extend(CallMessage)
    this.instance = new InstanceConstructor({
      store, // 如果用到 Vuex
      propsData: options // 传递 props（如果需要）
    })
    console.log('&&&&&')
    // 手动挂载到 DOM
    this.instance.$mount()
    
    // 插入到页面（H5 环境）
    if (typeof document !== 'undefined') {
      document.body.appendChild(this.instance.$el)
    }
    // 小程序/APP 环境（使用 uni-app API）
    else if (uni && uni.createSelectorQuery) {
      uni.createSelectorQuery()
        .select('#app')
        .node((res) => {
          res.node.appendChild(this.instance.$el)
        }).exec()
    }
    
    // 调用组件方法（假设组件有 show()）
    this.instance.show(options)
  },
  
  hide() {
    if (this.instance) {
      this.instance.hide() // 调用组件方法
      // 可选：销毁实例
      this.instance.$destroy()
      this.instance.$el.remove()
      this.instance = null
    }
  }
}


// 创建Vue实例
const app = new Vue({
  store,
  ...App
})

// 使用UI库
app.use(uviewPlus)
app.use(store)

// 挂载应用
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import uviewPlus from '@/uni_modules/uview-plus'
import store from './store'
import CallMessage from './pages/call/callMessage.vue'

export function createApp() {
  const app = createSSRApp(App)
  
  app.component('call-message', CallMessage)
  
  app.config.globalProperties.$voiceCall = {
    instance: null,
    
    show(options) {
      if (this.instance) {
        this.instance._component.proxy.show(options)
        return
      }
      
      // #ifdef H5
      const mountNode = document.createElement('div')
      document.body.appendChild(mountNode)
      this.instance = createSSRApp(CallMessage).mount(mountNode)
      // #endif
      
      // #ifdef MP-WEIXIN || APP
      const instance = createSSRApp(CallMessage)
      const container = document.createElement('view')
      const page = getCurrentPages().pop()
      page.$vm.$el.appendChild(container)
      this.instance = instance.mount(container)
      // #endif
      
      this.instance._component.proxy.show(options)
    },
    
    hide() {
      if (this.instance) {
        this.instance._component.proxy.hide()
        this.instance.unmount()
        // #ifdef H5
        document.body.removeChild(this.instance.$el)
        // #endif
        this.instance = null
      }
    }
  }
  
  app.use(uviewPlus)
  app.use(store)
  
  return {
    app,
    store
  }
}
// #endif