//
//  NERtcReplayKit.h
//  NERtcReplayKit
//
//  Created by IM.NetEase on 2022/5/9.
//

#import <Foundation/Foundation.h>

//! Project version number for NERtcReplayKit.
FOUNDATION_EXPORT double NERtcReplayKitVersionNumber;

//! Project version string for NERtcReplayKit.
FOUNDATION_EXPORT const unsigned char NERtcReplayKitVersionString[];

#define NERtcRelayKitCommitHASH 7bf0667

// In this header, you should import all the public headers of your framework using statements like #import <NERtcReplayKit/PublicHeader.h>
//Broadcast Related
#import <NERtcReplayKit/NEScreenShareSampleHandler.h>
#import <NERtcReplayKit/NEScreenShareBroadcasterOptions.h>
//Host App Related
#import <NERtcReplayKit/NEScreenSharePublicDefine.h>
#import <NERtcReplayKit/NEScreenShareHost.h>
#import <NERtcReplayKit/NEScreenShareHostDelegate.h>
#import <NERtcReplayKit/NEScreenShareHostOptions.h>
#import <NERtcReplayKit/NESrceenAudioMixerUtil.h>
