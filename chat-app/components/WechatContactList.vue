<template>
	<view>
		<up-index-list :index-list="indexList">
			<template #header>
				<view class="list">
					<view class="list__item" @click="goNext">
						<view style="display: flex;align-items: center;flex-direction: row;width: 100%;padding: 20rpx;">
							<up-avatar shape="circle" style="" size="40" src="../../static/contacts/new-friends.png"
								randomBgColor />
							<view class="list__item__user-name"
								style="padding:20rpx 20rpx 20rpx 0;margin-left: 20rpx; flex-grow: 1;color: #222222;font-weight: bold;">
								新的朋友</view>
						</view>
					</view>
					<view style="border: 1rpx solid #E6E6E6;float: right;width: 85%;"></view>
					<view class="list__item" @click="goNext">
						<view style="display: flex;align-items: center;flex-direction: row;width: 100%;padding: 20rpx;">
							<up-avatar shape="circle" style="" size="40" src="../../static/contacts/group-chats.png"
								randomBgColor />
							<view class="list__item__user-name"
								style="padding:20rpx 20rpx 20rpx 0;margin-left: 20rpx; flex-grow: 1;color: #222222;font-weight: bold;">
								群聊</view>
						</view>
					</view>
				</view>
			</template>
			<template v-for="(item, index) in itemArr">
				<!-- #ifdef APP-NVUE -->
				<up-index-anchor :text="indexList[index]"></up-index-anchor>
				<!-- #endif -->
				<up-index-item>
					<!-- #ifndef APP-NVUE -->
					<up-index-anchor :text="indexList[index]">
						11
					</up-index-anchor>
					<!-- #endif -->
					<view class="list-cell" style="" v-for="(itemchid, index) in item">
						<view v-if="itemchid.counts"
							style="font-size: 24rpx;color: #999999; margin-left: 20rpx; margin-top: 10rpx; ">
							共305位好友
						</view> 
						<view class="list__item" style="display: flex;align-items: center">
							<up-avatar shape="circle" style="" size="40" :src="itemchid.avatars" randomBgColor />
							<view class="list__item__user-name"
								style="margin-left: 20rpx; flex-grow: 1;color: #222222;font-weight: bold;">
								{{ itemchid.names }}
							</view>
						</view>
						
					</view>
				</up-index-item>
			</template>
			<!-- <template #footer>
				<view class="u-safe-area-inset--bottom">
					<text class="list__footer"
						style="display: flex;justify-content: center;align-items: flex-start;height: 100rpx;background-color: #f8f8f8;">共305位好友</text>
				</view>
			</template> -->
		</up-index-list>
	</view>
</template>

<script setup>
	import {
		ref,
		computed
	} from 'vue';

	const indexList = ref([
		"A", "B", "C", "D", "E", "F", "G", "H",
		"J", "K", "L", "M", "N", "P", "Q", "R",
		"S", "T", "U", "V", "W", "X", "Y", "Z", "#"
	])

	const itemArr = ref([
		[{
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "花开富贵"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "落叶知秋"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "雪山飞狐"
		}],
		[{
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "剑指苍穹"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "墨染流年"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "浮生若梦"
		}],
		[{
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "沧海一笑"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "竹林听雨"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "天涯孤客"
		}],
		[{
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "铁血丹心"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "云卷云舒"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "碧海潮生"
		}],
		[{
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "龙腾四海"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "凤舞九天"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "虎啸山林"
		}],
		[{
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "鹰击长空"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "鱼跃龙门"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "鹤立鸡群"
		}],
		[{
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "狼烟四起"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "烽火连城"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "金戈铁马"
		}],
		[{
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "玉树临风"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "冰清玉洁"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "闭月羞花"
		}],
		[{
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "沉鱼落雁"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "国色天香"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "倾国倾城"
		}],
		[{
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "才高八斗"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "学富五车"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "文武双全"
		}],
		[{
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "智勇双全"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "义薄云天"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "忠肝义胆"
		}],
		[{
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "侠骨柔情"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "笑傲江湖"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "纵横四海"
		}],
		[{
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "独步天下"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "天下无双"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "唯我独尊"
		}],
		[{
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "王者归来"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "君临天下"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "万古长青"
		}],
		[{
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "千秋万代"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "一统江湖"
		}, {
			"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
			"names": "名扬四海"
		}],
		[{
				"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
				"names": "威震八方"
			}, {
				"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
				"names": "声名鹊起"
			}, {
				"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
				"names": "如日中天"
			},
			{
				"avatars": "https://uiadmin.net/uview-plus/album/10.jpg",
				"names": "如日中天1234"
			}, {
				"avatars": null,
				"names": "共305位好友"
			}
		]
	])

	const indexListStyle = computed(() => {
		const systemInfo = uni.getSystemInfoSync();
		const statusBarHeight = systemInfo.statusBarHeight || 0;
		const navBarHeight = 44; // 导航栏高度
		const tabBarHeight = 50; // 底部标签栏高度
		const safeAreaBottom = systemInfo.safeAreaInsets?.bottom || 0;

		// 根据不同平台计算高度
		// #ifdef H5
		if (systemInfo.platform === 'ios') {
			return {
				height: `calc(100vh - ${statusBarHeight + navBarHeight + tabBarHeight + safeAreaBottom}px)`,
				overflow: 'auto'
			};
		}
		// #endif

		// 默认计算方式
		return {
			height: `calc(100vh - ${statusBarHeight + navBarHeight + tabBarHeight}px)`,
			overflow: 'auto'
		};
	});

	const goNext = () => {
		console.log("dsafds")
	}
</script>

<style lang="scss">
	.list-cell {
		display: flex;
		box-sizing: border-box;
		width: 100%;
		padding: 10px 24rpx;
		overflow: hidden;
		color: #323233;
		font-size: 14px;
		line-height: 24px;
		background-color: #fff;
	}

	.list {
		background-color: #fff;
		width: 100%;
	}

	.list__item {
		width: 100%;
		background-color: #fff;
	}
</style>