<template>
  <view class="custom-modal" v-if="visible">
    <view class="modal-mask"></view>
    <view class="modal-container">
      <view class="modal-content">
        <text class="modal-text">{{ content }}</text>
      </view>
      <view class="modal-footer">
        <view class="modal-btn cancel-btn" @click="cancelHandler">
          <text>{{ cancelText }}</text>
        </view>
        <view class="modal-btn confirm-btn" @click="confirmHandler">
          <text>{{ confirmText }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  content: {
    type: String,
    default: ''
  },
  confirmText: {
    type: String,
    default: '确认'
  },
  cancelText: {
    type: String,
    default: '取消'
  }
});

const emit = defineEmits(['confirm', 'cancel', 'update:visible']);

const confirmHandler = () => {
  emit('confirm');
  emit('update:visible', false);
};

const cancelHandler = () => {
  emit('cancel');
  emit('update:visible', false);
};
</script>

<style lang="scss" scoped>
.custom-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-container {
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  position: relative;
  z-index: 10000;
}

.modal-content {
  padding: 50rpx 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-text {
  font-size: 32rpx;
  color: #000;
  text-align: center;
  line-height: 1.5;
}

.modal-footer {
  display: flex;
  height: 100rpx;
  border-top: 1rpx solid #eee;
}

.modal-btn {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.cancel-btn {
  border-right: 1rpx solid #eee;
  color: #000;
}

.confirm-btn {
  color: #FF3366;
}
</style>