<template>
  <view class="message-list-container" @tap="closeLocalBubbleMenu">
    <!-- 加载状态提示 -->
    <view class="loading-more" v-if="isLoadingMore">
      <view class="loading-icon"></view>
      <text class="loading-text">加载更多消息...</text>
    </view>

    <!-- 消息列表 -->
    <view class="message-container">
      <block v-for="item in messages" :key="item.id">
        <!-- 时间标签 -->
        <view class="time-tag" v-if="item.showTimeTag">
          <text>{{ item.timeTag || formatTime(item.time) }}</text>
        </view>

        <!-- 撤回消息 -  -->
        <view v-if="item.type == 'recall'" class="recall-message-container">
          <view class="recall-bubble" :id="'bubble-' + item.id">
            <text class="recall-text">{{ item.content }}</text>
          </view>
        </view>

        <!-- 普通消息气泡 -->
        <view v-else class="message-item" :class="{ self: item.self }">
          <!-- 头像 -->
          <image class="avatar" :src="item.avatar" mode="aspectFill" @click.stop="onAvatarClick(item)"></image>

          <!-- 消息内容 -->
          <view class="message-content">
            <!-- 文本消息 -->
            <view v-if="item.type === 'text'" class="message-bubble" :class="{ 'self-bubble': item.self }"
              :id="'bubble-' + item.id" @longpress.stop="handleBubbleLongPress($event, item)" @tap.stop>
              <text class="message-text">{{ item.content }}</text>
            </view>

            <!-- 图片消息 -->
            <view v-else-if="item.type === 'image'" class="message-bubble image-bubble"
              :class="{ 'self-bubble': item.self }" :id="'bubble-' + item.id"
              @longpress.stop="handleBubbleLongPress($event, item)" @tap.stop="$emit('viewImage', item)">
              <image :src="item.content" class="bubble-image" lazy-load
                style="max-width: 320rpx; max-height: 320rpx; min-width: 80rpx; min-height: 80rpx;" />
            </view>

            <!-- 视频消息 -->
            <view v-else-if="item.type === 'video'" class="message-bubble video-bubble"
              :class="{ 'self-bubble': item.self }" :id="'bubble-' + item.id"
              @longpress.stop="handleBubbleLongPress($event, item)" @tap.stop="$emit('playVideo', item)">
              <view class="video-container">
                <image :src="item.thumbUrl || item.content" class="video-thumb" lazy-load
                  style="max-width: 320rpx; max-height: 320rpx; min-width: 80rpx; min-height: 80rpx;" />
                <view class="video-play-icon">
                  <view class="video-play-circle">
                    <view class="video-play-triangle"></view>
                  </view>
                </view>
                <view class="video-duration" v-if="item.videoDuration">{{ formatDuration(item.videoDuration) }}</view>
              </view>
            </view>

            <!-- 语音消息 -->
            <view v-else-if="item.type === 'voice'" class="voice-bubble" :class="{ 'self-bubble': item.self }"
              :id="'bubble-' + item.id" @tap.stop="onPlayVoice(item)"
              @longpress.stop="handleBubbleLongPress($event, item)">
              <view class="voice-content">
                <view class="voice-icon" :class="{ 'voice-playing': item.isPlaying }">
                  <view class="voice-wave"></view>
                  <view class="voice-wave"></view>
                  <view class="voice-wave"></view>
                </view>
                <view class="voice-duration">
                  <text>{{ item.content.duration }}"</text>
                </view>
              </view>
            </view>
			<!-- 语音通话 -->
		<!-- 	<view v-if="item.type === 'voiceCall'" class="message-bubble voice-call-end-bubble"  :class="{ 'self-bubble': item.self }"
			  :id="'bubble-' + item.id"  @tap.stop>
			  <up-icon name="phone"  size="24" :color="item.self ?  '#fff' : '#999' "></up-icon>
			  <text class="message-text" style="display: flex;align-items: center;">{{ item.content }}  </text>

			</view> -->

			<!-- 语音通话终止 -->
			<view v-else-if="item.type === 'voiceCallEnd'||item.type === 'voiceCall'" class="message-bubble voice-call-end-bubble" :class="{ 'self-bubble': item.self }"
			  :id="'bubble-' + item.id"  @tap.stop="onVoiceClick()" >
			  <view class="voice-call-end-content">
				<up-icon name="phone" size="24" :color="item.self ?  '#fff' : '#999' "></up-icon>	
				<text class="message-text voice-call-end-text">{{ item.content }}</text>
				
			  </view>
			</view>
            
            <!-- 文件消息 -->
            <view v-else-if="item.type === 'file'" class="file-bubble" :class="{ 'self-bubble': item.self }"
              :id="'bubble-' + item.id" @tap.stop="onOpenFile(item)"
              @longpress.stop="handleBubbleLongPress($event, item)">
              <view class="file-icon">
                <image class="file-icon-img" src="/static/conversation/files.png"></image>
              </view>
              <view class="file-info">
                <text class="file-name">{{ item.content }}</text>
                <text class="file-size" v-if="item.fileSize">{{ formatFileSize(item.fileSize) }}</text>
              </view>
            </view>
          </view>

          <!-- 发送状态 -->
          <view class="message-status" v-if="item.self">
            <view v-if="item.status === 'sending'" class="sending-status">
              <view class="loading-dot"></view>
            </view>
            <view v-else-if="item.status === 'failed'" class="failed-status">
              <text class="failed-icon">!</text>
            </view>
          </view>
        </view>
      </block>
    </view>

    <!-- 气泡菜单 - 使用 v-show 控制显隐 -->
    <view v-show="localBubbleMenuInfo.visible" class="bubble-menu-container" :style="menuStyle" @tap.stop>
      <view class="bubble-menu" :class="{
        'self-menu': localBubbleMenuInfo.isSelf,
        'menu-below': !localBubbleMenuInfo.isAbove
      }">
        <!-- 复制功能 - 只有文本消息才显示 -->
        <view v-if="shouldShowCopy" class="bubble-menu-item" @tap="copyMsg">
          <view class="menu-icon-wrapper">
            <image class="menu-icon" src="/static/conversation/copy.png"></image>
          </view>
          <text class="menu-text">复制</text>
        </view>

        <!-- 撤回功能 - 只有自己的消息且在2分钟内才显示 -->
        <view v-if="shouldShowRecall" class="bubble-menu-item" @tap="recallMsg">
          <view class="menu-icon-wrapper">
            <image class="menu-icon" src="/static/conversation/recall.png"></image>
          </view>
          <text class="menu-text">撤回</text>
        </view>

        <!-- 转发功能 - 撤回消息不能转发 -->
        <view v-if="shouldShowForward" class="bubble-menu-item" @tap="forwardMsg">
          <view class="menu-icon-wrapper">
            <image class="menu-icon" src="/static/conversation/transfer.png"></image>
          </view>
          <text class="menu-text">转发</text>
        </view>

        <!-- 删除功能 -->
        <view class="bubble-menu-item" @tap="deleteMsg">
          <view class="menu-icon-wrapper">
            <image class="menu-icon" src="/static/conversation/delete.png"></image>
          </view>
          <text class="menu-text">删除</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits, ref, computed, getCurrentInstance, nextTick } from "vue";

const props = defineProps({
  messages: {
    type: Array,
    default: () => [],
  },
  initialMessage: {
    type: Array,
    default: () => [],
  },
  isLoadingMore: {
    type: Boolean,
    default: false,
  },
  // 接收父组件的滚动偏移量
  scrollTop: {
    type: Number,
    default: 0,
  }
});

const emit = defineEmits(["playVoice", "copy", "delete", "forward", "multiSelect", "openFile", "viewImage", "playVideo", "recall", "avatarClick"]);

// 获取当前组件实例
const instance = getCurrentInstance();

// 本地管理气泡菜单状态
const localBubbleMenuInfo = ref({
  id: -1,
  top: 0,
  left: 0,
  visible: false,
  message: null, // 保存选中的消息对象
  isSelf: false, // 标记是否是自己的消息，用于菜单样式
  isAbove: true, // 标记菜单是否在消息上方（默认为true）
  triangleOffset: 50 // 三角形位置偏移（百分比）
});

// 计算菜单位置和样式 - 现在位置已经在handleBubbleLongPress中计算好了
const menuStyle = computed(() => {
  if (!localBubbleMenuInfo.value.visible) return {};

  // 直接使用已经计算好的位置
  return {
    top: `${localBubbleMenuInfo.value.top}px`,
    left: `${localBubbleMenuInfo.value.left}px`,
    '--triangle-offset': `${localBubbleMenuInfo.value.triangleOffset}%`
  };
});

// 计算是否显示复制按钮 - 只有文本消息才显示复制
const shouldShowCopy = computed(() => {
  const message = localBubbleMenuInfo.value.message;
  if (!message) return false;
  return message.type === 'text';
});

// 计算是否显示转发按钮 - 撤回消息不能转发
const shouldShowForward = computed(() => {
  const message = localBubbleMenuInfo.value.message;
  if (!message) return false;
  return message.type !== 'recall';
});

// 计算是否显示撤回按钮 - 只有自己的消息且在2分钟内可以撤回
const shouldShowRecall = computed(() => {
  const message = localBubbleMenuInfo.value.message;
  if (!message || !message.self || message.type === 'recall') return false;

  // 检查消息时间是否在2分钟内
  const messageTime = new Date(message.time);
  const currentTime = new Date();
  const timeDiff = currentTime.getTime() - messageTime.getTime();
  const twoMinutes = 2 * 60 * 1000; // 2分钟的毫秒数

  return timeDiff <= twoMinutes;
});

// 格式化时间
const formatTime = (time) => {
  if (!time) return "";

  const date = new Date(time);
  const hours = date.getHours();
  const minutes = date.getMinutes();

  return `${hours}:${minutes < 10 ? "0" + minutes : minutes}`;
};
const getMsg=(item)=>{
	console.log(JSON.parse(item))
	return JSON.parse(item)
}
// 气泡长按处理函数 - 内部处理
const handleBubbleLongPress = (event, item) => {
  console.log('handleBubbleLongPress', item);
  // 撤回消息不显示菜单
  if (item.type === 'recall') {
    return;
  }

  // 震动反馈
  try {
    uni.vibrateShort({});
  } catch (e) { /* 忽略震动失败 */ }

  console.log('长按消息ID:', item.id, '消息内容:', item.content || item.text);

  // 先尝试使用触摸点坐标作为基准
  const touchY = event.touches[0]?.clientY || event.changedTouches[0]?.clientY || 0;
  const touchX = event.touches[0]?.clientX || event.changedTouches[0]?.clientX || 0;

  console.log('触摸点坐标:', { x: touchX, y: touchY });

  // 获取容器位置信息
  const query = uni.createSelectorQuery().in(instance);
  const containerId = '.message-list-container';

  query.select(containerId).boundingClientRect();
  query.exec((res) => {
    console.log('容器查询结果:', res);
    const containerRect = res[0];

    if (containerRect) {
      console.log('容器位置:', containerRect);

      // 计算触摸点相对于容器的位置
      const relativeY = touchY - containerRect.top;
      const relativeX = touchX - containerRect.left;

      console.log('触摸点相对位置:', { top: relativeY, left: relativeX });

      // 菜单尺寸 - 根据按钮数量动态调整
      const menuWidth = uni.upx2px(320); // 增加宽度以容纳更多按钮
      const menuHeight = uni.upx2px(80);
      const triangleHeight = uni.upx2px(6);

      // 计算菜单位置 - 始终显示在触摸点上方
      let calculatedTop, calculatedLeft;
      let isMenuAbove = true; // 始终在上方

      // 菜单显示在触摸点上方，距离20px
      calculatedTop = relativeY - menuHeight - uni.upx2px(40); // 20px = 40rpx

      // 如果上方空间不够，则显示在下方
      if (calculatedTop < uni.upx2px(20)) {
        calculatedTop = relativeY + uni.upx2px(40); // 显示在下方，距离20px
        isMenuAbove = false;
      }

      // 菜单水平居中对齐触摸点
      calculatedLeft = relativeX - (menuWidth / 2);

      // 边界处理
      const containerWidth = containerRect.width;
      let triangleOffset = 50; // 三角形默认在菜单中心

      if (calculatedLeft < uni.upx2px(20)) {
        // 计算三角形偏移
        triangleOffset = ((relativeX - uni.upx2px(20)) / menuWidth) * 100;
        calculatedLeft = uni.upx2px(20);
        triangleOffset = Math.max(10, Math.min(90, triangleOffset));
      } else if (calculatedLeft + menuWidth > containerWidth - uni.upx2px(20)) {
        // 计算三角形偏移
        const newLeft = containerWidth - menuWidth - uni.upx2px(20);
        triangleOffset = ((relativeX - newLeft) / menuWidth) * 100;
        calculatedLeft = newLeft;
        triangleOffset = Math.max(10, Math.min(90, triangleOffset));
      }

      // 确保菜单不超出容器上下边界
      if (calculatedTop < uni.upx2px(20)) {
        calculatedTop = uni.upx2px(20);
        isMenuAbove = false;
      }

      console.log('最终菜单位置:', {
        top: calculatedTop,
        left: calculatedLeft,
        isAbove: isMenuAbove,
        triangleOffset: triangleOffset,
        touchPoint: { x: relativeX, y: relativeY },
        menuWidth: menuWidth,
        menuHeight: menuHeight
      });

      // 更新状态
      localBubbleMenuInfo.value = {
        id: item.id,
        top: calculatedTop,
        left: calculatedLeft,
        visible: true,
        message: item,
        isSelf: item.self,
        isAbove: isMenuAbove,
        triangleOffset: triangleOffset
      };
    } else {
      // Fallback: 直接使用触摸点坐标
      console.log('容器查询失败，使用绝对坐标');

      localBubbleMenuInfo.value = {
        id: item.id,
        top: touchY - uni.upx2px(80) - 20, // 显示在触摸点上方
        left: touchX - uni.upx2px(120),
        visible: true,
        message: item,
        isSelf: item.self,
        isAbove: true,
        triangleOffset: 50
      };
    }
  });
};

// 关闭本地菜单
const closeLocalBubbleMenu = () => {
  if (localBubbleMenuInfo.value.visible) {
    localBubbleMenuInfo.value = {
      id: -1,
      top: 0,
      left: 0,
      visible: false,
      message: null,
      isSelf: false,
      isAbove: true,
      triangleOffset: 50
    };
  }
};

// 格式化视频时长
const formatDuration = (duration) => {
  if (!duration && duration !== 0) return "00:00";

  const minutes = Math.floor(duration / 60);
  const seconds = Math.floor(duration % 60);

  return `${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
};

// 播放语音
const onPlayVoice = (item) => {
  emit("playVoice", item);
};

// 打开文件
const onOpenFile = (item) => {
  emit("openFile", item);
};

// 头像点击事件
const onAvatarClick = (item) => {
  console.log('点击了头像:', item);
  // 只有非自己发送的消息才能点击头像查看好友详情
  if (!item.self) {
   emit("avatarClick", item);
  }
};

const onVoiceClick = (item) => {

 emit("voiceClick", item);
};

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return "";
  if (size < 1024) {
    return size + "B";
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + "KB";
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + "MB";
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + "GB";
  }
};

// --- 修改菜单项点击事件 ---
function copyMsg() {
  if (!localBubbleMenuInfo.value.message) return;

  const message = localBubbleMenuInfo.value.message;
  const textToCopy = message.content || message.text || '';

  if (textToCopy) {
    // 使用uni-app的复制到剪贴板API
    uni.setClipboardData({
      data: textToCopy,
      success: () => {
        console.log('复制成功:', textToCopy);
      }
    });
  }

  // 同时触发父组件的copy事件（如果需要）
  emit('copy', message);
  closeLocalBubbleMenu();
}

function deleteMsg() {
  if (!localBubbleMenuInfo.value.message) return;
  emit('delete', localBubbleMenuInfo.value.message);
  closeLocalBubbleMenu();
}

function forwardMsg() {
  let currentMessage = props.initialMessage.find(item => item.id == localBubbleMenuInfo.value.message.id);
  if (!localBubbleMenuInfo.value.message && !currentMessage) return;
  const params = {
    initParams: currentMessage,
    ...localBubbleMenuInfo.value.message,
  }
  emit('forward', params);
  closeLocalBubbleMenu();
}

function recallMsg() {
  if (!localBubbleMenuInfo.value.message) return;

  const message = localBubbleMenuInfo.value.message;

  // 直接触发父组件的撤回事件，父组件会处理确认对话框和后续逻辑
  emit('recall', message);

  closeLocalBubbleMenu();
}

function multiSelectMsg() {
  if (!localBubbleMenuInfo.value.message) return;
  emit('multiSelect', localBubbleMenuInfo.value.message);
  closeLocalBubbleMenu();
}

// 暴露关闭菜单方法给父组件（如果需要外部关闭）
defineExpose({ closeLocalBubbleMenu });
</script>

<style>
.message-list-container {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  position: relative;
}

.loading-more {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f2f2f2;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 24rpx;
  color: #999;
}

.time-tag {
  text-align: center;
  margin: 20rpx 0;
}

.time-tag text {
  background-color: rgba(0, 0, 0, 0.1);
  color: #999;
  font-size: 24rpx;
  padding: 4rpx 10rpx;
  border-radius: 8rpx;
}

.message-container {
  width: 100%;
  will-change: transform;
  transform: translateZ(0);
}

.message-item {
  display: flex;
  margin-bottom: 30rpx;
  position: relative;
  align-items: flex-start;
  will-change: transform;
  /* 告知浏览器该元素会被频繁变换 */
  contain: layout;
  /* 限制重排影响范围 */
}

.message-item.self {
  flex-direction: row-reverse;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin: 0 20rpx;
  will-change: transform;
  transform: translateZ(0);
}

.message-content {
  max-width: 60%;
}

.message-bubble {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 20rpx;
  position: relative;
  word-break: break-all;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  will-change: transform;
  transform: translateZ(0);
}

.self-bubble {
  background: linear-gradient(90deg, #FF5685, #FF7D39);
  color: #fff;
}

/* 添加气泡尖角 - 他人消息（左侧箭头） */
.message-bubble:before {
  content: "";
  position: absolute;
  top: 20rpx;
  left: -16rpx;
  border-width: 8rpx;
  border-style: solid;
  border-color: transparent #fff transparent transparent;
}

/* 自己发送的消息气泡箭头（右侧箭头） */
.self-bubble:before {
  content: "";
  position: absolute;
  top: 20rpx;
  right: -16rpx;
  left: auto;
  border-width: 8rpx;
  border-style: solid;
  border-color: transparent transparent transparent #FF5685;
}


/* 语音消息样式 */
.voice-bubble {
  min-width: 120rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  position: relative;
}

/* 语音消息气泡箭头 - 他人消息（左侧箭头） */
.voice-bubble:before {
  content: "";
  position: absolute;
  top: 20rpx;
  left: -16rpx;
  border-width: 8rpx;
  border-style: solid;
  border-color: transparent #fff transparent transparent;
}

.self-bubble.voice-bubble {
  background: linear-gradient(90deg, #FF5685, #FF7D39);
}

/* 自己发送的语音消息气泡箭头（右侧箭头） */
.self-bubble.voice-bubble:before {
  content: "";
  position: absolute;
  top: 20rpx;
  right: -16rpx;
  left: auto;
  border-width: 8rpx;
  border-style: solid;
  border-color: transparent transparent transparent #FF5685;
}

.voice-content {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}

.voice-icon {
  display: flex;
  align-items: center;
  height: 40rpx;
}

.voice-wave {
  width: 4rpx;
  height: 20rpx;
  background-color: #666;
  margin: 0 3rpx;
  border-radius: 2rpx;
}

.self-bubble .voice-wave {
  background-color: #fff;
}

/* 默认状态静止，不使用动画 */
.voice-icon .voice-wave:nth-child(1) {
  height: 10rpx;
}

.voice-icon .voice-wave:nth-child(2) {
  height: 20rpx;
}

.voice-icon .voice-wave:nth-child(3) {
  height: 15rpx;
}

/* 只有在播放状态下才使用动画 */
.voice-playing .voice-wave:nth-child(1) {
  animation: voiceWave 1s infinite 0.1s;
}

.voice-playing .voice-wave:nth-child(2) {
  animation: voiceWave 1s infinite 0.2s;
}

.voice-playing .voice-wave:nth-child(3) {
  animation: voiceWave 1s infinite 0.3s;
}

@keyframes voiceWave {

  0%,
  100% {
    height: 10rpx;
  }

  50% {
    height: 30rpx;
  }
}

.voice-duration {
  font-size: 28rpx;
  margin-left: 10rpx;
  color: #666;
}

.self-bubble .voice-duration {
  color: #fff;
}

/* 语音通话终止消息样式 */
.voice-call-end-bubble {
  background-color: #f5f5f5;
  border: 1rpx solid #e0e0e0;
  padding: 16rpx 20rpx;
}

.self-bubble.voice-call-end-bubble {
  background: linear-gradient(90deg, #FF5685, #FF7D39);
  border: none;
  color: #fff;
}

.voice-call-end-content {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.voice-call-end-text {
  font-size: 28rpx;
  line-height: 40rpx;
}

/* 视频消息样式 */
.video-bubble {
  padding: 0;
  background: none;
  box-shadow: none;
  position: relative;
}

/* 视频消息气泡箭头 - 他人消息（左侧箭头） */
.video-bubble:before {
  content: "";
  position: absolute;
  top: 20rpx;
  left: -16rpx;
  border-width: 8rpx;
  border-style: solid;
  border-color: transparent #f5f5f5 transparent transparent;
}

/* 自己发送的视频消息气泡箭头（右侧箭头） */
.self-bubble.video-bubble:before {
  content: "";
  position: absolute;
  top: 20rpx;
  right: -16rpx;
  left: auto;
  border-width: 8rpx;
  border-style: solid;
  border-color: transparent transparent transparent #f5f5f5;
}

.video-container {
  position: relative;
  border-radius: 18rpx;
  overflow: hidden;
}

.video-thumb {
  display: block;
  border-radius: 18rpx;
  width: 320rpx;
  height: 240rpx;
  object-fit: cover;
}

.video-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.video-play-circle {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-play-triangle {
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 20rpx 0 20rpx 30rpx;
  border-color: transparent transparent transparent #fff;
  margin-left: 8rpx;
}

.video-duration {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 24rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

/* 微信风格气泡菜单 */
.bubble-menu-container {
  position: absolute;
  z-index: 999;
  pointer-events: auto;
  will-change: transform;
  transform: translateZ(0);
}

.bubble-menu {
  background: rgba(40, 40, 40, 0.95);
  border-radius: 6rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  padding: 8rpx 12rpx;
  height: 80rpx;
  min-width: 320rpx; /* 增加最小宽度以容纳更多按钮 */
  max-width: 400rpx; /* 设置最大宽度 */
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.3);
  position: relative;
}

/* 添加小三角形指向消息 - 默认向下指向（菜单在消息上方） */
.bubble-menu::after {
  content: '';
  position: absolute;
  bottom: -6rpx;
  left: var(--triangle-offset, 50%);
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6rpx solid transparent;
  border-right: 6rpx solid transparent;
  border-top: 6rpx solid rgba(40, 40, 40, 0.95);
}

/* 当菜单在消息下方时，三角形指向上方 */
.bubble-menu.menu-below::after {
  bottom: auto;
  top: -6rpx;
  left: var(--triangle-offset, 50%);
  border-top: none;
  border-bottom: 6rpx solid rgba(40, 40, 40, 0.95);
}

.bubble-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 12rpx; /* 减少左右padding */
  height: 100%;
  min-width: 60rpx; /* 减少最小宽度 */
  border-radius: 4rpx;
  transition: background-color 0.15s ease;
  flex: 1;
}

.bubble-menu-item:active {
  background-color: rgba(255, 255, 255, 0.15);
}

.menu-icon-wrapper {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 4rpx;
}

.menu-icon {
  width: 24rpx;
  height: 24rpx;
  filter: brightness(0) invert(1);
}

.menu-text {
  font-size: 18rpx;
  color: #fff;
  text-align: center;
  line-height: 1;
  font-weight: 400;
  white-space: nowrap;
}

.message-text {
  font-size: 32rpx;
  line-height: 44rpx;
}

.image-bubble {
  padding: 0;
  background: none;
  box-shadow: none;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* 图片消息气泡箭头 - 他人消息（左侧箭头） */
.image-bubble:before {
  content: "";
  position: absolute;
  top: 20rpx;
  left: -16rpx;
  border-width: 8rpx;
  border-style: solid;
  border-color: transparent #f5f5f5 transparent transparent;
}

/* 自己发送的图片消息气泡箭头（右侧箭头） */
.self-bubble.image-bubble:before {
  content: "";
  position: absolute;
  top: 20rpx;
  right: -16rpx;
  left: auto;
  border-width: 8rpx;
  border-style: solid;
  border-color: transparent transparent transparent #f5f5f5;
}

.bubble-image {
  border-radius: 18rpx;
  display: block;
  background: #f5f5f5;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
}

.message-status {
  position: absolute;
  bottom: 0;
  margin: 0 10rpx;
}

.sending-status {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid #999;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

.failed-status {
  width: 30rpx;
  height: 30rpx;
  background-color: #ff4f4f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.failed-icon {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

/* 文件气泡样式 */
.file-bubble {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  min-width: 200rpx;
  max-width: 400rpx;
  position: relative;
}

/* 文件消息气泡箭头 - 他人消息（左侧箭头） */
.file-bubble:before {
  content: "";
  position: absolute;
  top: 20rpx;
  left: -16rpx;
  border-width: 8rpx;
  border-style: solid;
  border-color: transparent #fff transparent transparent;
}

.self-bubble.file-bubble {
  background-color: #ff4f81;
}

/* 自己发送的文件消息气泡箭头（右侧箭头） */
.self-bubble.file-bubble:before {
  content: "";
  position: absolute;
  top: 20rpx;
  right: -16rpx;
  left: auto;
  border-width: 8rpx;
  border-style: solid;
  border-color: transparent transparent transparent #ff4f81;
}

.file-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-icon-img {
  width: 60rpx;
  height: 60rpx;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.file-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.self-bubble .file-name {
  color: #fff;
}

.file-size {
  font-size: 24rpx;
  color: #999;
}

.self-bubble .file-size {
  color: rgba(255, 255, 255, 0.8);
}

/* 撤回消息 */
.recall-message-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 15rpx 0;
}

.recall-bubble {
  background: none;
  border: none;
  padding: 10rpx 20rpx;
  text-align: center;
  display: inline-block;
}

.recall-text {
  text-align: center;
  font-size: 24rpx;
  color: #bab0b0;
  font-family: normal;
}
</style>