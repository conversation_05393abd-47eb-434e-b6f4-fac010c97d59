<template>
  <view class="custom-tabbar">
    <view 
      v-for="(item, index) in tabItems" 
      :key="index"
      class="tab-item" 
      :class="{ active: currentTab === item.key }"
      @click="switchTab(item.key)"
    >
	<uni-badge size="small" :text="item.num" :max-num="100" absolute="rightTop" type="error"  :offset="[-5, 3]" >
      <image class="tab-icon" :src="item.icon" mode="aspectFit"></image>
	</uni-badge>
	<text>{{ item.text }}</text>
    </view>
  </view>
</template>

<script setup>
import {
	ref,
	computed,
	onUnmounted
} from 'vue'
import { useStore } from 'vuex';
import { t, getCurrentLanguage } from '@/utils/i18n.js';

const store = useStore();
const props = defineProps({
  currentTab: {
    type: String,
    default: 'me'
  }
});

const emit = defineEmits(['tabChange']);

const totalUnreadCount = computed(() => {
  return store.getters.getChatList.reduce((sum, chat) => {
    return sum + (chat.unreadCount || 0); // 防止 undefined 或 null
  }, 0);
});


// 使用 computed 响应式获取通知消息
const notifyMessages = computed(() => {
  return store.state.notifyMsg || [];
});

// 过滤出未读的好友申请消息
const unreadFriendRequests = computed(() => {
  return notifyMessages.value.filter(msg => 
    msg.typecode == 3 && msg.isRedRead == 0
  );
});
const unreadCount = computed(() => {
	return unreadFriendRequests.value.length;
});
// 响应式语言状态
const currentLang = ref(getCurrentLanguage());

// 监听语言变化事件
uni.$on('languageChanged', (lang) => {
  currentLang.value = lang;
});

// 多语言翻译函数
const $t = (key) => {
  // 访问响应式变量以触发更新
  currentLang.value;
  return t(key);
};

const tabItems = computed(() => [
  {
    key: 'chats',
    text: $t('tabBar.chats'),
    icon: '/static/tabbar/chat.png',
    num: totalUnreadCount.value
  },
  {
    key: 'contacts',
    text: $t('tabBar.contacts'),
    icon: '/static/tabbar/contacts.png',
    num: unreadCount.value
  },
  {
    key: 'channels',
    text: $t('tabBar.channels'),
    icon: '/static/tabbar/planet.png',
    num: 0
  },
  {
    key: 'me',
    text: $t('tabBar.me'),
    icon: '/static/tabbar/me.png',
    num: 0
  }
]);

const switchTab = (tab) => {
  console.log('CustomTabbar: 触发切换标签页:', tab);
  emit('tabChange', tab);
};

// 组件销毁时清理事件监听
onUnmounted(() => {
  uni.$off('languageChanged');
});
</script>

<style lang="scss" scoped>
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 8vh;
  background: #fff;
  display: flex;
  align-items: center;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 10rpx 0;
    margin-bottom: env(safe-area-inset-bottom);

    &.active {
      color: #FF4B6A;
      
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 88rpx;
        height: 88rpx;
        background: rgba(255, 75, 106, 0.1);
        border-radius: 16rpx;
        z-index: 1;
      }

      .tab-icon {
        position: relative;
        z-index: 2;
        filter: brightness(0) saturate(100%) invert(45%) sepia(83%) saturate(2151%) hue-rotate(322deg) brightness(99%) contrast(101%);
      }

      text {
        position: relative;
        z-index: 2;
        color: #FF4B6A;
        font-weight: 500;
      }
    }

    .tab-icon {
      width: 44rpx;
      height: 44rpx;
      margin-bottom: 6rpx;
    }

    text {
      font-size: 20rpx;
      color: #999;
      line-height: 1;
    }
  }
}
</style>
