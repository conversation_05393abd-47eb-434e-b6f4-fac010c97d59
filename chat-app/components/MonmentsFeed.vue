<template>
	<view class="moments-feed">
		<!-- 封面区域 -->
		<view class="cover-area">
			<image class="cover-image" :src="channelImg" mode="aspectFill"></image>
			<view class="user-profile-on-cover">
				<!-- <text class="nickname-on-cover">{{ coverInfo.nickname }}</text>
        <image class="avatar-on-cover" :src="coverInfo.avatarUrl" mode="aspectFill"></image> -->
			</view>
		</view>

		<!-- 朋友圈列表 -->
		<view v-for="moment in momentsData" :key="moment.id" class="moment-item">
			<view class="moment-header">
				<image :src="moment.user.avatar" class="avatar" mode="aspectFill"></image>
				<view class="user-info">
					<text class="nickname">{{ moment.user.nickname }}</text>
					<text v-if="moment.content.text" class="content-text">{{ moment.content.text }}</text>
				</view>
			</view>

			<!-- 内容区域 -->
			<view class="moment-content">
				<!-- 单张图片展示 -->
				<view v-if="moment.content.mediaList.length==1" class="media-container">
					<image v-if="moment.content.mediaList[0].type=='img'" :src="moment.content.mediaList[0].val"
						class="single-image-display" mode="widthFix"
						@tap="openImagePreview(moment.content.mediaList[0].val)"></image>
					<image v-if="moment.content.mediaList[0].type=='video'" src="/static/poster.jpg"
						class="single-image-display" mode="widthFix"
						@tap="openVideoFullscreen(moment.content.mediaList[0].val,'/static/poster.jpg')"></image>
				</view>
				<view v-if="moment.content.mediaList.length>1" class="media-container">
					<up-grid :border="false" gap="10rpx">
						<up-grid-item span="4" v-for="(item,index) in moment.content.mediaList" :key="index">
							<image v-if="item.type=='img'" :src="item.val" class="single-image-display" mode="scaleToFill"
								@tap.stop="openImagePreview(item.val)"></image>
							<image v-if="item.type=='video'" src="/static/poster.jpg" class="single-image-display"
								mode="scaleToFill" @tap.stop="openVideoFullscreen(item.val, '/static/poster.jpg')"></image>
						</up-grid-item>
					</up-grid>

				</view>

			</view>

			<!-- 底部区域 -->
			<view class="moment-footer">
				<text class="timestamp">{{ moment.timestamp }}</text>
			</view>
		</view>

		<!-- 全屏图片预览 -->
		<view v-if="showImageFullscreen" class="fullscreen-image-container" @tap="closeImagePreview">
			<image :src="currentImage" class="fullscreen-image" mode="aspectFit"></image>
			<view class="close-btn">×</view>
		</view>

		<!-- 全屏视频播放 -->
		<view v-if="showVideoFullscreen" class="fullscreen-video-container">
			<video :id="'fullscreen-video'" :src="currentVideo" class="fullscreen-video" :poster="currentVideoPoster"
				:controls="true" :loop="false" :autoplay="true" :show-fullscreen-btn="true" :enable-play-gesture="true"
				:show-loading="true" :initial-time="0" :page-gesture="true" :show-progress="true"
				:show-center-play-btn="true" :title="'视频播放'" :vslide-gesture="true" :vslide-gesture-in-fullscreen="true"
				@ended="handleVideoEnded" @fullscreenchange="handleVideoFullscreenChange"></video>
			<view class="close-btn" @tap.stop="closeVideoFullscreen">×</view>

			<!-- 安卓特定底部退出按钮 -->
			<view v-if="isAndroid" class="android-exit-btn" @tap.stop="closeVideoFullscreen">
				退出视频
			</view>
		</view>
	</view>
</template>

<script>
	import {
		ref,
		onUnmounted,
		onMounted,
		computed
	} from 'vue';
	import {
		GetChannelList
	} from '../api/channel';
	import {
		useStore
	} from 'vuex';
	import {
		onShow,
		onReady,
		onHide
	} from "@dcloudio/uni-app";

	export default {
		setup() {
			// 封面和用户信息模拟数据
			const coverInfo = ref({
				nickname: '示例用户',
				avatarUrl: 'https://picsum.photos/seed/myavatar/150/150',
				coverUrl: '../../static/images/friendsTopImageTitle.png'
			});
			uni.hideTabBar()

			const store = useStore();



			const config = computed(() => store.getters.getConfig);
			const channelImg = computed(() => config.value?.server?.channelImg || '');

			console.log('channelImg', channelImg.value)
			// 模拟朋友圈数据
			const momentsData = ref([]);
			// 图片全屏预览相关
			const showImageFullscreen = ref(false);
			const currentImage = ref('');

			const openImagePreview = (imageUrl) => {
				currentImage.value = imageUrl;
				showImageFullscreen.value = true;
			};

			const closeImagePreview = () => {
				showImageFullscreen.value = false;
			};

			// 视频全屏播放相关
			const showVideoFullscreen = ref(false);
			const currentVideo = ref('');
			const currentVideoPoster = ref('');

			const openVideoFullscreen = (videoUrl, posterUrl) => {
				currentVideo.value = videoUrl;
				currentVideoPoster.value = posterUrl || '';
				showVideoFullscreen.value = true;
				// 隐藏TabBar
				uni.hideTabBar({
					animation: false
				});

				// 延迟执行，确保DOM更新完成
				setTimeout(() => {
					const videoContext = uni.createVideoContext('fullscreen-video');
					if (videoContext) {
						videoContext.play();

						// 对于安卓设备，自动进入全屏模式
						if (isAndroid.value) {
							console.log('安卓设备，自动进入全屏模式');
							setTimeout(() => {
								videoContext.requestFullScreen({
									direction: 0,
									success: () => {
										console.log('请求全屏成功');
									},
									fail: (err) => {
										console.error('请求全屏失败:', err);
									}
								});
							}, 500);
						}
					}
				}, 100);
			};

			const closeVideoFullscreen = () => {
				const videoContext = uni.createVideoContext('fullscreen-video');
				if (videoContext) {
					// 如果处于全屏状态，先退出全屏
					try {
						videoContext.exitFullScreen();
					} catch (e) {
						console.error('退出全屏失败:', e);
					}

					// 停止播放
					setTimeout(() => {
						videoContext.stop();
					}, 100);
				}

				showVideoFullscreen.value = false;
			};

			const handleVideoFullscreenChange = (e) => {
				console.log('全屏状态变化:', e.detail);
				if (!e.detail.fullScreen) {
					// 如果是从全屏状态退出，只需退出全屏不关闭视频
					if (isAndroid.value) {
						console.log('安卓设备退出全屏');
						// 如果是安卓设备，延迟一小段时间再关闭整个视频界面
						setTimeout(() => {
							closeVideoFullscreen();
						}, 300);
					} else {
						closeVideoFullscreen();
					}
				}
			};

			const handleVideoEnded = (e) => {
				// 获取视频上下文
				const videoContext = uni.createVideoContext(e.target.id);
				// 将视频时间设置为0，回到开始位置
				videoContext.seek(0);
				// 重新播放
				videoContext.play();
			};

			// 检测是否是安卓设备
			const isAndroid = ref(false);
			onMounted(() => {
				getChannelList();
				uni.hideTabBar();
				// 检测平台
				uni.getSystemInfo({
					success: function(res) {
						isAndroid.value = res.platform === 'android';
						console.log('当前平台:', res.platform);
					}
				});
			});

			// 安卓特定处理：监听物理返回键
			const handleAndroidBack = () => {
				if (showVideoFullscreen.value) {
					closeVideoFullscreen();
					return true; // 阻止默认返回行为
				}
				return false;
			};

			// 如果是安卓，添加物理返回键监听
			if (uni.getSystemInfoSync().platform === 'android') {
				uni.addInterceptor('navigateBack', {
					invoke(e) {
						return !handleAndroidBack();
					}
				});
			}
			const getChannelList = async () => {
				try {
					const res = await GetChannelList();
					console.log('频道数据=======》〉》〉》〉', res);

					if (res.data && res.data.code === 0 && res.data.data) {
						// 将接口数据转换为组件需要的格式
						const data = res.data.data.map(item => {
							// 格式化时间
							const formatTime = (dateString) => {
								const date = new Date(dateString);
								const now = new Date();
								const diff = now - date;
								const minutes = Math.floor(diff / (1000 * 60));
								const hours = Math.floor(diff / (1000 * 60 * 60));
								const days = Math.floor(diff / (1000 * 60 * 60 * 24));

								if (minutes < 1) return '刚刚';
								if (minutes < 60) return `${minutes}分钟前`;
								if (hours < 24) return `${hours}小时前`;
								if (days < 7) return `${days}天前`;
								return date.toLocaleDateString();
							};

							// 确定内容类型和媒体URL
							let contentType = 'text';
							let mediaUrl = '';
							let videoPosterUrl = '';

							let imgs = item.Img.map((url) => {
								return {
									type: 'img',
									val: url
								}
							})
							console.log('imgs', imgs)
							let videos = item.Video.map((url) => {
								return {
									type: 'video',
									val: url
								}
							})
							console.log('videos', videos)
							let mediaList = [...imgs, ...videos]
							console.log('mediaList', mediaList,item)
							return {
								id: item.ID,
								user: {
									nickname: item.Title || '用户',
									avatar: config.value.server.channelHeader

								},

								content: {
									mediaList: mediaList,
									type: contentType,
									text: item.String || '',
								},
								timestamp: formatTime(item.CreatedAt)
							};
						});
						console.log(data)
						momentsData.value = data
						console.log('转换后的朋友圈数据:', momentsData.value);
					}
				} catch (error) {
					console.error('获取频道列表失败:', error);
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					});
				}
			}


			return {
				coverInfo,
				momentsData,
				showImageFullscreen,
				currentImage,
				openImagePreview,
				closeImagePreview,
				showVideoFullscreen,
				currentVideo,
				currentVideoPoster,
				openVideoFullscreen,
				closeVideoFullscreen,
				handleVideoFullscreenChange,
				handleVideoEnded,
				isAndroid,
				getChannelList,
				channelImg,
				config
			};
		}
	}
</script>

<style scoped>
	.moments-feed {
		padding-bottom: 20rpx;
		background-color: #f8f8f8;
		position: relative;
		min-height: 100vh;
	}

	/* 封面区域样式 */
	.cover-area {
		position: relative;
		width: 100%;
		height: 500rpx;
		margin-bottom: 60rpx;
		background-color: #ccc;
	}

	.cover-image {
		width: 100%;
		height: 100%;
	}

	/* 朋友圈条目样式 */
	.moment-item {
		padding: 30rpx 30rpx 20rpx;
		background-color: #FFFFFF;
		margin-bottom: 15rpx;
		display: flex;
		flex-direction: column;
	}

	.moment-header {
		display: flex;
		align-items: flex-start;
		margin-bottom: 10rpx;
	}

	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
		/* background-color: #f0f0f0; */
		flex-shrink: 0;
	}

	.user-info {
		flex: 1;
	}

	.nickname {
		font-size: 30rpx;
		color: #576B95;
		font-weight: bold;
		line-height: 1.4;
		display: block;
	}

	.content-text {
		font-size: 28rpx;
		color: #333333;
		line-height: 1.5;
		display: block;
		word-break: break-all;
		margin-top: 5rpx;
	}

	/* 内容区域 */
	.moment-content {
		margin-left: 100rpx;
		margin-bottom: 15rpx;
	}

	/* 媒体容器 */
	.media-container {
		max-width: 100%;
		margin-top: 0;
	}

	/* 单张图片样式 */
	.single-image-display {
		width: 100%;
		height: 200rpx;
		object-fit: cover;
		border-radius: 5rpx;
		background-color: #f0f0f0;
		display: block;
	}

	/* 底部样式 */
	.moment-footer {
		margin-left: 100rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 10rpx;
	}

	.timestamp {
		font-size: 24rpx;
		color: #AAAAAA;
	}

	/* 全屏图片预览样式 */
	.fullscreen-image-container {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #000;
		z-index: 999;
		display: flex;
		align-items: center;
		justify-content: center;
		touch-action: none;
		overflow: hidden;
	}

	.fullscreen-image {
		width: 100%;
		height: 100%;
	}

	.close-btn {
		position: absolute;
		top: 40rpx;
		right: 40rpx;
		width: 80rpx;
		height: 80rpx;
		background-color: rgba(0, 0, 0, 0.5);
		color: white;
		font-size: 60rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	/* 全屏视频播放样式 */
	.fullscreen-video-container {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #000;
		z-index: 1001;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	.fullscreen-video {
		width: 100%;
		height: 100%;
		object-fit: contain;
	}

	.close-btn {
		position: absolute;
		top: 80rpx;
		right: 40rpx;
		width: 80rpx;
		height: 80rpx;
		background-color: rgba(0, 0, 0, 0.7);
		color: white;
		font-size: 60rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
		border: 2rpx solid rgba(255, 255, 255, 0.5);
		box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.3);
	}

	/* 视频封面样式 */
	.video-poster-wrapper {
		position: relative;
		width: 100%;
		height: 400rpx;
		border-radius: 5rpx;
		overflow: hidden;
	}

	.video-poster-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.play-button {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 100rpx;
		height: 100rpx;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.play-icon {
		width: 0;
		height: 0;
		border-top: 20rpx solid transparent;
		border-left: 30rpx solid white;
		border-bottom: 20rpx solid transparent;
		margin-left: 10rpx;
	}

	/* 全屏图片预览容器添加阻止滑动 */
	.fullscreen-image-container {
		touch-action: none;
		overflow: hidden;
	}

	/* 安卓特定底部退出按钮 */
	.android-exit-btn {
		position: fixed;
		bottom: 40rpx;
		left: 50%;
		transform: translateX(-50%);
		padding: 15rpx 40rpx;
		background-color: rgba(0, 0, 0, 0.7);
		color: white;
		border-radius: 50rpx;
		font-size: 28rpx;
		z-index: 9999;
		border: 2rpx solid rgba(255, 255, 255, 0.5);
		box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.3);
	}
</style>