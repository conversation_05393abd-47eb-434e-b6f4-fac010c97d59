<template>
	<NERTCUniPluginSDK-NERTCRemoteViewComponent 
		:mediaType="mediaType" 
		:channel="channel" 
		:userID="userID" 
		:viewID="viewID"
		@onViewLoad="onViewLoad"
		>
	</NERTCUniPluginSDK-NERTCRemoteViewComponent>
</template>

<script>
	export default {
		name: 'NertcRemoteView',
		methods: {
			onViewLoad() {
				this.$emit('onViewLoad');
			}
		},
		props: {
			mediaType: {
				type: String,
				default: 'video'
			},
			channel: {
				type: String,
				default: ""
			},
			userID: {
				type: String,
				default: ""
			},
			viewID: {
				type: String,
				default: ""
			}
		},
	}
</script>

<style>

</style>
