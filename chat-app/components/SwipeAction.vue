<template>
  <view class="chat-list-container">
    <view 
      v-for="(chat, index) in chats" 
      :key="chat.id"
      class="chat-item"
      @touchstart="handleTouchStart(index, $event)"
      @touchmove="handleTouchMove(index, $event)"
      @touchend="handleTouchEnd(index)"
      :style="{ transform: `translateX(${chat.translateX}px)`, transition: chat.isDragging ? 'none' : 'transform 0.2s ease-out' }"
    >
      <!-- 左侧操作按钮（右滑显示） -->
      <view class="left-actions">
        <view class="action-btn mark-read-btn" @click="handleMarkRead(index)">
          <uni-icons type="eye" size="22" color="#fff"></uni-icons>
          <text>{{ chat.unreadCount > 0 ? '标为已读' : '标为未读' }}</text>
        </view>
      </view>

      <!-- 聊天项内容 -->
      <view class="chat-content" @click="handleItemClick(index)">
        <image class="avatar" :src="chat.avatar" mode="aspectFill"></image>
        <view class="chat-info">
          <view class="chat-top">
            <text class="chat-name">{{ chat.name }}</text>
            <text class="chat-time">{{ formatTime(chat.time) }}</text>
          </view>
          <view class="chat-bottom">
            <text class="chat-preview">{{ chat.preview }}</text>
            <view class="chat-status">
              <view v-if="chat.unreadCount > 0" class="unread-badge">{{ chat.unreadCount > 99 ? '99+' : chat.unreadCount }}</view>
              <uni-icons v-if="chat.muted" type="volume-off" size="14" color="#999"></uni-icons>
            </view>
          </view>
        </view>
      </view>

      <!-- 右侧操作按钮（左滑显示） -->
      <view class="right-actions">
        <view class="action-btn delete-btn" @click="handleDelete(index)">
          <uni-icons type="trash" size="22" color="#fff"></uni-icons>
          <text>删除</text>
        </view>
        <view class="action-btn more-btn" @click="showActionSheet(index)">
          <uni-icons type="more" size="22" color="#fff"></uni-icons>
          <text>更多</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import dayjs from 'dayjs';

const props = defineProps({
  chats: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['item-click', 'mark-read', 'delete', 'mute', 'top', 'archive']);

// 当前活动的item索引
const activeIndex = ref(-1);
// 触摸起始位置
const startX = ref(0);
// 最大左滑距离（两个按钮宽度）
const maxLeftSwipe = 160;
// 最大右滑距离（一个按钮宽度）
const maxRightSwipe = 80;

const formatTime = (time) => {
  const today = dayjs().startOf('day');
  const msgDate = dayjs(time);
  
  if (msgDate.isSame(today, 'day')) {
    return msgDate.format('HH:mm');
  } else if (msgDate.isSame(today.subtract(1, 'day'), 'day')) {
    return '昨天';
  } else if (msgDate.isSame(dayjs(), 'year')) {
    return msgDate.format('M月D日');
  } else {
    return msgDate.format('YYYY/M/D');
  }
};

const handleTouchStart = (index, e) => {
  // 如果有其他item打开，先关闭
  if (activeIndex.value !== -1 && activeIndex.value !== index) {
    props.chats[activeIndex.value].translateX = 0;
  }
  
  activeIndex.value = index;
  startX.value = e.touches[0].clientX;
  props.chats[index].isDragging = true;
};

const handleTouchMove = (index, e) => {
  const deltaX = e.touches[0].clientX - startX.value;
  let newX = props.chats[index].translateX + deltaX;
  
  // 限制滑动范围
  if (newX > maxRightSwipe) newX = maxRightSwipe;
  if (newX < -maxLeftSwipe) newX = -maxLeftSwipe;
  
  props.chats[index].translateX = newX;
  startX.value = e.touches[0].clientX;
};

const handleTouchEnd = (index) => {
  props.chats[index].isDragging = false;
  const currentX = props.chats[index].translateX;
  const threshold = 30; // 滑动阈值
  
  // 自动吸附逻辑
  if (currentX > threshold) {
    // 右滑超过阈值，显示左侧按钮
    props.chats[index].translateX = maxRightSwipe;
  } else if (currentX < -threshold) {
    // 左滑超过阈值，显示右侧按钮
    props.chats[index].translateX = -maxLeftSwipe;
  } else {
    // 未超过阈值，恢复原位
    props.chats[index].translateX = 0;
    activeIndex.value = -1;
  }
};

const resetPosition = (index) => {
  props.chats[index].translateX = 0;
  activeIndex.value = -1;
};

const handleItemClick = (index) => {
  if (props.chats[index].translateX !== 0) {
    resetPosition(index);
    return;
  }
  emit('item-click', index);
};

const handleMarkRead = (index) => {
  emit('mark-read', index);
  resetPosition(index);
};

const handleDelete = (index) => {
  uni.showModal({
    title: '删除聊天',
    content: '将同时删除聊天记录',
    confirmText: '删除',
    confirmColor: '#FA5151',
    success: (res) => {
      if (res.confirm) {
        emit('delete', index);
      }
    }
  });
  resetPosition(index);
};

const showActionSheet = (index) => {
  uni.showActionSheet({
    title: props.chats[index].name,
    itemList: [
      props.chats[index].isTop ? '取消置顶' : '置顶聊天',
      props.chats[index].muted ? '取消静音' : '静音',
      '标为未读',
      '删除',
      '免打扰'
    ],
    success: (res) => {
      switch (res.tapIndex) {
        case 0: // 置顶/取消置顶
          emit('top', index);
          break;
        case 1: // 静音/取消静音
          emit('mute', index);
          break;
        case 2: // 标为未读
          emit('mark-read', index, true);
          break;
        case 3: // 删除
          handleDelete(index);
          break;
      }
    }
  });
  resetPosition(index);
};
</script>

<style scoped>
.chat-list-container {
  background-color: #f5f5f5;
}

.chat-item {
  position: relative;
  height: 84px;
  display: flex;
  background-color: #fff;
  margin-bottom: 1px;
}

.left-actions, .right-actions {
  display: flex;
  height: 100%;
}

.left-actions {
  position: absolute;
  left: -80px;
  width: 80px;
}

.right-actions {
  position: absolute;
  right: -160px;
  width: 160px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.action-btn text {
  font-size: 12px;
  color: #fff;
  margin-top: 4px;
}

.mark-read-btn {
  background-color: #10AEFF;
}

.delete-btn {
  background-color: #FA5151;
}

.more-btn {
  background-color: #C9C9C9;
}

.chat-content {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px;
  box-sizing: border-box;
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  margin-right: 12px;
}

.chat-info {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.chat-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-name {
  font-size: 16px;
  color: #000;
  font-weight: 500;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-time {
  font-size: 12px;
  color: #999;
}

.chat-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-preview {
  font-size: 14px;
  color: #999;
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-status {
  display: flex;
  align-items: center;
}

.unread-badge {
  background-color: #FA5151;
  color: #fff;
  font-size: 12px;
  min-width: 18px;
  height: 18px;
  line-height: 18px;
  border-radius: 9px;
  text-align: center;
  padding: 0 4px;
  box-sizing: border-box;
  margin-left: 4px;
}
</style>