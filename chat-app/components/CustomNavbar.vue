<template>
  <view class="custom-navbar">
    <!-- 左侧标题 -->
    <text class="title">{{ title }}</text>

    <!-- 中间留空 -->
    <view class="spacer"></view>

    <!-- 右侧图标 -->
    <view class="icons">
      <image class="icon" src="/static/search.webp" @click="handleSearch" />
      <image class="icon add-icon" src="/static/add.webp" @click.stop="toggleDropdown" />
    </view>

    <!-- 下拉菜单 -->
    <view class="dropdown-container" v-if="isShow" @click.stop>
      <view class="triangle"></view>
      <view class="dropdown-content">
		
		  
        <view class="dropdown-item" v-if="config.server?.userADDFriend||config.server?.userADGroup" @click="handleAddFriend">加好友/群</view>
        <view class="dropdown-item" @click="handleCreateGroup">发起群聊</view>
        <view class="dropdown-item" @click="handleScan">扫一扫</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted,computed } from 'vue'
import { useStore } from 'vuex';
defineProps({
  title: {
    type: String,
    default: '页面标题'
  }
})

const isShow = ref(false)
const store = useStore();
// 点击外部关闭下拉
const handleClickOutside = (e) => {
  if (isShow.value) {
    isShow.value = false
  }
}
const config = computed(() => store.getters.getConfig);
onMounted(() => {
  // 监听全局点击
  // document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

const toggleDropdown = () => {
  isShow.value = !isShow.value
}

const handleSearch = () => {
  uni.navigateTo({
    url: '/pages/search/search'
  });
}

const handleAddFriend = () => {
  isShow.value = false
  /* uni.showToast({
    title: '加好友/群',
    icon: 'none'
  }) */
  uni.navigateTo({
    url: '/pages/searchContact/index' 
  })
}

const handleCreateGroup = () => {
  isShow.value = false
  uni.showToast({
    title: '发起群聊',
    icon: 'none'
  })
}

const handleScan = () => {
  isShow.value = false
  uni.showToast({
    title: '扫一扫',
    icon: 'none'
  })
}
</script>

<style scoped>
.custom-navbar {
  display: flex;
  align-items: center;
  height: 6vh;
  padding: 0 30rpx;
  background-color: #ffffff;
  position: relative;
  z-index: 100;
}

.title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.spacer {
  flex: 1;
}

.icons {
  display: flex;
  align-items: center;
  width: 132rpx;
  justify-content: space-between;
}

.icon {
  width: 44rpx;
  height: 44rpx;
}

.add-icon {
  margin-right: 10rpx;
}

.dropdown-container {
  position: absolute;
  right: 10rpx;
  top: 88rpx;
  z-index: 101;
}

.dropdown-content {
  background: #FFFFFF;
  box-shadow: 0rpx 14rpx 20rpx 0rpx rgba(106, 106, 106, 0.2);
  border-radius: 20rpx;
  border: 2rpx solid #E6E6E6;
  width: 230rpx;
  overflow: hidden;
}

.triangle {
  position: absolute;
  right: 16rpx;
  top: -24rpx;
  width: 0;
  height: 0;
  border-left: 36rpx solid transparent;
  border-right: 36rpx solid transparent;
  border-bottom: 52rpx solid white;
  z-index: 102;
}

.dropdown-item {
  height: 105rpx;
  line-height: 105rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
}

.dropdown-item:active {
  background-color: #f5f5f5;
}
</style>