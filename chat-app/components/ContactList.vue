<template>
    <view class="contact-list">
        <!-- 单一滚动列表视图 -->
        <scroll-view class="list-scroll" scroll-y @scroll="handleScroll" :show-scrollbar="false"
            :scroll-top="scrollTop">
            <view class="list-content">
                <!-- 单一连续的联系人列表 -->
                <template v-for="(group, groupIndex) in contactGroups" :key="`group-${groupIndex}`">
                    <view class="group-title" :id="getgroupID(group.title)" :data-index="group.title"
                        :class="{ 'active': currentGroup === group.title }">
                        {{ group.title }}
                    </view>
                    <view class="contact-item" v-for="(contact, contactIndex) in group.contacts"
                        :key="`contact-${groupIndex}-${contactIndex}`" @tap="handleContactClick(contact)">
                        <up-avatar shape="circle" size="40" :src="contact.head_img" randomBgColor />
                        <text class="contact-name">{{ contact.Name }}</text>
                    </view>
                </template>

                <!-- 底部统计 -->
                <view class="footer">
                    <text class="footer-text">{{ $t('contacts.totalContacts').replace('{count}', totalContacts) }}</text>
                </view>
            </view>
        </scroll-view>

        <!-- 右侧索引栏 -->
        <view class="index-bar" :class="{ 'small-screen': isSmallScreen, 'touching': isIndexTouching }"
            @touchstart="handleIndexTouchStart" @touchmove="handleIndexTouch" @touchend="handleIndexTouchEnd"
            @touchcancel="handleIndexTouchEnd">
            <view class="index-item" v-for="(letter, index) in availableIndexLetters" :key="index"
                :class="{ 'active': currentGroup === letter }" @tap="handleIndexClick(letter)">
                {{ letter }}
            </view>
        </view>

        <!-- 索引提示 -->
        <view class="index-tip" v-if="showIndexTip">
            {{ currentIndexTip }}
        </view>
    </view>
</template>

<style lang="scss">
.contact-list {
    position: relative;
    height: 100vh;
    background-color: #fff;
    display: flex;
    flex-direction: column;
}

.list-scroll {
    flex: 1;
    position: relative;
    overflow-y: auto;
}

.list-content {
    width: 100%;
    box-sizing: border-box;
    padding-bottom: 100rpx;
    /* 为底部统计预留足够空间 */
}

.group-title {
    padding: 10rpx 20rpx;
    font-size: 26rpx;
    color: #999;
    background-color: #f8f8f8;
    position: sticky;
    top: 0;
    z-index: 1;
    width: 100%;
    box-sizing: border-box;

    &.active {
        background-color: #f0f0f0;
        color: #007AFF;
        font-weight: 500;
        box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    }
}

.contact-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    border-bottom: 1rpx solid #eee;
    background: #fff;
    width: 100%;
    box-sizing: border-box;

    .contact-name {
        margin-left: 20rpx;
        font-size: 28rpx;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
    }
}

.index-bar {
    position: fixed;
    right: 15rpx;
    top: 50%;
    transform: translateY(-50%);
    padding: 10rpx 8rpx;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20rpx;
    z-index: 99;
    touch-action: none;
    user-select: none;
    width: 30rpx;
    height: auto;
    box-shadow: 0 0 8rpx rgba(0, 0, 0, 0.1);
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0.95;
    transition: opacity 0.3s ease, transform 0.3s ease;

    &.touching {
        opacity: 1;
        transform: translateY(-50%) scale(1.05);
        box-shadow: 0 0 12rpx rgba(0, 0, 0, 0.2);
    }
}

.index-item {
    width: 100%;
    height: 40rpx;
    line-height: 40rpx;
    font-size: 24rpx;
    color: #666;
    text-align: center;
    padding: 2rpx 0;
    margin: 1rpx 0;
    transition: all 0.2s ease;

    &.active {
        color: #007AFF;
        font-weight: bold;
        transform: scale(1.2);
        background: transparent;
        border-radius: 0;
    }
}

.index-tip {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 120rpx;
    height: 120rpx;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 60rpx;
    color: #fff;
    font-weight: bold;
    z-index: 1000;
    box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.3);
}

.footer {
    padding: 20rpx 0 5rpx 0;
    // padding: 20rpx 0;
    text-align: center;
    // background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f0f0f0;
    border-top: 1rpx solid #eee;
    width: 100%;
    box-sizing: border-box;

    .footer-text {
        font-size: 24rpx;
        color: #666;
        line-height: 34rpx;
        ;
    }
}

/* 适配 iOS 安全区域 */
/* #ifdef APP-PLUS */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
    .list-content {
        padding-bottom: calc(308rpx + env(safe-area-inset-bottom));
    }

    .footer {
        padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    }
}

/* #endif */

/* 适配 android 安全区域 */
/* #ifdef H5 */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
    .list-content {
        padding-bottom: calc(342rpx + env(safe-area-inset-bottom));
    }

    .footer {
        padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    }
}

/* #endif */

.index-bar.small-screen {
    right: 10rpx;
    padding: 8rpx 6rpx;
    width: 24rpx;

    .index-item {
        height: 32rpx;
        line-height: 32rpx;
        font-size: 20rpx;
        margin: 1rpx 0;
    }
}
</style>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { t, getCurrentLanguage } from '@/utils/i18n.js';

// 多语言支持
const currentLang = ref(getCurrentLanguage());

// 监听语言变化事件
uni.$on('languageChanged', (lang) => {
  currentLang.value = lang;
});

// 多语言翻译函数
const $t = (key) => {
  // 访问响应式变量以触发更新
  currentLang.value;
  return t(key);
};

// 定义组件接收的属性
const props = defineProps({
    // 联系人数据，可以从父组件传入
    contactData: {
        type: Array,
        default: () => []
    },
    // 默认选中的分组
    defaultGroup: {
        type: String,
        default: 'A'
    },
    // 是否显示索引栏
    showIndexBar: {
        type: Boolean,
        default: true
    }
});

// 定义组件向外发出的事件
const emit = defineEmits(['select', 'groupChange']);

// 索引字母数组
const indexLetters = ref(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '#']);

// 状态管理
const currentGroup = ref(props.defaultGroup); // 当前选中的组
const showIndexTip = ref(false); // 是否显示索引提示
const currentIndexTip = ref(''); // 当前索引提示内容
const isIndexTouching = ref(false); // 是否正在触摸索引
const scrollTop = ref(0); // 用于控制滚动位置

// 缓存每个分组的位置
const groupPositions = ref({});

// 联系人数据
const contactGroups = ref([]);

// 对于外部传入的联系人数据，使用外部数据
watch(() => props.contactData, (newData) => {
    if (newData && newData.length > 0) {
        contactGroups.value = newData;
    }
}, { immediate: true });

// 计算有效的索引字母（只显示有数据的分组）
const availableIndexLetters = computed(() => {
    // 获取所有有数据的分组标题
    const availableTitles = contactGroups.value.map(group => group.title);

    // 只保留有效的字母索引
    return indexLetters.value.filter(letter => availableTitles.includes(letter));
});

// 监听当前组的变化
watch(currentGroup, (newValue, oldValue) => {
    console.log('当前组变更为:', newValue);

    // 触发groupChange事件
    emit('groupChange', newValue);

    // 如果是在触摸状态下改变分组，滚动到对应位置
    if (isIndexTouching.value && newValue) {
        scrollToLetter(newValue);
    }
}, { immediate: true });

// 计算总联系人数量
const totalContacts = computed(() => {
    try {
        let total = 0;
        if (Array.isArray(contactGroups.value)) {
            contactGroups.value.forEach(group => {
                if (group && group.contacts && Array.isArray(group.contacts)) {
                    total += group.contacts.length;
                }
            });
        }
        return total || 0;
    } catch (error) {
        console.error('计算总人数时出错:', error);
        return 0;
    }
});

// 获取对应组的ID（特殊处理#字符）
const getgroupID = (title) => {
    // #字符在ID中是特殊字符，需要处理
    return title === '#' ? 'group-special' : `group-${title}`;
};

// 更新所有分组的位置
const updateGroupPositions = () => {
    // 获取所有组标题的位置
    uni.createSelectorQuery()
        .selectAll('.group-title')
        .boundingClientRect(rects => {
            if (!rects || rects.length === 0) return;

            const newPositions = {};

            // 获取scroll-view的位置
            uni.createSelectorQuery()
                .select('.list-scroll')
                .boundingClientRect(scrollRect => {
                    if (!scrollRect) return;

                    // 获取当前滚动位置
                    uni.createSelectorQuery()
                        .select('.list-scroll')
                        .scrollOffset(scrollData => {
                            if (!scrollData) return;

                            // 计算每个分组相对于滚动容器的绝对位置
                            rects.forEach(rect => {
                                if (rect && rect.dataset && rect.dataset.index) {
                                    const index = rect.dataset.index;
                                    // 存储绝对滚动位置
                                    newPositions[index] = rect.top - scrollRect.top + scrollData.scrollTop;
                                }
                            });

                            groupPositions.value = newPositions;
                            console.log('更新分组位置:', newPositions);
                        }).exec();
                }).exec();
        }).exec();
};

// 滚动到指定字母位置
const scrollToLetter = (letter) => {
    console.log('滚动到字母:', letter);

    // 如果已经缓存了位置，直接滚动
    if (groupPositions.value[letter] !== undefined) {
        scrollTop.value = groupPositions.value[letter];
        return;
    }

    // 否则需要查询DOM并滚动
    const groupID = getgroupID(letter);

    uni.createSelectorQuery()
        .select(`#${groupID}`)
        .boundingClientRect(rect => {
            if (!rect) return;

            uni.createSelectorQuery()
                .select('.list-scroll')
                .boundingClientRect(scrollRect => {
                    if (!scrollRect) return;

                    uni.createSelectorQuery()
                        .select('.list-scroll')
                        .scrollOffset(scrollData => {
                            if (!scrollData) return;

                            // 计算目标滚动位置
                            const targetTop = rect.top - scrollRect.top + scrollData.scrollTop;
                            // 更新滚动位置
                            scrollTop.value = targetTop;

                            // 缓存位置
                            groupPositions.value[letter] = targetTop;
                        }).exec();
                }).exec();
        }).exec();
};

// 处理滚动事件 - 更新当前显示的分组
let scrollTimer = null;
const handleScroll = (e) => {
    // 如果正在触摸索引，不处理滚动事件
    if (isIndexTouching.value) return;

    // 防抖处理
    if (scrollTimer) clearTimeout(scrollTimer);

    scrollTimer = setTimeout(() => {
        // 获取当前滚动位置
        const currentScrollTop = e.detail.scrollTop;

        // 根据滚动位置查找最近的分组
        let currentLetterFound = null;
        let minDistance = Infinity;

        // 遍历所有缓存的分组位置
        Object.entries(groupPositions.value).forEach(([letter, position]) => {
            // 如果位置小于等于当前滚动位置，且距离最小
            if (position <= currentScrollTop) {
                const distance = currentScrollTop - position;
                if (distance < minDistance) {
                    minDistance = distance;
                    currentLetterFound = letter;
                }
            }
        });

        // 如果找到了对应分组且不是当前分组，更新当前分组
        if (currentLetterFound && currentLetterFound !== currentGroup.value) {
            currentGroup.value = currentLetterFound;
        }

        // 如果没有足够的位置信息，重新获取所有位置
        if (Object.keys(groupPositions.value).length < availableIndexLetters.value.length) {
            updateGroupPositions();
        }
    }, 50);
};

// 处理索引触摸开始
const handleIndexTouchStart = (e) => {
    // 进入触摸状态
    isIndexTouching.value = true;

    // 如果位置缓存不完整，更新位置缓存
    if (Object.keys(groupPositions.value).length < availableIndexLetters.value.length) {
        updateGroupPositions();
    }

    // 触感反馈
    uni.vibrateShort({
        type: 'medium'
    });

    // 处理初始触摸位置
    handleIndexTouch(e);
};

// 处理索引触摸移动
const handleIndexTouch = (e) => {
    if (!isIndexTouching.value) return;

    const touch = e.touches[0];
    showIndexTip.value = true;

    // 获取索引栏位置
    uni.createSelectorQuery()
        .select('.index-bar')
        .boundingClientRect(data => {
            if (!data) return;

            // 计算触摸位置对应的索引
            const touchY = Math.min(Math.max(touch.clientY - data.top, 0), data.height);
            const index = Math.min(
                Math.max(Math.floor((touchY / data.height) * availableIndexLetters.value.length), 0),
                availableIndexLetters.value.length - 1
            );

            const letter = availableIndexLetters.value[index];
            if (letter && currentGroup.value !== letter) {
                // 更新当前字母和提示
                currentIndexTip.value = letter;
                currentGroup.value = letter;

                // 轻触感反馈
                uni.vibrateShort({
                    type: 'light'
                });
            }
        })
        .exec();
};

// 处理索引触摸结束
const handleIndexTouchEnd = () => {
    // 隐藏提示
    showIndexTip.value = false;

    // 延迟退出触摸状态
    setTimeout(() => {
        isIndexTouching.value = false;
    }, 300);
};

// 处理索引点击
const handleIndexClick = (letter) => {
    // 设置当前组
    currentGroup.value = letter;
    currentIndexTip.value = letter;
    showIndexTip.value = true;

    // 临时设置为触摸状态，触发滚动
    isIndexTouching.value = true;

    // 滚动到对应字母位置
    scrollToLetter(letter);

    // 触感反馈
    uni.vibrateShort({
        type: 'light'
    });

    // 延迟隐藏提示和退出触摸状态
    setTimeout(() => {
        showIndexTip.value = false;

        setTimeout(() => {
            isIndexTouching.value = false;
        }, 300);
    }, 500);
};

// 处理联系人点击
const handleContactClick = (contact) => {
	console.log('点击联系人:', contact);
    // 触发select事件
	uni.navigateTo({
	  url: `/pages/friend/info?Friend=${contact.Friend}`
	})
    console.log('点击联系人:', contact);
    // 触发select事件
    emit('select', contact);
};

// 添加计算属性判断是否为小屏幕
const isSmallScreen = ref(false);

// 初始化处理
onMounted(() => {
    // 获取系统信息
    uni.getSystemInfo({
        success: (res) => {
            console.log('系统信息:', res.platform, res.screenHeight);

            // 根据屏幕高度判断是否为小屏幕
            isSmallScreen.value = res.screenHeight < 700;

            // 根据屏幕尺寸调整索引字母
            if (res.screenHeight < 700) {
                indexLetters.value = ['A', 'B', 'C', 'D', 'E', 'F', 'J', 'L', 'M', 'P', 'S', 'W', 'X', 'Y', 'Z', '#'];
            }

            // 确保默认组在可用索引中
            if (!availableIndexLetters.value.includes(currentGroup.value) && availableIndexLetters.value.length > 0) {
                currentGroup.value = availableIndexLetters.value[0];
            }
        }
    });

    // 延迟获取所有分组位置
    setTimeout(() => {
        updateGroupPositions();
    }, 300);
});

// 对外暴露的方法
defineExpose({
    // 切换到指定分组
    switchToGroup: (letter) => {
        if (availableIndexLetters.value.includes(letter)) {
            isIndexTouching.value = true;
            currentGroup.value = letter;

            // 滚动到对应分组
            scrollToLetter(letter);

            // 延迟退出触摸状态
            setTimeout(() => {
                isIndexTouching.value = false;
            }, 500);

            return true;
        }
        return false;
    },
    // 获取当前分组
    getCurrentGroup: () => currentGroup.value,
    // 获取所有分组
    getAllGroups: () => contactGroups.value,
    // 获取联系人总数
    getContactCount: () => totalContacts.value,
    // 获取可用的索引字母数组
    getAvailableIndexLetters: () => availableIndexLetters.value
});
</script>

<!-- 添加使用说明 -->
<!-- 
使用示例:

<contact-list 
  :contact-data="contactData" 
  default-group="A"
  :show-index-bar="true"
  @select="onContactSelect"
  @group-change="onGroupChange"
/>

其中contactData格式为:
[
  {
    title: 'A',
    contacts: [
      { avatars: '头像URL', names: '联系人名称', ...其他自定义字段 },
      { avatars: '头像URL', names: '联系人名称', ...其他自定义字段 }
    ]
  },
  {
    title: 'B',
    contacts: [...]
  },
  ...
]
-->