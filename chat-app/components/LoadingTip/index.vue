<template>
    <view class="loading-overlay" v-if="show">
        <view class="loading-content" :style="{bottom: bottom}">
            <text>{{ text }}</text>
        </view>
    </view>
</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    text: {
        type: String,
        default: '已发送'
    },
    bottom: {
        type: String,
        default: '40%'
    }
})
</script>

<style lang="scss" scoped>
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
}

.loading-content {
    background-color: rgba(0, 0, 0, 0.4);
    padding: 10rpx 40rpx;
    border-radius: 8rpx;
    text-align: center;
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;

    text {
        color: #FFFFFF;
        font-size: 26rpx;
    }
}
</style> 