<template>
	<view class="container">
		<MomentsFeed />
		<view style="padding:50rpx"></view>
		<CustomTabbarVue :currentTab="'channels'" @tabChange="handleTabChange" />
		<!-- 其他页面内容 -->
	</view>
</template>

<script setup>
import { onMounted } from 'vue';
import { onPageShow } from '@dcloudio/uni-app';
import MomentsFeed from '../../components/MonmentsFeed.vue';
import CustomTabbarVue from '../../components/CustomTabbar.vue';


onMounted(() => {
	uni.hideTabBar();

})

// 使用 onPageShow 替代 onShow
onPageShow(() => {
	uni.hideTabBar();
})

const handleTabChange = (tab) => {
	switch (tab) {
		case 'chats':
			uni.switchTab({
				url: '/pages/chats/chats'
			});
			break;
		case 'contacts':
			uni.switchTab({
				url: '/pages/contacts/contacts'
			});
			break;
		case 'channels':
			// 已经在频道页面，不需要跳转
			break;
		case 'me':
			uni.switchTab({
				url: '/pages/me/me'
			});
			break;
	}
};
</script>

<style>
.container {
	/* 你页面的样式 */
	background-color: #f8f8f8;
	/* 给页面一个背景色 */
	min-height: 100vh;
	/* 添加这行禁用默认触摸行为 */
}
</style>