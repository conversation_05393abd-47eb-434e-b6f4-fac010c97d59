<template>
    <view class="page-container">
        <!-- 状态栏占位 -->
        <view class="status_bar" ></view>

        <!-- 顶部导航栏 -->
        <view class="fixed-top-bar">
            <CustomNavbar :title="$t('chat.title')" />
        </view>

        <!-- 聊天区域 -->
        <scroll-view scroll-y="true" class="chat-list-scroll" :style="scrollViewStyle"
            @scrolltolower="handleScrollToLower" @refresherrefresh="onRefresh" :refresher-enabled="true"
            :refresher-triggered="isrefreshing">
            <!-- 使用 u-swipe-action 包裹列表，并增加 threshold 属性 -->
            <u-swipe-action :threshold="80"> <!-- 增加 threshold 属性，值可以调整 -->
                <!-- 循环渲染聊天项 -->
                <u-swipe-action-item v-for="(chat, index) in chatList" :key="chat.id || index" :options="swipeOptions"
                    @click="handleSwipeClick($event, chat.chatid, index)" :name="chat.chatid" :auto-close="true">
				
                    <!-- 将原有的 chat-item 作为 swipe-action-item 的内容 -->
                    <view class="chat-item" @tap.stop="navigateToChat(chat)">
                        <view class="avatar-container">
                            <image class="avatar" :src="chat.avatar" mode="aspectFill"></image>
                            <view v-if="chat.unreadCount > 0" class="unread-badge">{{ chat.unreadCount > 99 ? '99+' :
                                chat.unreadCount }}</view>
                        </view>
                        <view class="chat-info">
                            <view class="info-top">
                                <text class="nickname">{{ chat.nickname }}</text>
                                <text class="timestamp">{{ formatTimestamp(chat.timestamp) }}</text>
                            </view>
                            <view class="info-bottom">
                                <text class="last-message">{{ lastMessageIsImage(chat.lastMessage) }}</text>
                                <!-- <image v-if="chat.isMuted" class="mute-icon" src="/static/mute.png"></image> -->
                            </view>
                        </view>
                    </view>
                </u-swipe-action-item>
            </u-swipe-action>

            <!-- 加载更多提示 -->
            <view v-if="isLoadingMore" class="loading-more">{{ $t('common.loading') }}</view>
        </scroll-view>


        <!-- 底部标签栏 -->
        <view class="fixed-bottom-bar">
            <custom-tabbar :current-tab="'chats'" @tabChange="switchTab" />
        </view>
		<callMessage ref="callMessage1" ></callMessage>
    </view>
</template>

<script setup>
import {
    ref,
    onMounted,
    onUnmounted,
    computed,
	nextTick
} from 'vue'
import {
    onLoad
} from '@dcloudio/uni-app'
import CustomNavbar from '../../components/CustomNavbar.vue' // 请确保路径正确
import CustomTabbar from '../../components/CustomTabbar.vue' // 请确保路径正确

import callMessage from '../call/callMessage.vue' // 请确保路径正确
import { useStore } from 'vuex';
import { clearChatMessages } from '@/utils/db.js';
import { t, getCurrentLanguage } from '@/utils/i18n.js';
// import { uSwipeAction, uSwipeActionItem } from 'uview-plus'
const store = useStore();

// 多语言支持
const currentLang = ref(getCurrentLanguage());

// 监听语言变化事件
uni.$on('languageChanged', (lang) => {
  currentLang.value = lang;
});

// 多语言翻译函数
const $t = (key) => {
  // 访问响应式变量以触发更新
  currentLang.value;
  return t(key);
};
const callMessage1 = ref(null)

// uni.$on('show-call-message', (message) => {
// 	console.log('全局消息处理',message)
// 	if(message.typecode2==9&&message.fromid!=uni.getStorageSync("userId")){
// 		console.log('语音通话请求')
// 		callMessage1.value.show({
// 		  caller: '张三',
// 		  avatar: '/static/avatar.jpg',
// 		  isVideoCall: false,
// 		  message:message
// 		})
// 	}
// })
const statusBarHeight = ref(44) // 默认44rpx
const isrefreshing = ref(false)
const isAndroidBrowser = ref(false) // 在 App/小程序环境通常不需要
const isLoadingMore = ref(false)

const chatList = computed(() => store.getters.getChatList);
console.log('chatList===>>>>>>>')
// console.log(chatList.value)
console.log('chatList===>>>>>>>')

// 计算属性处理最后一调消息是图片的显示
const lastMessageIsImage = computed(() => {
    return (val) =>{
        if(!val) return val

        // 检查是否为JSON格式的语音通话终止消息
        if(typeof val == 'string' && val.startsWith('{') && val.includes('apply')) {
            try {
                const parsed = JSON.parse(val);
                if(parsed.apply != undefined) {
                    return '[语音通话]';
                }
            } catch (e) {
                // 解析失败，继续其他判断
            }
        }

        if(val.includes('.jpg') || val.includes('.png') || val.includes('.jpeg') || val.includes('.gif') || val.includes('.bmp') || val.includes('.svg') || val.includes('.webp') || val.includes('.ico')){
        return '[图片]'
      } else if (val.includes('.mp4') || val.includes('.avi') || val.includes('.mkv') || val.includes('.mov') || val.includes('.wmv') || val.includes('.flv') || val.includes('.mpg') || val.includes('.mpeg') || val.includes('.3gp') || val.includes('.rmvb') || val.includes('.rm')){
        return '[视频]'
      } else if (val.includes('.mp3') || val.includes('.wav') || val.includes('.ogg') || val.includes('.aac')){
        return '[语音]'
      } else {
        return val
      }
    }
});

// --- 定义滑动操作选项 ---
const swipeOptions = ref([
    {
        text: '更多',
        icon: 'more-dot-fill',
        iconSize: '48rpx',
        style: {
            backgroundColor: '#E6E6E6', // 修改背景色为灰色 (uView Plus 的灰色)
            color: 'black',
            fontSize: '24rpx',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '0 20rpx',
            width: '100rpx', // 增加宽度，可以根据需要调整

        },
        action: 'mark'
    },
    {
        text: '删除',
        icon: 'trash-fill',
        iconSize: '48rpx',
        style: {
            // 渐变色 #FD3357 - #FE7057 从上到下
            backgroundImage: 'linear-gradient(to bottom, #FD3357, #FE7057)',
            color: '#ffffff',
            fontSize: '24rpx',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '0 20rpx',
            width: '100rpx', // 增加宽度，保持与“更多”一致
            fontWeight: 'bold'
        },
        action: 'delete'
    }
]);

// --- 处理滑动按钮点击事件 ---
const handleSwipeClick = (event, chatId, index) => {
    console.log('滑动点击:', event, 'Chat ID:', chatId, 'Index:', index);
    const { name, index: buttonIndex } = event;
    const clickedOption = swipeOptions.value[buttonIndex];

    if (clickedOption.action === 'mark') {
        markAsUnread(chatId, index);
    } else if (clickedOption.action === 'delete') {
        deleteChat(chatId, index);
    }
};

// --- 标记未读逻辑 ---
const markAsUnread = (chatId, index) => {
    console.log(`标记 ID 为 ${chatId} 的聊天为未读`);
    const chatItem = chatList.value[index];
    if (chatItem) {
        chatItem.unreadCount = chatItem.unreadCount > 0 ? 0 : 1;
        uni.showToast({ title: chatItem.unreadCount > 0 ? '标记为未读' : '标记为已读', icon: 'none' });
    }
};

// --- 删除聊 ---
const deleteChat = async (chatId, index) => {
    uni.showModal({
        title: $t('common.confirm'),
        content: $t('chatInfo.confirmClearHistory'),
        success: async (res) => {
            if (res.confirm) {
                console.log('用户点击确定，开始删除操作');
                try {
                    const deleteResult = await clearChatMessages(chatId);
                    store.commit('delChatItem', {
                        chatid: chatId
                    });
                    // 强制刷新页面数据
                    await nextTick();
                    const updatedChatList = store.getters.getChatList;
                } catch (error) {
                    console.error('删除聊天失败:', error);
                }
            } else if (res.cancel) {
                console.log('用户点击取消');
            }
        }
    });
};


// --- 处理滚动到底部事件 ---
const handleScrollToLower = () => {
    console.log("滚动到底部，加载更多");
    if (isLoadingMore.value) return;
    isLoadingMore.value = true;
    // 模拟加载数据
    setTimeout(() => {
        const moreData = Array.from({ length: 10 }).map((_, i) => ({
            id: Date.now() + i,
            avatar: `https://picsum.photos/seed/chat_more${chatList.value.length + i + 1}/100/100`,
            nickname: `新联系人 ${chatList.value.length + i + 1}`,
            lastMessage: '你好，这是加载的更多消息！',
            timestamp: Date.now() - 1000 * 60 * (Math.random() * 60 * 24),
            unreadCount: Math.floor(Math.random() * 3),
            isMuted: Math.random() > 0.8
        }));
        chatList.value.push(...moreData);
        isLoadingMore.value = false;
        console.log("加载完成，当前列表长度:", chatList.value.length);
    }, 1500);
}

// --- 计算属性和生命周期钩子 ---
const NAV_BAR_HEIGHT = 88 // 导航栏高度 (rpx)
const BOTTOM_BAR_HEIGHT = 110 // 底部栏高度 (rpx)

// 计算scroll-view样式
const scrollViewStyle = computed(() => {
    const topHeight = NAV_BAR_HEIGHT + statusBarHeight.value;
    const bottomHeight = BOTTOM_BAR_HEIGHT;
    return {
        position: 'fixed',
        top: `${topHeight}rpx`,
        bottom: `${bottomHeight}rpx`,
        left: '0',
        right: '0',
        backgroundColor: '#FFFFFF'
    }
})

onMounted(() => {
    uni.hideTabBar() // 如果你使用的是自定义 TabBar
    const sysInfo = uni.getSystemInfoSync()
    statusBarHeight.value = Math.ceil((sysInfo.statusBarHeight || 0) * (750 / sysInfo.windowWidth))

    // 监听聊天列表更新事件（这个事件监听器可能是多余的，因为我们已经在deleteChat函数中处理了删除）
    uni.$on('chatListUpdated', (data) => {
        console.log('收到聊天列表更新事件:', data);
        if (data.action === 'delete' && data.chatid) {
            console.log(`通过事件删除聊天 chatid: ${data.chatid}`);
            // 这里可能会导致重复删除，先注释掉
            // store.commit('delChatItem', {
            //     chatid: data.chatid
            // });
        }
    });
})

// 页面卸载时移除事件监听
onUnmounted(() => {
    uni.$off('chatListUpdated');
})

// --- 下拉刷新逻辑 ---
const onRefresh = () => {
    console.log("下拉刷新")
    isrefreshing.value = true
    setTimeout(() => {
        // 刷新完成后，停止刷新状态
        isrefreshing.value = false
    }, 1500) // 1.5秒后停止刷新
}

// --- 格式化时间戳 ---
// todo: 待优化
const formatTimestamp = (timestamp) => {
    const now = new Date();
    const msgDate = new Date(timestamp);
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterdayStart = new Date(todayStart.getTime() - 24 * 3600 * 1000);

    if (msgDate >= todayStart) {
        const hours = msgDate.getHours().toString().padStart(2, '0');
        const minutes = msgDate.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
    } else if (msgDate >= yesterdayStart) {
        return '昨天';
    } else {
        if (msgDate.getFullYear() === now.getFullYear()) {
            const month = (msgDate.getMonth() + 1).toString().padStart(2, '0');
            const day = msgDate.getDate().toString().padStart(2, '0');
            return `${month}/${day}`;
        } else {
            const year = msgDate.getFullYear();
            const month = (msgDate.getMonth() + 1).toString().padStart(2, '0');
            const day = msgDate.getDate().toString().padStart(2, '0');
            return `${year}/${month}/${day}`;
        }
    }
}

// --- 跳转到聊天页面 ---
/* const navigateToChat = (item) => {
    const userId = uni.getStorageSync("userId")
    const toid = item.toid == userId ? item.fromid : item.toid
    // 立即更新未读消息状态为0
    if (item.unreadCount > 0) {
        store.commit('updateUnreadCount', { chatId: item.chatid, count: 0 });
    }
    // 设置当前聊天ID，确保进入聊天页面时不会增加未读数
    uni.setStorageSync('currentChatId', item.chatid);
    if (item.typecode == 1) {
        uni.navigateTo({
            url: `/pages/conversation/conversation?chatid=${item.chatid}&toId=${toid}&nickname=${encodeURIComponent(item.nickname)}&avatar=${encodeURIComponent(item.avatar)}`
        })
    } else {
        const sourceTypecode = item.typecode == 2 ? 2 : 1; // 2为群聊，1为好友
        uni.navigateTo({
            url: `/pages/conversation/conversation?chatid=${item.chatid}&toId=${item.chatid}&sourceTypecode=${sourceTypecode}&nickname=${encodeURIComponent(item.nickname)}&avatar=${encodeURIComponent(item.avatar)}`
        })
    }
} */

/**
 * @param {object} item - 包含聊天信息
 */
const navigateToChat = (item) => {
    const CHAT_TYPE_PRIVATE = 1; // 私聊
    const CHAT_TYPE_GROUP = 2;   // 群聊
    const userId = uni.getStorageSync("userId");
    // 更新未读数和当前聊天ID
    if (item.unreadCount > 0) {
        store.commit('updateUnreadCount', { chatId: item.chatid, count: 0 });
    }
    // 设置当前聊天ID，确保进入聊天页面时不会增加未读数
    uni.setStorageSync('currentChatId', item.chatid);
    const params = {
        chatid: item.chatid,
        nickname: encodeURIComponent(item.nickname),
        avatar: encodeURIComponent(item.avatar)
    };
    if (item.typecode === CHAT_TYPE_PRIVATE) {
        params.toId = item.toid === userId ? item.fromid : item.toid;
    } else {
        params.toId = item.chatid;
        params.sourceTypecode = item.typecode === CHAT_TYPE_GROUP ? 2 : 1; 
    }
    const queryString = Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join('&');
    uni.navigateTo({
        url: `/pages/conversation/conversation?${queryString}`
    });
}
// 切换底部标签页
const switchTab = (tab) => {
    console.log('切换标签页:', tab);
    const routes = {
        chats: '/pages/chats/chats',
        contacts: '/pages/contacts/contacts',
        channels: '/pages/channels/channels',
        me: '/pages/me/me'
    };

    if (routes[tab]) {
        uni.switchTab({
            url: routes[tab],
            success: () => {
                console.log('页面跳转成功:', routes[tab]);
            },
            fail: (err) => {
                console.error('页面跳转失败:', err);
                uni.showToast({
                    title: '页面跳转失败',
                    icon: 'none',
                    duration: 2000
                });
            }
        });
    } else {
        console.error('未知的标签页:', tab);
    }
};

</script>

<style lang="scss" scoped>
/* 全局安全区域变量 */
:root {
    --status-bar-height: 44rpx;
    /* 默认值 */
    --safe-area-bottom: 0rpx;
    /* 默认值 */
}

/* CSS变量支持检测 */
@supports (top: env(safe-area-inset-top)) {
    :root {
        /* 根据实际安全区域计算rpx值 */
        --status-bar-height: calc(env(safe-area-inset-top) * (750 / var(--window-width, 750)) * 1rpx);
        --safe-area-bottom: calc(env(safe-area-inset-bottom) * (750 / var(--window-width, 750)) * 1rpx);
    }
}

.page-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    background-color: #ededed;
}

.status_bar {
    height: var(--status-bar-height);
    width: 100%;
    background-color: white;
    /* 状态栏背景色 */
    position: fixed;
    /* 固定状态栏背景 */
    top: 0;
    left: 0;
    right: 0;
    z-index: 101;
    /* 比导航栏高一级 */
}

.fixed-top-bar {
    position: fixed;
    top: var(--status-bar-height, 44rpx);
    /* 使用CSS变量 */
    left: 0;
    right: 0;
    height: 88rpx;
    /* 导航栏高度 */
    z-index: 100;
    background: #ededed;
    /* 导航栏背景色 */
}

.chat-list-scroll {
    box-sizing: border-box;
    background-color: #FFFFFF;

    /* 列表背景色 */
    // 隐藏滚动条
    &::-webkit-scrollbar {
        width: 0;
        height: 0;
    }
}

.chat-list {
    padding: 0;
}

/* 聊天列表项样式 */
.chat-item {
    display: flex;
    align-items: center;
    padding: 24rpx 30rpx;
    position: relative;
    background-color: #FFFFFF;
    /* 确保背景色 */
    transition: background-color 0.2s;
    width: 100%;
    /* 确保宽度 */
    box-sizing: border-box;

    &:active {
        background-color: #f0f0f0;
    }

    /* 分隔线样式 */
    &::after {
        content: '';
        position: absolute;
        left: 150rpx;
        /* 根据头像和间距调整 */
        right: 0;
        bottom: 0;
        // height: 1rpx;
        background-color: #e5e5e5;
        transform: scaleY(0.5);
    }
}

/* 最后一个 swipe-action-item 里的 chat-item 移除分隔线 */
::v-deep .u-swipe-action-item:last-child .chat-item::after {
    display: none;
}


.avatar-container {
    position: relative;
    margin-right: 24rpx;
}

.avatar {
    width: 96rpx;
    height: 96rpx;
    /* border-radius: 10rpx; */
    /* 将原来的圆角注释或删除 */
    border-radius: 50%;
    /* 添加这行，设置为 50% 即为圆形 */
    background-color: #eee;
    display: block;
}

.unread-badge {
    position: absolute;
    top: -8rpx;
    right: -12rpx;
    background-color: #fa5151;
    color: white;
    font-size: 20rpx;
    line-height: 1.5;
    padding: 2rpx 10rpx;
    border-radius: 18rpx;
    min-width: 36rpx;
    height: 36rpx;
    box-sizing: border-box;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
}

.chat-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;
    min-height: 96rpx;
    padding: 4rpx 0;
}

.info-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8rpx;
}

.nickname {
    font-size: 32rpx;
    color: #000000;
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100% - 120rpx);
    /* 限制最大宽度 */
}

.timestamp {
    font-size: 24rpx;
    color: #b2b2b2;
    margin-left: 20rpx;
    flex-shrink: 0;
    white-space: nowrap;
}

.info-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.last-message {
    font-size: 26rpx;
    color: #999999;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.mute-icon {
    width: 28rpx;
    height: 28rpx;
    margin-left: 10rpx;
    flex-shrink: 0;
}

.loading-more {
    text-align: center;
    color: #999;
    font-size: 24rpx;
    padding: 20rpx 0;
}


.fixed-bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 110rpx;
    /* 底部栏高度 */
    z-index: 100;
    padding-bottom: var(--safe-area-bottom);
    /* 使用CSS变量 */
    background: #f7f7f7;
    /* 底部栏背景色 */
    box-shadow: 0 -1rpx 0 0 #dcdcdc;
    /* 顶部细线 */
}
</style>
