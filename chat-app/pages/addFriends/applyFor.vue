<template>
    <view class="apply-container">
        <view class="status_bar"></view>
        <view class="nav-bar">
            <view class="back-btn" @click="goBack">
                <image class="arrow-left" src="/static/My/arrow-right.png" mode="aspectFit"></image>
            </view>
            <view class="title">申请添加好友</view>
        </view>
        <view class="apply-form">
            <view class="desc">发送添加朋友申请</view>
            <view class="input-section">
                <up-textarea v-model="applyMessage" placeholder="" border="none"></up-textarea>
            </view>
            <view class="desc">设置备注</view>
            <view class="input-section">
                <up-textarea  height="20" v-model="remark" placeholder="" border="none"></up-textarea>
            </view>
        </view>
        <view class="send-btn-wrapper">
            <button class="send-btn" @click="sendApply">发送</button>
        </view>
        <loading-tip :show="showLoading" text="已发送" bottom="30%"></loading-tip>
    </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app'
import { addFriend, setFriend } from '@/api/friend'
import LoadingTip from '@/components/LoadingTip/index.vue'

const userInfo = ref({})
const showLoading = ref(false)

onLoad((options) => {
    userInfo.value = options
    console.log(options)
})

const applyMessage = ref('');
const remark = ref('');

const goBack = () => {
    uni.navigateBack();
};

const sendApply = async () => {
    try {
        showLoading.value = true
        const params = {
            id: Number(userInfo.value.id),
            text: applyMessage.value,
        }
        const setParams = {
            id: Number(userInfo.value.id),
            name: remark.value
        }
		console.log(params)
        const {data: res} = await addFriend(params)
        // const {data: setRes} = await setFriend(setParams)
		console.log(res)
        if(res.code == 0) {
            setTimeout(() => {
                showLoading.value = false
                uni.navigateBack()
            }, 1500)
        } else {
			showLoading.value = false
			if(res.code == 301){
				uni.showToast({
				    title: '已经是好友了',
				    icon: 'none'
				})
				return
			}
            
            uni.showToast({
                title: '添加好友失败',
                icon: 'none'
            })
        }
    } catch (error) {
        console.log(error)
        showLoading.value = false
        uni.showToast({
            title: '添加好友失败',
            icon: 'none'
        })
    }
};
</script>

<style lang="scss" scoped>
::v-deep .nav-bar{
    border-bottom: none !important; 
}
::v-deep .u-textarea{
    background-color: #f8f8f8 !important;
}
::v-deep .u-input{
    background-color: #f8f8f8 !important;
}
.apply-container {
    height: 100vh;
    background-color: #fff;
    overflow: hidden;
    position: relative;
}

.status_bar {
    height: var(--status-bar-height);
    width: 100%;
    background-color: #fff;
}

.nav-bar {
    position: relative;
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-bottom: 1rpx solid #f0f0f0;
    .title{
        color: #FF3366;
    }
}

.back-btn {
    position: absolute;
    left: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    color: #FF3366;
}

.arrow-left {
    width: 26rpx;
    height: 26rpx;
    transform: rotate(180deg);
}

.title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
}

.apply-form {
    padding: 30rpx;
}

.desc {
    padding-left: 20rpx;
    font-size: 28rpx;
    color: #999;
    margin-bottom: 20rpx;
}

.input-section {
    background-color: #fff;
    border-radius: 8rpx;
    margin-bottom: 40rpx;
}

.send-btn-wrapper {
    position: fixed;
    bottom: 200rpx;
    left: 50%;
    transform: translateX(-50%);
    padding: 0;
    width: 100%;
}

.send-btn {
    width: 40%;
    height: 90rpx;
    line-height: 90rpx;
    background: #FF3366;
    color: #fff;
    font-size: 26rpx;
    border-radius: 15rpx;
    border: none;
}

.send-btn:active {
    opacity: 0.9;
}

:deep(.u-input) {
    background-color: #fff;
    padding: 0;
}
</style>