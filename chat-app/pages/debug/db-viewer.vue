<template>
  <view class="container">
    <!-- 状态栏占位 -->
    <view class="status_bar"></view>
    
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="back-btn" @click="goBack">
        <image class="arrow-left" src="/static/My/arrow-right.png" mode="aspectFit"></image>
      </view>
      <view class="title">数据库查看器</view>
    </view>
    
    <!-- 数据库信息区域 -->
    <view class="db-info-area">
      <view class="info-item">
        <text class="info-label">数据库状态:</text>
        <text class="info-value">{{ dbStatus }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">表名:</text>
        <text class="info-value">{{ tableName }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">记录数:</text>
        <text class="info-value">{{ recordCount }}</text>
      </view>
    </view>
    
    <!-- 控制按钮区域 -->
    <view class="control-area">
      <button class="control-btn" @click="refreshData">刷新数据</button>
      <button class="control-btn" @click="generateData">生成测试数据</button>
      <button class="control-btn" @click="clearTable">清空表</button>
      <button class="control-btn" @click="exportDb">导出数据库</button>
    </view>
    
    <!-- 数据显示区域 -->
    <view class="data-area">
      <view class="table-header">
        <view class="th">ID</view>
        <view class="th">类型</view>
        <view class="th">内容</view>
        <view class="th">时间</view>
        <view class="th">操作</view>
      </view>
      
      <scroll-view class="table-body" scroll-y>
        <view v-if="records.length === 0" class="no-data">
          暂无数据
        </view>
        <view v-else class="table-row" v-for="(item, index) in records" :key="index">
          <view class="td id-col">{{ item.id }}</view>
          <view class="td type-col">{{ getTypeText(item.typecode) }}</view>
          <view class="td content-col">{{ truncateText(item.msg) }}</view>
          <view class="td time-col">{{ formatTime(item.t) }}</view>
          <view class="td action-col">
            <text class="action-btn" @click="viewDetail(item)">查看</text>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 详情弹窗 -->
    <uni-popup ref="detailPopup" type="center">
      <view class="detail-popup">
        <view class="detail-header">
          <text class="detail-title">消息详情</text>
          <text class="close-btn" @click="closeDetail">×</text>
        </view>
        <scroll-view class="detail-content" scroll-y>
          <view v-if="currentDetail">
            <view class="detail-item" v-for="(value, key) in currentDetail" :key="key">
              <text class="detail-label">{{ key }}:</text>
              <text class="detail-value">{{ formatValue(value) }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { onShow } from "@dcloudio/uni-app";
import { 
  name, 
  tabName,
  isOpen,
  openDb,
  selectDataList,
  getCount,
  deleteInformationType,
  closeSQL,
  generateTestData
} from '@/utils/db.js';

// 状态变量
const dbStatus = ref('正在加载...');
const tableName = ref('');
const recordCount = ref(0);
const records = ref([]);
const currentDetail = ref(null);

// 弹窗引用
const detailPopup = ref(null);

// 初始化
onMounted(async () => {
  tableName.value = tabName();
  await loadData();
});

// 每次显示页面时刷新数据
onShow(async () => {
  await loadData();
});

// 页面卸载时关闭数据库连接
onUnmounted(() => {
  closeSQL().then(() => {
    console.log('数据库连接已关闭');
  }).catch(err => {
    console.error('关闭数据库连接失败:', err);
  });
});

// 加载数据
async function loadData() {
  try {
    // 检查数据库是否打开
    const isDbOpen = isOpen(name);
    dbStatus.value = isDbOpen ? '已连接' : '未连接';
    
    if (!isDbOpen) {
      await openDb();
      dbStatus.value = '已连接';
    }
    
    // 获取记录数
    const countResult = await getCount(name, tableName.value);
    recordCount.value = countResult[0].num;
    
    // 获取所有记录
    if (recordCount.value > 0) {
      const data = await selectDataList(name, tableName.value, {}, 't', 'desc');
      records.value = data;
    } else {
      records.value = [];
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    uni.showToast({
      title: '加载数据失败',
      icon: 'none'
    });
  }
}

// 刷新数据
async function refreshData() {
  try {
    uni.showLoading({ title: '正在刷新...' });
    await loadData();
    uni.hideLoading();
    uni.showToast({
      title: '数据已刷新',
      icon: 'success'
    });
  } catch (error) {
    uni.hideLoading();
    console.error('刷新数据失败:', error);
    uni.showToast({
      title: '刷新失败',
      icon: 'none'
    });
  }
}

// 生成测试数据
async function generateData() {
  uni.showModal({
    title: '确认生成测试数据',
    content: '将生成20条测试消息数据，确定继续吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '正在生成数据...' });
          const result = await generateTestData();
          await loadData(); // 重新加载数据
          uni.hideLoading();
          uni.showToast({
            title: `成功生成${result.count}条数据`,
            icon: 'success'
          });
        } catch (error) {
          uni.hideLoading();
          console.error('生成测试数据失败:', error);
          uni.showToast({
            title: '生成数据失败',
            icon: 'none'
          });
        }
      }
    }
  });
}

// 清空表
function clearTable() {
  uni.showModal({
    title: '警告',
    content: '确定要清空所有数据吗？此操作不可恢复！',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '正在清空...' });
          await deleteInformationType(name, tableName.value, {});
          await loadData();
          uni.hideLoading();
          uni.showToast({
            title: '数据已清空',
            icon: 'success'
          });
        } catch (error) {
          uni.hideLoading();
          console.error('清空数据失败:', error);
          uni.showToast({
            title: '清空失败',
            icon: 'none'
          });
        }
      }
    }
  });
}

// 导出数据库
function exportDb() {
  const exportPath = '_doc/export/';
  // 确保导出目录存在
  plus.io.resolveLocalFileSystemURL(exportPath, function(entry) {
    copyDatabaseFile();
  }, function() {
    plus.io.requestFileSystem(plus.io.PUBLIC_DOCUMENTS, function(fs) {
      fs.root.getDirectory('export', { create: true }, function(dirEntry) {
        copyDatabaseFile();
      });
    });
  });
  
  function copyDatabaseFile() {
    const dbPath = `_doc/${name}.db`;
    const exportDbPath = exportPath + `${name}.db`;
    
    plus.io.resolveLocalFileSystemURL(dbPath, function(entry) {
      plus.io.resolveLocalFileSystemURL(exportPath, function(exportDir) {
        entry.copyTo(exportDir, `${name}.db`, function() {
          console.log('数据库导出成功，路径：' + exportDbPath);
          uni.showToast({
            title: '导出成功',
            icon: 'success'
          });
        }, function(e) {
          console.error('数据库导出失败：' + JSON.stringify(e));
          uni.showToast({
            title: '导出失败',
            icon: 'none'
          });
        });
      });
    }, function(e) {
      console.error('数据库文件不存在：' + JSON.stringify(e));
      uni.showToast({
        title: '数据库文件不存在',
        icon: 'none'
      });
    });
  }
}

// 查看详情
function viewDetail(item) {
  currentDetail.value = item;
  detailPopup.value.open();
}

// 关闭详情
function closeDetail() {
  detailPopup.value.close();
}

// 返回上一页
function goBack() {
  uni.navigateBack();
}

// 辅助函数
function getTypeText(typecode) {
  const types = {
    1: '好友消息',
    2: '群组消息',
    3: '通知消息'
  };
  return types[typecode] || `未知(${typecode})`;
}

function truncateText(text) {
  if (!text) return '';
  return typeof text === 'string' && text.length > 15 ? text.substring(0, 15) + '...' : text;
}

function formatTime(time) {
  if (!time) return '';
  if (typeof time === 'string') {
    // 简单处理一下时间格式
    return time.split(' ')[0] + ' ' + time.split(' ')[1]?.substring(0, 8);
  }
  return time;
}

function formatValue(value) {
  if (value === null || value === undefined) return 'null';
  if (typeof value === 'object') return JSON.stringify(value);
  return value.toString();
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* 状态栏占位 */
.status_bar {
  height: var(--status-bar-height);
  width: 100%;
  background-color: #F9F4F6;
}

/* 顶部导航栏 */
.nav-bar {
  position: relative;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  background-color: #F9F4F6;

  .title {
    color: #FF3366;
    font-size: 36rpx;
    font-weight: 500;
  }
}

.back-btn {
  background-color: white;
  width: 53rpx;
  height: 53rpx;
  border-radius: 50%;
  position: absolute;
  left: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-left {
  width: 26rpx;
  height: 26rpx;
  transform: rotate(180deg);
}

/* 数据库信息区域 */
.db-info-area {
  background-color: #fff;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  margin-bottom: 10rpx;
}

.info-label {
  width: 150rpx;
  color: #666;
}

.info-value {
  flex: 1;
  color: #333;
  font-weight: 500;
}

/* 控制按钮区域 */
.control-area {
  display: flex;
  flex-wrap: wrap;
  padding: 0 30rpx 20rpx;
  justify-content: space-between;
}

.control-btn {
  flex: 1;
  margin: 0 5rpx 10rpx;
  min-width: 160rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 24rpx;
  background-color: #FF3366;
  color: #fff;
  padding: 0 10rpx;
}

/* 数据显示区域 */
.data-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  background-color: #f6f6f6;
  padding: 20rpx 0;
  font-weight: bold;
}

.table-body {
  flex: 1;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #eee;
}

.th, .td {
  flex-shrink: 0;
  padding: 20rpx 10rpx;
  line-height: 1.4;
  font-size: 24rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.id-col {
  width: 80rpx;
}

.type-col {
  width: 140rpx;
}

.content-col {
  flex: 1;
}

.time-col {
  width: 170rpx;
}

.action-col {
  width: 90rpx;
  text-align: center;
}

.action-btn {
  color: #FF3366;
  padding: 6rpx 12rpx;
  font-size: 24rpx;
}

.no-data {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

/* 详情弹窗 */
.detail-popup {
  width: 600rpx;
  max-height: 800rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.detail-title {
  font-size: 32rpx;
  font-weight: 500;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}

.detail-content {
  padding: 20rpx 30rpx;
  max-height: 700rpx;
}

.detail-item {
  margin-bottom: 16rpx;
}

.detail-label {
  color: #666;
  margin-right: 10rpx;
}

.detail-value {
  color: #333;
  word-break: break-all;
}
</style> 