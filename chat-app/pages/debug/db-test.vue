<template>
  <view class="container">
    <view class="status_bar"></view>

    <view class="header">
      <text class="title">数据库调试工具</text>
    </view>

    <scroll-view scroll-y="true" class="content">
      <view class="section">
        <text class="section-title">数据库操作</text>

        <button @click="testDatabase" class="btn">测试数据库连接</button>
        <button @click="createTestData" class="btn">创建测试数据</button>
        <button @click="queryAllData" class="btn">查询所有数据</button>
        <button @click="queryByChatId" class="btn">查询chatId=10002</button>
        <button @click="checkTableStructure" class="btn">检查表结构</button>
        <button @click="clearDatabase" class="btn danger">清空数据库</button>
      </view>

      <view class="section">
        <text class="section-title">测试插入数据</text>

        <view class="input-group">
          <text>消息ID:</text>
          <input v-model="testMessage.id" placeholder="输入消息ID" type="number" />
        </view>

        <view class="input-group">
          <text>发送者ID:</text>
          <input v-model="testMessage.fromid" placeholder="输入发送者ID" />
        </view>

        <view class="input-group">
          <text>接收者ID:</text>
          <input v-model="testMessage.toid" placeholder="输入接收者ID" />
        </view>

        <view class="input-group">
          <text>聊天ID:</text>
          <input v-model="testMessage.chatid" placeholder="输入聊天ID" />
        </view>

        <view class="input-group">
          <text>消息内容:</text>
          <input v-model="testMessage.msg" placeholder="输入消息内容" />
        </view>

        <button @click="insertTestMessage" class="btn">插入测试消息</button>
      </view>

      <view class="section">
        <text class="section-title">查询结果</text>
        <view class="result-area">
          <text class="result-text">{{ resultText }}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import {
  openDb,
  addTab,
  addTabItem,
  selectDataList,
  debugDatabase,
  generateTestData,
  name,
  tabName,
  dropTable
} from '../../utils/db.js'

const resultText = ref('等待操作...')

const testMessage = ref({
  id: Date.now(),
  typecode: 1,
  typecode2: 0,
  fromid: '10001',
  toid: '10002',
  chatid: '10002',
  msg: '这是一条测试消息',
  t: new Date().toISOString(),
  isRedRead: 0,
  idDel: 0
})

// 添加查询特定chatId的功能
const queryByChatId = async () => {
  try {
    resultText.value = '正在查询chatId为10002的消息...'

    const result = await selectDataList(name, tabName(), {chatid: '10002'}, 't', 'desc')
    resultText.value = `查询结果 (${result.length} 条记录):\n${JSON.stringify(result, null, 2)}`
  } catch (error) {
    resultText.value = `查询失败: ${error.message}`
    console.error('查询失败:', error)
  }
}

// 检查数据库表结构
const checkTableStructure = async () => {
  try {
    resultText.value = '正在检查表结构...'

    const result = await new Promise((resolve, reject) => {
      plus.sqlite.selectSql({
        name: name,
        sql: `PRAGMA table_info(${tabName()})`,
        success(e) {
          resolve(e);
        },
        fail(e) {
          reject(e);
        }
      })
    })

    resultText.value = `表结构信息:\n${JSON.stringify(result, null, 2)}`
  } catch (error) {
    resultText.value = `检查表结构失败: ${error.message}`
    console.error('检查表结构失败:', error)
  }
}

// 测试数据库连接
const testDatabase = async () => {
  try {
    resultText.value = '正在测试数据库连接...'

    const result = await debugDatabase()
    resultText.value = `数据库测试结果:\n${JSON.stringify(result, null, 2)}`
  } catch (error) {
    resultText.value = `数据库测试失败: ${error.message}`
    console.error('数据库测试失败:', error)
  }
}

// 创建测试数据
const createTestData = async () => {
  try {
    resultText.value = '正在创建测试数据...'

    const result = await generateTestData()
    resultText.value = `测试数据创建成功:\n${JSON.stringify(result, null, 2)}`
  } catch (error) {
    resultText.value = `创建测试数据失败: ${error.message}`
    console.error('创建测试数据失败:', error)
  }
}

// 查询所有数据
const queryAllData = async () => {
  try {
    resultText.value = '正在查询所有数据...'

    const result = await selectDataList(name, tabName(), {}, 't', 'desc')
    resultText.value = `查询结果 (${result.length} 条记录):\n${JSON.stringify(result.slice(0, 5), null, 2)}`
  } catch (error) {
    resultText.value = `查询数据失败: ${error.message}`
    console.error('查询数据失败:', error)
  }
}

// 插入测试消息
const insertTestMessage = async () => {
  try {
    resultText.value = '正在插入测试消息...'

    console.log('插入的测试消息:', testMessage.value)
    const result = await addTabItem(testMessage.value)
    resultText.value = `消息插入成功:\n${JSON.stringify(result, null, 2)}`

    // 更新消息ID为下一个
    testMessage.value.id = Date.now()
  } catch (error) {
    resultText.value = `插入消息失败: ${error.message}`
    console.error('插入消息失败:', error)
  }
}

// 清空数据库
const clearDatabase = async () => {
  try {
    resultText.value = '正在清空数据库...'

    await dropTable()
    await addTab()

    resultText.value = '数据库清空成功'
  } catch (error) {
    resultText.value = `清空数据库失败: ${error.message}`
    console.error('清空数据库失败:', error)
  }
}

onMounted(async () => {
  try {
    await openDb()
    await addTab()
    resultText.value = '数据库初始化完成'
  } catch (error) {
    resultText.value = `数据库初始化失败: ${error.message}`
    console.error('数据库初始化失败:', error)
  }
})
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.status_bar {
  height: 44rpx;
}

.header {
  text-align: center;
  padding: 20rpx 0;

  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.content {
  height: calc(100vh - 120rpx);
}

.section {
  background-color: white;
  margin-bottom: 20rpx;
  padding: 30rpx;
  border-radius: 10rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
  }
}

.btn {
  width: 100%;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;

  &.danger {
    background-color: #ff3b30;
  }

  &:active {
    opacity: 0.8;
  }
}

.input-group {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  text {
    width: 150rpx;
    font-size: 28rpx;
    color: #333;
  }

  input {
    flex: 1;
    padding: 15rpx;
    border: 1px solid #ddd;
    border-radius: 5rpx;
    font-size: 28rpx;
  }
}

.result-area {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 10rpx;
  min-height: 200rpx;

  .result-text {
    font-size: 24rpx;
    color: #333;
    line-height: 1.5;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
</style>
