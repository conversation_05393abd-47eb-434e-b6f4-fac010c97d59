<template>
  <view class="chat-container">
    <!-- 状态栏占位 -->
    <view class="status_bar"></view>
    <!-- 固定顶部导航栏 -->
    <view class="fixed-header" :style="{ top: statusBarHeight + 'rpx' }">
      <view class="nav-bar">
        <view class="nav-left" @click.once="goBack">
          <image class="nav-icon-img" src="/static/conversation/left.png"></image>
        </view>
        <view class="nav-title">
          <text>{{ chatTitle }}</text>
        </view>
        <view class="nav-right">
          <image class="nav-icon-img" src="/static/conversation/dot.png"></image>
        </view>
      </view>
    </view>

    <!-- 聊天区域 -->
    <scroll-view 
      scroll-y="true" 
      class="chatslist_container" 
      :style="{
        marginTop: statusBarHeight + navBarHeight + 'rpx',
        height: containerHeight
      }" 
      :scroll-into-view="scrollToId" 
      :scroll-with-animation="true" 
      id="chat-list" 
      @scroll="onScroll"
      @scrolltoupper="onScrollToUpper" 
      :scroll-top="scrollTop"
    >

      <!-- 下拉加载提示 -->
      <view class="loading-more" v-if="isLoadingMore">
        <view class="loading-icon"></view>
        <text class="loading-text">加载历史消息中...</text>
      </view>

      <!-- 使用消息列表组件 -->
      <message-list ref="messageListComponent" :messages="displayMessages" :isLoadingMore="isLoadingMore"
        :scrollTop="scrollTop" @copy="onCopyMsg" @delete="onDeleteMsg" @forward="forwardMessage"
        @multiSelect="multiSelectMessages" @openFile="openFile" @playVoice="playVoiceMessage" @viewImage="viewImage"
        @playVideo="handlePlayVideo" />

      <!-- 添加一个锚点元素 -->
      <view id="scroll-anchor" style="height: 1px; width: 100%"></view>

      <!-- 未读消息提示 -->
      <view v-if="hasUnread" class="unread-dot" @tap="handleUnreadClick">
        <view class="dot"></view>
        <text>有新消息</text>
      </view>
    </scroll-view>

    <!-- 输入区域 -->
    <view class="input-wrapper-container" :style="{ bottom: keyboardHeight > 0 ? keyboardHeight + 'rpx' : '0' }">
      <!-- 输入工具栏 -->
      <view class="toolbar">
        <view class="voice-button" @tap="toggleVoiceInput">
          <image class="tool-icon-img" :src="isVoiceMode
            ? '/static/conversation/keyboard.png'
            : '/static/conversation/micph.png'
            "></image>
        </view>

        <!-- 文本输入框/语音按钮 -->
        <view class="input-wrapper" :class="{ expanded: inputMessage && inputMessage.length > 0 }">
          <input v-if="!isVoiceMode" class="message-input" v-model="inputMessage" placeholder="输入消息..."
            confirm-type="send" @confirm="sendTextMessage" @focus="onInputFocus" @blur="onInputBlur"
            @input="inputChange" :adjust-position="false" :cursor-spacing="20" ref="messageInput"
            :focus="inputFocusState" confirm-hold="true" hold-keyboard="true"
            @keyboardheightchange="onKeyboardHeightChange" />
          <view v-else class="voice-input-button" @touchstart.prevent="startRecordVoice"
            @touchend.prevent="stopRecordVoice" @touchcancel.prevent="cancelRecordVoice"
            @touchmove.prevent="onTouchMove">
            <text>{{ isRecording ? "松开 结束" : "按住 说话" }}</text>
          </view>
        </view>

        <!-- 表情按钮 -->
        <view class="emoji-button" @tap="toggleEmojiPanel" :class="{ active: activePanel === 'emoji' }">
          <image class="tool-icon-img" src="/static/conversation/smile.png"></image>
        </view>

        <!-- 发送/加号按钮 -->
        <view class="send-button" v-if="inputMessage && inputMessage.trim().length > 0" @tap="sendTextMessage">
          <image class="send-icon-img" src="/static/conversation/send.png"></image>
        </view>
        <view v-else class="more-button" @tap="toggleMorePanel" :class="{ active: activePanel === 'more' }">
          <image class="tool-icon-img" src="/static/conversation/add.png"></image>
        </view>
      </view>

      <!-- 扩展面板 -->
      <view class="extension-panel" v-if="activePanel === 'emoji'" @touchmove.stop.prevent>
        <!-- 表情面板 -->
        <view class="emoji-panel">
          <scroll-view class="emoji-scroll" scroll-y="true">
            <!-- 表情网格 -->
            <view class="emoji-grid">
              <view class="emoji-item" v-for="(emoji, index) in emojiList" :key="index" @tap="insertEmoji(emoji)">
                <text>{{ emoji }}</text>
              </view>
            </view>
          </scroll-view>
          <!-- 发送按钮 -->
          <view class="emoji-send-area">
            <view class="emoji-send-button" @tap="sendFromEmoji" :class="{ active: inputMessage.trim().length > 0 }">
              <text>发送</text>
            </view>
            <view class="emoji-backspace" @tap="deleteEmoji">
              <image class="emoji-delete-icon" src="/static/conversation/delete.png" />
            </view>
          </view>
        </view>
      </view>
      <view class="extension-panel" v-if="activePanel === 'more'" @touchmove.stop.prevent>
        <!-- 更多功能面板 -->
        <view class="more-panel">
          <view class="more-panel-row">
            <view class="more-panel-item" @tap="chooseImage">
              <view class="more-panel-icon">
                <image class="more-icon-img" src="/static/conversation/photos.png"></image>
              </view>
              <text class="more-panel-title">相册</text>
            </view>
            <view class="more-panel-item" @tap="takePhoto">
              <view class="more-panel-icon">
                <image class="more-icon-img" src="/static/conversation/photoaction.png"></image>
              </view>
              <text class="more-panel-title">拍摄</text>
            </view>
            <view class="more-panel-item" @tap="chooseVideo">
              <view class="more-panel-icon">
                <image class="more-icon-img" src="/static/conversation/video.png"></image>
              </view>
              <text class="more-panel-title">视频</text>
            </view>
            <view class="more-panel-item" @tap="startVoiceCall">
              <view class="more-panel-icon">
                <image class="more-icon-img" src="/static/conversation/micph.png"></image>
              </view>
              <text class="more-panel-title">语音</text>
            </view>
            <view class="more-panel-item" @tap="chooseFile">
              <view class="more-panel-icon">
                <image class="more-icon-img" src="/static/conversation/files.png"></image>
              </view>
              <text class="more-panel-title">文件</text>
            </view>
          </view>
        </view>
      </view>
      <view class="voice-input-panel" v-if="isVoiceMode && isRecording">
        <!-- 语音输入内容 -->
        <view class="voice-recording-indicator">
          <view class="recording-wave-animation" v-if="!isCancelRecording"></view>
          <view class="cancel-recording-icon" v-else>
            <image src="/static/conversation/cancel.png" class="cancel-icon"></image>
          </view>
          <text class="voice-duration">{{ formatRecordDuration(recordDuration) }}</text>
          <text class="voice-tip">{{ isCancelRecording ? "松开手指，取消发送" : "手指上滑，取消发送" }}</text>
        </view>
      </view>
    </view>

    <!-- 在聊天容器末尾添加视频全屏预览组件 -->
    <!-- 视频全屏预览 -->
    <view v-if="showVideoFullscreen" class="fullscreen-video-container" @tap="closeVideoFullscreen">
      <video id="fullscreen-video" :src="currentVideo" :poster="currentVideoPoster" class="fullscreen-video" controls
        autoplay @fullscreenchange="handleVideoFullscreenChange" @ended="handleVideoEnded"></video>
      <view class="close-video-btn" @tap.stop="closeVideoFullscreen">
        <text class="close-video-icon">×</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  nextTick,
  getCurrentInstance,
  defineExpose,
  watch
} from "vue";
import MessageList from "../../components/MessageList.vue";

// 聊天标题
const chatTitle = ref("发财");
// 添加语音模式状态
const isVoiceMode = ref(false);
// 当前播放的语音消息ID
const currentPlayingVoice = ref(null);
// 获取组件实例，用于在 createSelectorQuery 中使用
const instance = getCurrentInstance();
// 消息列表
const messageList = ref([
  {
    id: 1,
    self: false,
    type: "text",
    content: "晚上好啊!",
    time: "2019-02-19 21:33:00",
    timeTag: "2019年2月19日 下午9:33",
    showTimeTag: true,
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 2,
    self: true,
    type: "text",
    content: "我在三里屯MAX等你",
    time: "2019-02-19 21:34:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 3,
    self: true,
    type: "voice",
    duration: 8,
    time: "2019-02-19 21:35:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 4,
    self: false,
    type: "text",
    content: "emmmmm...",
    time: "2019-02-19 21:36:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 5,
    self: false,
    type: "text",
    content: "emmmmm...",
    time: "2019-02-19 21:36:30",
    timeTag: "9:33",
    showTimeTag: true,
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  // 添加更多消息
  {
    id: 6,
    self: true,
    type: "text",
    content: "今天天气不错",
    time: "2019-02-19 21:37:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 7,
    self: false,
    type: "text",
    content: "是啊，很适合出去玩",
    time: "2019-02-19 21:38:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 8,
    self: true,
    type: "text",
    content: "要不要一起去逛街？",
    time: "2019-02-19 21:39:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 9,
    self: false,
    type: "text",
    content: "好啊，去哪里？",
    time: "2019-02-19 21:40:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 10,
    self: true,
    type: "text",
    content: "去国贸吧，听说新开了很多店",
    time: "2019-02-19 21:41:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 11,
    self: false,
    type: "text",
    content: "可以啊，几点去？",
    time: "2019-02-19 21:42:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 12,
    self: true,
    type: "text",
    content: "下午2点怎么样？",
    time: "2019-02-19 21:43:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 13,
    self: false,
    type: "text",
    content: "好的，到时候见",
    time: "2019-02-19 21:44:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 14,
    self: true,
    type: "text",
    content: "别忘了带伞，天气预报说可能会下雨",
    time: "2019-02-19 21:45:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 15,
    self: false,
    type: "text",
    content: "好的，谢谢提醒",
    time: "2019-02-19 21:46:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 16,
    self: true,
    type: "text",
    content: "不客气，明天见",
    time: "2019-02-19 21:47:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 17,
    self: false,
    type: "text",
    content: "明天见",
    time: "2019-02-19 21:48:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 18,
    self: true,
    type: "text",
    content: "晚安",
    time: "2019-02-19 21:49:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 19,
    self: false,
    type: "text",
    content: "晚安",
    time: "2019-02-19 21:50:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 20,
    self: true,
    type: "text",
    content: "做个好梦",
    time: "2019-02-19 21:51:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 1,
    self: false,
    type: "text",
    content: "晚上好啊!",
    time: "2019-02-19 21:33:00",
    timeTag: "2019年2月19日 下午9:33",
    showTimeTag: true,
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 2,
    self: true,
    type: "text",
    content: "我在三里屯MAX等你",
    time: "2019-02-19 21:34:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 3,
    self: true,
    type: "voice",
    duration: 8,
    time: "2019-02-19 21:35:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 4,
    self: false,
    type: "text",
    content: "emmmmm...",
    time: "2019-02-19 21:36:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 5,
    self: false,
    type: "text",
    content: "emmmmm...",
    time: "2019-02-19 21:36:30",
    timeTag: "9:33",
    showTimeTag: true,
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  // 添加更多消息
  {
    id: 6,
    self: true,
    type: "text",
    content: "今天天气不错",
    time: "2019-02-19 21:37:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 7,
    self: false,
    type: "text",
    content: "是啊，很适合出去玩",
    time: "2019-02-19 21:38:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 8,
    self: true,
    type: "text",
    content: "要不要一起去逛街？",
    time: "2019-02-19 21:39:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 9,
    self: false,
    type: "text",
    content: "好啊，去哪里？",
    time: "2019-02-19 21:40:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 10,
    self: true,
    type: "text",
    content: "去国贸吧，听说新开了很多店",
    time: "2019-02-19 21:41:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 11,
    self: false,
    type: "text",
    content: "可以啊，几点去？",
    time: "2019-02-19 21:42:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 12,
    self: true,
    type: "text",
    content: "下午2点怎么样？",
    time: "2019-02-19 21:43:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 13,
    self: false,
    type: "text",
    content: "好的，到时候见",
    time: "2019-02-19 21:44:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 14,
    self: true,
    type: "text",
    content: "别忘了带伞，天气预报说可能会下雨",
    time: "2019-02-19 21:45:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 15,
    self: false,
    type: "text",
    content: "好的，谢谢提醒",
    time: "2019-02-19 21:46:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 16,
    self: true,
    type: "text",
    content: "不客气，明天见",
    time: "2019-02-19 21:47:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 17,
    self: false,
    type: "text",
    content: "明天见",
    time: "2019-02-19 21:48:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 18,
    self: true,
    type: "text",
    content: "晚安",
    time: "2019-02-19 21:49:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 19,
    self: false,
    type: "text",
    content: "晚安",
    time: "2019-02-19 21:50:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
  {
    id: 20,
    self: true,
    type: "text",
    content: "做个好梦",
    time: "2019-02-19 21:51:00",
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  },
]);

// 输入相关
const inputMessage = ref("");
const isRecording = ref(false);
const isCancelRecording = ref(false);
const recordStartTime = ref(0);
const recordDuration = ref(0);
const recordTimer = ref(null);
const touchStartY = ref(0);
const inputFocusState = ref(false); // 默认不自动聚焦

// 面板相关
const activePanel = ref(null); // 可选值: null, 'emoji', 'more', 'voice'
// 微信的表情面板高度约为屏幕的1/3
const panelHeight = ref(500); // rpx单位，微信中约为屏幕高度的1/3
const keyboardHeight = ref(0);
const toolbarHeight = ref(100); // toolbar的固定高度

// 计算输入区域总高度
const inputTotalHeight = computed(() => {
  const baseToolbarHeight = toolbarHeight.value; // 100rpx

  // 表情面板或更多面板显示时
  if (activePanel.value) {
    return baseToolbarHeight + panelHeight.value;
  }

  // 键盘弹出时
  if (keyboardHeight.value > 0) {
    return baseToolbarHeight + keyboardHeight.value;
  }

  // 默认只有工具栏
  return baseToolbarHeight;
});
// 导航栏高度
const navBarHeight = ref(88); // 44px -> 88rpx
const statusBarHeight = ref(0);
// 存储容器实际高度（px单位）
const containerHeight = ref('70vh');

// 滚动相关
const scrollTop = ref(0);
const isScrolling = ref(false);
const isAutoScrolling = ref(false);
const scrollTimer = ref(null);

// 引入节流相关变量
let throttleTimer = null; // 用于节流的计时器
const throttleDelay = 100; // 缩短节流延迟，提高响应速度

// 上拉加载相关
const isLoadingMore = ref(false);
const hasMoreMessages = ref(true);
const oldestMessageId = ref(0);

// 未读消息提示
const hasUnread = ref(false);
const isAtBottom = ref(true);

// 输入框引用
const messageInput = ref(null);

// 表情数据
const emojiList = ref([
  "😀",
  "😃",
  "😄",
  "😁",
  "😆",
  "😅",
  "😂",
  "🤣",
  "😊",
  "😇",
  "🙂",
  "🙃",
  "😉",
  "😌",
  "😍",
  "🥰",
  "😘",
  "😗",
  "😙",
  "😚",
  "😋",
  "😛",
  "😝",
  "😜",
  "🤪",
  "🤨",
  "🧐",
  "🤓",
  "😎",
  "🤩",
  "🥳",
  "😏",
  "😒",
  "😞",
  "😔",
  "😟",
  "😕",
  "🙁",
  "☹️",
  "😣",
  "😖",
  "😫",
  "😩",
  "🥺",
  "😢",
  "😭",
  "😤",
  "😠",
  "😡",
  "🤬",
  "🤯",
  "😳",
  "🥵",
  "🥶",
  "😱",
  "😨",
  "😰",
  "😥",
  "🤗",
]);

// 添加新的响应式变量
const scrollToId = ref("");

// 添加视频相关状态
const showVideoFullscreen = ref(false);
const currentVideo = ref("");
const currentVideoPoster = ref("");

// 添加 MessageList 组件的引用
const messageListComponent = ref(null);

// 显示限制，最多显示50条消息，防止内存过多
const MAX_MESSAGES = 50;

// 计算实际展示的消息列表（限制条数）
const displayMessages = computed(() => {
  if (messageList.value.length <= MAX_MESSAGES) {
    return messageList.value;
  }

  // 只显示最近的MAX_MESSAGES条消息
  return messageList.value.slice(messageList.value.length - MAX_MESSAGES);
});

// 生命周期
onMounted(() => {
  try {
    const systemInfo = uni.getSystemInfoSync();
    const screenWidth = systemInfo.screenWidth || 375; // 获取屏幕宽度，用于rpx转换

    // 设置状态栏高度
    statusBarHeight.value = systemInfo.statusBarHeight * (750 / screenWidth); // px转rpx

    // 初始计算容器高度
    const screenHeight = systemInfo.windowHeight;
    const statusBarHeightPx = systemInfo.statusBarHeight;
    const navBarHeightPx = uni.upx2px(navBarHeight.value);
    const inputHeightPx = uni.upx2px(inputTotalHeight.value);

    // 设置为确切的像素值，避免使用calc()
    containerHeight.value = `${screenHeight - statusBarHeightPx - navBarHeightPx - inputHeightPx}px`;

    // 只在非H5平台监听键盘高度
    if (systemInfo.platform !== "h5") {
      try {
        // 监听键盘高度变化
        uni.onKeyboardHeightChange((res) => {
          keyboardHeight.value = res.height * 2 + 25; // 转换为rpx
          console.log("键盘高度变化:", keyboardHeight.value);

          // 如果键盘收起，且当前没有激活的面板
          if (keyboardHeight.value === 0 && !activePanel.value) {
            // 不执行任何滚动操作
            return;
          }

          // 不再自动关闭面板，让切换面板的函数自己处理
          // 注释掉下面的代码，避免干扰面板切换
          // if (keyboardHeight.value > 0) {
          //   activePanel.value = null;
          // }

          // 延迟执行滚动，等待布局稳定
          setTimeout(() => {
            scrollToBottom(true);
          }, 300);
        });
      } catch (error) {
        console.warn("键盘高度监听失败:", error);
      }
    }

    // 确保获取高度后再执行需要依赖此高度的逻辑
    nextTick(() => {
      scrollToBottom();
    });
  } catch (e) {
    console.error("获取系统信息失败:", e);
    statusBarHeight.value = 44; // 备用值
    nextTick(() => {
      scrollToBottom();
    });
  }
});
onUnmounted(() => {
  // 只在非H5平台移除监听
  if (uni.getSystemInfoSync().platform !== "h5") {
    try {
      uni.offKeyboardHeightChange();
    } catch (error) {
      console.warn("移除键盘高度监听失败:", error);
    }
  }

  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value);
  }
});

// 方法
const onInputFocus = () => {
  // 设置输入框为聚焦状态
  inputFocusState.value = true;

  // 微信的行为是：聚焦输入框时，关闭所有面板
  if (activePanel.value) {
    activePanel.value = null;
  }

  // 确保消息在视图底部可见
  setTimeout(() => {
    scrollToBottom(true);
  }, 300);
};

// 删除表情
const deleteEmoji = () => {
  if (inputMessage.value.length === 0) return;

  try {
    // 更智能的方法处理emoji表情删除
    const stringArray = Array.from(inputMessage.value);
    stringArray.pop();  // 删除最后一个字符（无论是普通字符还是emoji）
    inputMessage.value = stringArray.join('');
  } catch (e) {
    console.error('删除表情失败', e);
    // 兼容性处理，出错时简单删除最后一个字符
    inputMessage.value = inputMessage.value.slice(0, -1);
  }
};

// 监听新消息
const receiveMessage = (message) => {
  messageList.value.push(message);
  if (!isAtBottom.value) {
    hasUnread.value = true;
  } else {
    nextTick(() => scrollToBottom(true));
  }
};

// 切换更多功能面板
const toggleMorePanel = () => {
  // 确保语音模式关闭
  if (isVoiceMode.value) {
    isVoiceMode.value = false;
  }

  const currentKeyboardHeight = keyboardHeight.value;

  if (activePanel.value === "more") {
    // 关闭更多面板，聚焦输入框（就像微信一样）
    activePanel.value = null;

    // 延迟聚焦，避免面板关闭和键盘弹出的动画冲突
    setTimeout(() => {
      inputFocusState.value = true;
      try {
        if (messageInput.value) {
          messageInput.value.focus();
        }
      } catch (e) {
        console.error('输入框聚焦失败', e);
      }
    }, 150);
  } else {
    // 关闭键盘，显示更多面板
    activePanel.value = "more";
    inputFocusState.value = false;

    try {
      if (messageInput.value) {
        messageInput.value.blur();
      }
    } catch (e) {
      console.error('输入框失焦失败', e);
    }

    // 如果键盘是打开的，给它一点时间关闭
    if (currentKeyboardHeight > 0) {
      // 防止面板和键盘高度变化引起的滚动抖动
      setTimeout(() => {
        scrollToBottom(true);
      }, 300);
    } else {
      // 确保内容滚动到底部
      nextTick(() => {
        scrollToBottom(true);
      });
    }
  }
};

// 切换表情面板
const toggleEmojiPanel = () => {
  // 根据微信的交互逻辑：
  // 1. 如果表情面板已经开启，则关闭面板并聚焦输入框
  // 2. 如果更多面板开启，则切换到表情面板
  // 3. 如果键盘开启，则关闭键盘，打开表情面板
  // 4. 如果什么都没有开启，则打开表情面板

  // 确保语音模式关闭
  if (isVoiceMode.value) {
    isVoiceMode.value = false;
  }

  const currentKeyboardHeight = keyboardHeight.value;

  if (activePanel.value === "emoji") {
    // 关闭表情面板，聚焦输入框（就像微信一样）
    activePanel.value = null;

    // 延迟聚焦，避免面板关闭和键盘弹出的动画冲突
    setTimeout(() => {
      inputFocusState.value = true;
      try {
        if (messageInput.value) {
          messageInput.value.focus();
        }
      } catch (e) {
        console.error('输入框聚焦失败', e);
      }
    }, 150);
  } else {
    // 关闭键盘，显示表情面板
    activePanel.value = "emoji";
    inputFocusState.value = false;

    try {
      if (messageInput.value) {
        messageInput.value.blur();
      }
    } catch (e) {
      console.error('输入框失焦失败', e);
    }

    // 如果键盘是打开的，给它一点时间关闭
    if (currentKeyboardHeight > 0) {
      // 防止面板和键盘高度变化引起的滚动抖动
      setTimeout(() => {
        scrollToBottom(true);
      }, 300);
    } else {
      // 确保内容滚动到底部
      nextTick(() => {
        scrollToBottom(true);
      });
    }
  }
};

// 切换语音输入模式
const toggleVoiceInput = () => {
  isVoiceMode.value = !isVoiceMode.value;

  // 微信的行为是：切换到语音模式时，关闭所有面板和键盘
  if (isVoiceMode.value) {
    activePanel.value = null;
    inputFocusState.value = false;
    try {
      if (messageInput.value && typeof messageInput.value.blur === 'function') {
        messageInput.value.blur();
      }
    } catch (e) {
      console.error('输入框失焦失败', e);
    }
  } else {
    // 从语音模式切换到文本模式，微信的行为是聚焦输入框
    inputFocusState.value = true;
    setTimeout(() => {
      try {
        if (messageInput.value && typeof messageInput.value.focus === 'function') {
          messageInput.value.focus();
        }
      } catch (e) {
        console.error('输入框聚焦失败', e);
      }
    }, 100);
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

const onInputBlur = () => {
  // 只改变状态，不执行其他操作，让其他函数来处理面板的状态
  inputFocusState.value = false;
};

const inputChange = () => {
  // 不执行任何操作
};

// 插入表情
const insertEmoji = (emoji) => {
  // 微信的行为是：点击表情时，保持表情面板打开，同时向输入框添加表情
  inputMessage.value += emoji;

  // 没有必要聚焦，因为表情面板打开时，输入框应该保持失焦状态

  // 微信不会主动发送表情，除非点击发送按钮
};

// 发送文本消息
const sendTextMessage = () => {
  if (!inputMessage.value.trim()) return;

  // 创建消息对象
  const newMessage = {
    id: Date.now(),
    self: true,
    type: "text",
    content: inputMessage.value,
    time: new Date().toLocaleTimeString("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
    }),
    avatar: "/static/My/avatar.jpg",
    status: "success",
    unread: true,
  };

  // 添加消息到列表
  messageList.value.push(newMessage);

  // 清空输入框
  inputMessage.value = "";

  // 发送后滚动到底部
  nextTick(() => {
    setTimeout(() => {
      scrollToBottom(true);
    }, 100);
  });
};

// 原始的滚动处理逻辑
const handleScrollEvent = (e) => {
  // 更新滚动位置
  scrollTop.value = e.detail.scrollTop;

  // 判断是否在底部
  const scrollView = e.detail;
  const atBottom = scrollView.scrollHeight - scrollView.scrollTop - scrollView.clientHeight < 20;
  
  // 只在状态变化时更新
  if (atBottom !== isAtBottom.value) {
    isAtBottom.value = atBottom;
    if (atBottom) {
      hasUnread.value = false;
    }
  }

  // 管理滚动状态
  if (!isScrolling.value) {
    isScrolling.value = true;
    
    // 重置计时器
    if (scrollTimer.value) {
      clearTimeout(scrollTimer.value);
      scrollTimer.value = null;
    }
    
    // 设置滚动停止检测
    scrollTimer.value = setTimeout(() => {
      isScrolling.value = false;
    }, 300);
  }
  
  // 关闭子组件菜单
  if (messageListComponent.value?.closeLocalBubbleMenu) {
    messageListComponent.value.closeLocalBubbleMenu();
  }
};

// 节流处理的滚动函数
const onScroll = (e) => {
  // 立即更新scrollTop
  scrollTop.value = e.detail.scrollTop;
  
  // 节流处理
  if (throttleTimer) return;
  
  // 执行完整滚动逻辑
  handleScrollEvent(e);

  // 设置节流计时器
  throttleTimer = setTimeout(() => {
    throttleTimer = null;
  }, 100);
};

// 修改滚动到底部的方法
const scrollToBottom = (force = false) => {
  if (!force && isScrolling.value) return;

  nextTick(() => {
    // 先尝试使用scrollIntoView
    scrollToId.value = "scroll-anchor";
    
    // 备用方法：手动计算和设置scrollTop
    setTimeout(() => {
      try {
        const query = uni.createSelectorQuery();
        query.select('#chat-list').boundingClientRect();
        query.selectAll('.message-item').boundingClientRect();
        
        query.exec((res) => {
          if (res && res[0] && res[1] && res[1].length > 0) {
            const scrollViewHeight = res[0].height;
            const lastMsgBottom = res[1][res[1].length - 1].bottom;
            const scrollViewTop = res[0].top;
            
            // 计算需要滚动的位置
            const newScrollTop = lastMsgBottom - scrollViewTop - scrollViewHeight + 20;
            
            if (newScrollTop > 0) {
              scrollTop.value = newScrollTop;
            }
          }
        });
      } catch(e) {
        console.error('计算滚动位置失败', e);
      }
      
      // 清除ID，允许下次滚动
      setTimeout(() => {
        scrollToId.value = "";
      }, 100);
    }, 50);
  });
};

// 播放语音消息 - 完全按照微信的交互体验实现
const playVoiceMessage = (item) => {
  // 修改语音波形动画样式
  const styleVoiceWave = {
    width: "120rpx",
    height: "120rpx",
    backgroundImage: 'url("/static/conversation/voice_wave.png")', // 修改路径，移除 @ 符号
    backgroundSize: "contain",
    backgroundRepeat: "no-repeat",
    backgroundPosition: "center",
    marginBottom: "20rpx",
  };
  // 格式化录音时长
  const formatRecordDuration = (duration) => {
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes > 0 ? minutes + "'" : ""}${seconds}"`;
  };

  // 添加位置选择
  const chooseLocation = () => {
    // 改为语音录制功能
    isVoiceMode.value = true;
    activePanel.value = null;
    uni.showToast({
      title: "已切换到语音模式，按住说话",
      icon: "none"
    });
  };

  // 点击红点
  const handleUnreadClick = () => {
    scrollToBottom(true);
    hasUnread.value = false;
  };

  // 滚动到顶部时触发加载更多消息
  const onScrollToUpper = () => {
    if (isLoadingMore.value || !hasMoreMessages.value) return;

    // 加载更多历史消息
    loadHistoryMessages();
  };

  // 加载历史消息的函数
  const loadHistoryMessages = () => {
    if (isLoadingMore.value) return;

    isLoadingMore.value = true;

    // 记录当前第一条消息的ID，用于确定加载位置
    if (messageList.value.length > 0) {
      oldestMessageId.value = messageList.value[0].id;
    }

    // 模拟网络请求延迟
    setTimeout(() => {
      // 生成历史消息
      const historicalMessages = [];
      const currentFirstId = oldestMessageId.value || 0;

      // 模拟没有更多消息的情况
      if (currentFirstId < -50) {
        hasMoreMessages.value = false;
        uni.showToast({
          title: "没有更多消息了",
          icon: "none",
        });
        isLoadingMore.value = false;
        return;
      }

      // 生成新的历史消息
      for (let i = 1; i <= 10; i++) {
        const newId = currentFirstId - i;
        historicalMessages.push({
          id: newId,
          self: newId % 2 === 0,
          type: "text",
          content: `历史消息 ${Math.abs(newId)}`,
          time: new Date(Date.now() - i * 60000).toISOString(),
          showTimeTag: i === 1 || i % 5 === 0,
          avatar: "/static/My/avatar.jpg",
          status: "success",
          unread: false,
        });
      }

      // 更新最早消息ID
      oldestMessageId.value = historicalMessages[historicalMessages.length - 1].id;

      // 将历史消息添加到消息列表的顶部
      messageList.value = [...historicalMessages, ...messageList.value];

      // 加载完成
      isLoadingMore.value = false;
    }, 800);
  };

  const onMsgAppear = (item) => {
    if (!item.self && item.unread) {
      item.unread = false;
      // 如需通知父组件或后端，可 emit('read', item)
    }
  };

  // 处理输入框的键盘高度变化事件
  const onKeyboardHeightChange = (e) => {
    const height = e.detail?.height || 0;
    console.log("input keyboard height change:", height);

    // 将高度转换为rpx
    keyboardHeight.value = height * 2 + 30;

    // 如果键盘收起，不做任何操作
    if (height === 0) {
      return;
    }

    // 如果键盘弹出，按照微信的逻辑，关闭所有面板
    if (activePanel.value && height > 0) {
      activePanel.value = null;
    }

    // 键盘弹出时，确保滚动到底部
    setTimeout(() => {
      scrollToBottom(true);
    }, 300);
  };

  // 处理录音长按和上滑取消
  const onTouchMove = (e) => {
    if (!isRecording.value) return;

    const touchMoveY = e.touches[0].clientY;
    const moveDistance = touchStartY.value - touchMoveY;

    // 更灵敏地检测上滑取消，减小阈值
    if (moveDistance > 30) { // 从50减小到30，更容易触发取消
      isCancelRecording.value = true;
    } else {
      isCancelRecording.value = false;
    }
  };

  // 取消录音
  const cancelRecordVoice = () => {
    if (!isRecording.value) return;

    clearInterval(recordTimer.value);

    // #ifdef APP-PLUS || MP-WEIXIN || H5
    const recorderManager = uni.getRecorderManager();
    recorderManager.stop();
    // #endif

    // #ifndef APP-PLUS || MP-WEIXIN || H5
    uni.stopRecord(); // 停止录音
    // #endif

    isRecording.value = false;
    isCancelRecording.value = false;
  };

  // 转发消息
  function forwardMessage(item) {
    // 这里可以弹出联系人选择界面
    uni.showModal({
      title: '转发消息',
      content: '选择要转发的联系人',
      confirmText: '选择联系人',
      success: (res) => {
        if (res.confirm) {
          uni.showToast({
            title: "转发功能待实现",
            icon: "none"
          });
        }
      }
    });
  }

  // 多选消息
  function multiSelectMessages() {
    // 这里可以切换到多选模式
    uni.showToast({
      title: "多选功能待实现",
      icon: "none",
      duration: 2000
    });
  }

  // 打开文件
  function openFile(item) {
    if (!item.filePath) {
      uni.showToast({
        title: "文件路径不存在",
        icon: "none"
      });
      return;
    }

    // 尝试打开文件
    // #ifdef APP-PLUS || H5
    uni.openDocument({
      filePath: item.filePath,
      success: function () {
        console.log('文件打开成功');
      },
      fail: function (err) {
        console.error('文件打开失败', err);
        uni.showToast({
          title: "无法打开此类型文件",
          icon: "none"
        });
      }
    });
    // #endif

    // #ifndef APP-PLUS || H5
    uni.showToast({
      title: "当前平台不支持打开文件",
      icon: "none"
    });
    // #endif
  }

  // 语音通话功能
  const startVoiceCall = () => {
    // 关闭面板
    activePanel.value = null;

    // 显示通话界面的弹窗
    uni.showModal({
      title: '语音通话',
      content: `是否开始与 ${chatTitle.value} 的语音通话？`,
      confirmText: '开始通话',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 模拟通话中的状态
          uni.showLoading({
            title: '正在连接...',
            mask: true
          });

          // 模拟连接延迟
          setTimeout(() => {
            uni.hideLoading();

            // 打开语音通话界面，确保路径正确
            try {
              uni.navigateTo({
                url: '/pages/call/voice-call?name=' + encodeURIComponent(chatTitle.value),
                success: () => {
                  console.log('成功跳转到语音通话页面');
                },
                fail: (err) => {
                  console.error('打开语音通话页面失败', err);
                  // 如果失败，尝试再次检查pages.json配置
                  uni.showToast({
                    title: '打开语音通话失败，请检查页面路径',
                    icon: 'none',
                    duration: 3000
                  });
                }
              });
            } catch (e) {
              console.error('跳转语音通话页面异常', e);
              uni.showToast({
                title: '系统异常，请稍后再试',
                icon: 'none'
              });
            }
          }, 1000);
        }
      }
    });
  };

  // 增加触顶刷新
  const onRefresh = () => {
    if (!hasMoreMessages.value) {
      uni.showToast({
        title: "没有更多消息了",
        icon: "none"
      });
      return;
    }

    // 记录当前第一条消息的ID
    if (messageList.value.length > 0) {
      oldestMessageId.value = messageList.value[0].id;
    }

    // 加载历史消息
    loadHistoryMessages();
  };

  // 处理下拉操作
  const onPullingDown = (e) => {
    // 可以根据下拉距离显示不同的提示
    console.log("下拉刷新，距离：", e.detail.dy);
  };

  // 选择视频
  const chooseVideo = () => {
    // 关闭面板
    activePanel.value = null;

    uni.chooseVideo({
      sourceType: ['album', 'camera'],
      maxDuration: 60, // 最长60秒
      camera: 'back',
      success: (res) => {
        console.log('选择视频成功：', res);

        // 添加视频消息
        messageList.value.push({
          id: Date.now(),
          self: true,
          type: "video",
          content: res.tempFilePath, // 缩略图（这里暂用视频本身路径）
          videoUrl: res.tempFilePath, // 视频路径
          videoDuration: Math.floor(res.duration), // 视频时长（秒）
          thumbUrl: '', // 视频缩略图，可能需要单独生成
          time: new Date().toLocaleTimeString("zh-CN", {
            hour: "2-digit",
            minute: "2-digit",
          }),
          avatar: "/static/My/avatar.jpg",
          status: "success",
          unread: true,
        });

        // 消息发送后滚动到底部
        nextTick(() => {
          scrollToBottom(true);
        });
      },
      fail: (err) => {
        console.error('选择视频失败:', err);
        uni.showToast({
          title: '选择视频失败',
          icon: 'none'
        });
      }
    });
  };

  // 打开视频全屏预览
  const openVideoFullscreen = (item) => {
    if (!item || !item.videoUrl) {
      uni.showToast({
        title: '无效的视频',
        icon: 'none'
      });
      return;
    }

    currentVideo.value = item.videoUrl;
    currentVideoPoster.value = item.thumbUrl || '';
    showVideoFullscreen.value = true;

    // 延迟一下再播放，确保视频元素已渲染
    setTimeout(() => {
      const videoContext = uni.createVideoContext('fullscreen-video');
      if (videoContext) {
        videoContext.play();
        videoContext.requestFullScreen({ direction: 0 });
      }
    }, 300);
  };

  // 关闭视频全屏预览
  const closeVideoFullscreen = () => {
    const videoContext = uni.createVideoContext('fullscreen-video');
    if (videoContext) {
      try {
        videoContext.exitFullScreen();
      } catch (e) {
        console.error('退出全屏失败', e);
      }

      // 停止播放
      videoContext.stop();
    }

    showVideoFullscreen.value = false;
    currentVideo.value = '';
    currentVideoPoster.value = '';
  };

  // 处理视频全屏变化事件
  const handleVideoFullscreenChange = (e) => {
    console.log('视频全屏状态变化:', e);
    // 如果退出全屏，则关闭视频预览
    if (!e.detail.fullScreen) {
      closeVideoFullscreen();
    }
  };

  // 处理视频播放结束事件
  const handleVideoEnded = (e) => {
    const videoContext = uni.createVideoContext(e.target.id);
    videoContext.seek(0);
    // 不自动播放，而是显示封面
    // videoContext.pause();
  };

  // 处理播放视频
  const handlePlayVideo = (item) => {
    openVideoFullscreen(item);
  };

  // 查看图片
  const viewImage = (item) => {
    // 简单的图片预览实现
    uni.previewImage({
      urls: [item.content], // 需要预览的图片http链接列表
      current: item.content // 当前显示图片的http链接
    });
    // 如果 MessageList 菜单是打开的，关闭它
    if (messageListComponent.value && typeof messageListComponent.value.closeLocalBubbleMenu === 'function') {
      messageListComponent.value.closeLocalBubbleMenu();
    }
  }

  // 导出方法
  defineExpose({
    scrollToBottom,
    sendTextMessage,
    receiveMessage,
    onKeyboardHeightChange,
    handlePlayVideo
  });

  // 当输入高度变化时更新容器高度
  watch(inputTotalHeight, () => {
    try {
      const systemInfo = uni.getSystemInfoSync();
      const screenHeight = systemInfo.windowHeight;
      const statusBarHeightPx = systemInfo.statusBarHeight;
      const navBarHeightPx = uni.upx2px(navBarHeight.value);
      const inputHeightPx = uni.upx2px(inputTotalHeight.value);

      // 设置为确切的像素值，避免使用calc()
      containerHeight.value = `${screenHeight - statusBarHeightPx - navBarHeightPx - inputHeightPx}px`;
    } catch (e) {
      console.error('更新容器高度失败', e);
    }
  });
</script>

<style lang="scss" scoped>
.chat-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 状态栏样式 */
.status_bar {
  background-color: white;
}

/* 固定顶部导航栏容器 */
.fixed-header {
  height: 88rpx; // 44px -> 88rpx
  overflow: hidden;
  position: fixed;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
}

/* 导航栏样式 */
.nav-bar {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx; // 44px -> 88rpx
  background-color: #fff;
  position: relative;
  padding: 0 10rpx;
  box-sizing: border-box;

  .nav-left,
  .nav-right {
    width: 88rpx;
    height: 88rpx; // 44px -> 88rpx
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx;
    box-sizing: border-box;

    .nav-icon-img {
      width: 44rpx;
      height: 44rpx;
    }
  }

  .nav-title {
    flex: 1;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    line-height: 88rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

/* 聊天区域 */
.chatslist_container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-anchor: auto;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  background-color: #f5f5f5;
}

/* 输入区域样式 */
.input-wrapper-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  background-color: #f5f5f5;
  transition: bottom 0.25s ease-out;
}

.toolbar {
  height: 100rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  border-top: 1rpx solid #eaeaea;
}

/* 语音按钮、表情按钮、更多按钮 */
.voice-button,
.emoji-button,
.more-button,
.send-button {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.voice-button.active,
.emoji-button.active,
.more-button.active {
  background-color: #e0e0e0;
  border-radius: 8rpx;
}

.tool-icon-img {
  width: 44rpx;
  height: 44rpx;
}

.send-icon-img {
  width: 44rpx;
  height: 44rpx;
}

/* 输入框样式 */
.input-wrapper {
  flex: 1;
  margin: 0 16rpx;
  transition: all 0.3s;
}

.input-wrapper.expanded {
  margin-right: 8rpx;
}

.message-input {
  width: 100%;
  min-height: 72rpx;
  background-color: #fff;
  border-radius: 36rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

/* 语音输入按钮 */
.voice-input-button {
  width: 100%;
  height: 72rpx;
  background-color: #fff;
  border: 1rpx solid #e0e0e0;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-input-button text {
  font-size: 28rpx;
  color: #666;
}

.voice-input-button:active {
  background-color: #e0e0e0;
}

/* 扩展面板 */
.extension-panel {
  position: relative;
  left: 0;
  right: 0;
  background-color: #f5f5f5;
  z-index: 1;
  border-top: none !important;
  margin: 0;
  padding: 0;
}

/* 表情面板 */
.emoji-panel {
  width: 100%;
  height: 500rpx;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  border-top: 1rpx solid #eaeaea;
  overflow: hidden;
}

.emoji-scroll {
  flex: 1;
  width: 100%;
  height: 420rpx;
}

.emoji-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}

.emoji-item {
  width: calc(100% / 7);
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 44rpx;
}

/* 表情面板底部发送区 */
.emoji-send-area {
  height: 80rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  border-top: 1rpx solid #e0e0e0;
  background-color: #f5f5f5;
}

.emoji-send-button {
  width: 120rpx;
  height: 60rpx;
  background-color: #cccccc;
  color: #ffffff;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
}

.emoji-send-button.active {
  background-color: #07c160;
}

.emoji-backspace {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.emoji-delete-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 更多功能面板 */
.more-panel {
  width: 100%;
  height: 500rpx;
  background-color: #f8f8f8;
  padding: 30rpx 20rpx;
  border-top: 1rpx solid #eaeaea;
  box-sizing: border-box;
  overflow-y: auto;
}

.more-panel-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.more-panel-item {
  width: 25%;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

.more-panel-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.more-icon-img {
  width: 56rpx;
  height: 56rpx;
}

.more-panel-title {
  font-size: 24rpx;
  color: #666;
}

/* 语音输入面板 */
.voice-input-panel {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 100rpx;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  z-index: 101;
}

.voice-recording-indicator {
  width: 300rpx;
  height: 300rpx;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.recording-wave-animation {
  width: 120rpx;
  height: 120rpx;
  background-image: url("/static/conversation/voice_wave.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-bottom: 20rpx;
  animation: wave 1s infinite;
}

.cancel-recording-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.cancel-icon {
  width: 80rpx;
  height: 80rpx;
}

@keyframes wave {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }

  50% {
    transform: scale(1);
    opacity: 1;
  }

  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}

.voice-duration {
  font-size: 40rpx;
  color: #fff;
  margin-bottom: 20rpx;
}

.voice-tip {
  font-size: 24rpx;
  color: #ccc;
}

/* 激活状态样式 */
.active {
  color: #ff4f81;
}

.unread-dot {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 120rpx; // 距离底部输入区上方
  background: #fff;
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  padding: 8rpx 24rpx;
  display: flex;
  align-items: center;
  z-index: 20;
  font-size: 26rpx;
  color: #ff4f81;

  .dot {
    width: 16rpx;
    height: 16rpx;
    background: #ff4f81;
    border-radius: 50%;
    margin-right: 12rpx;
  }
}

.msg-unread-dot {
  position: absolute;
  left: 8rpx;
  top: 8rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4f81;
  border-radius: 50%;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(255, 79, 129, 0.15);
}

/* 下拉加载提示 */
.loading-more {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  width: 100%;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f2f2f2;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #999;
}

/* 无更多消息提示 */
.no-more-messages {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 24rpx;
}

/* 下拉刷新样式 */
.refresher-icon {
  width: 32rpx;
  height: 32rpx;
  position: relative;
  border: 3rpx solid #f1f1f1;
  border-radius: 50%;
}

.refresher-dots {
  display: flex;
  justify-content: center;
  margin-top: 10rpx;
}

.refresher-dot {
  width: 8rpx;
  height: 8rpx;
  background-color: #999;
  border-radius: 50%;
  margin: 0 5rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes breath {

  0%,
  100% {
    opacity: 0.3;
  }

  50% {
    opacity: 1;
  }
}

/* 新增呼吸灯效果 */
.breathing-dot {
  animation: breath 1.5s infinite ease-in-out;
}

/* 视频全屏预览样式 */
.fullscreen-video-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.fullscreen-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.close-video-btn {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.close-video-icon {
  font-size: 60rpx;
  color: #fff;
  font-weight: bold;
}
</style>
