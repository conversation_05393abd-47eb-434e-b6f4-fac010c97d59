<template>
    <view class="contact-list">
		<view class="status_bar"></view>
		<!-- 固定顶部导航栏 -->
		<view class="fixed-header" :style="{ top: statusBarHeight + 'rpx' }">
		  <view class="nav-bar">
		    <view class="nav-left" @click.once="goBack">
		      <image class="nav-icon-img" src="/static/conversation/left.png"></image>
		    </view>
		    <view class="nav-title">
		      <text>群聊</text>
		    </view>
		    <view class="nav-right" @click="searchShow">
		      <image class="nav-icon-img" src="/static/search.webp"></image>
		    </view>
		  </view>
		</view>
        <!-- 单一滚动列表视图 -->
        <scroll-view scroll-y="true" class="chatslist_container smooth-scroll" :style="{
          marginTop: statusBarHeight + navBarHeight + 'rpx',
          height: containerHeight,
          'overflow-anchor': 'auto'
        }"  :scroll-with-animation="false" id="chat-list" @scroll="onScroll"
          enable-flex="true" :show-scrollbar="false">
            <view class="list-content">
                <!-- 单一连续的联系人列表 -->
				<view class="contact-item" v-for="(group, groupIndex) in gropList"
					:key="`contact-${groupIndex}-${contactIndex}`" @tap="handleContactClick(group)">
					<up-avatar shape="circle" size="40" :src="group.GroupHeader" randomBgColor />
					<text class="contact-name">{{ group.GroupName }}</text>
				</view>
                <!-- 底部统计 -->
                <view class="footer">
                    <text class="footer-text">共{{ totalContacts }}个群聊</text>
                </view>
            </view>
        </scroll-view>
    </view>
</template>

<style lang="scss">
.contact-list {
    position: relative;
    height: 100vh;
    background-color: #ededed;
    display: flex;
    flex-direction: column;
}
.status_bar {
    width: 100%;
    background-color: #fff;
	height: var(--status-bar-height);
}

/* 固定顶部导航栏容器 */
.fixed-header {
  height: 88rpx; // 44px -> 88rpx
  overflow: hidden;
  position: fixed;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
}
/* 导航栏样式 */
.nav-bar {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx; // 44px -> 88rpx
  background-color: #fff;
  position: relative;
  padding: 0 10rpx;
  box-sizing: border-box;

  .nav-left,
  .nav-right {
    width: 88rpx;
    height: 88rpx; // 44px -> 88rpx
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx;
    box-sizing: border-box;

    .nav-icon-img {
      width: 44rpx;
      height: 44rpx;
    }
  }

  .nav-title {
    flex: 1;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    line-height: 88rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.list-scroll {
    flex: 1;
    position: relative;
    overflow-y: auto;
}

.list-content {
    width: 100%;
    box-sizing: border-box;
    padding-bottom: 100rpx;
	margin-top: 1rpx;
    /* 为底部统计预留足够空间 */
}

.group-title {
    padding: 10rpx 20rpx;
    font-size: 26rpx;
    color: #999;
    background-color: #f8f8f8;
    position: sticky;
    top: 0;
    z-index: 1;
    width: 100%;
    box-sizing: border-box;

    &.active {
        background-color: #f0f0f0;
        color: #007AFF;
        font-weight: 500;
        box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
    }
}

.contact-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    border-bottom: 1rpx solid #eee;
    background: #fff;
    width: 100%;
    box-sizing: border-box;

    .contact-name {
        margin-left: 20rpx;
        font-size: 28rpx;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
    }
}

.index-bar {
    position: fixed;
    right: 15rpx;
    top: 50%;
    transform: translateY(-50%);
    padding: 10rpx 8rpx;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20rpx;
    z-index: 99;
    touch-action: none;
    user-select: none;
    width: 30rpx;
    height: auto;
    box-shadow: 0 0 8rpx rgba(0, 0, 0, 0.1);
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0.95;
    transition: opacity 0.3s ease, transform 0.3s ease;

    &.touching {
        opacity: 1;
        transform: translateY(-50%) scale(1.05);
        box-shadow: 0 0 12rpx rgba(0, 0, 0, 0.2);
    }
}

.index-item {
    width: 100%;
    height: 40rpx;
    line-height: 40rpx;
    font-size: 24rpx;
    color: #666;
    text-align: center;
    padding: 2rpx 0;
    margin: 1rpx 0;
    transition: all 0.2s ease;

    &.active {
        color: #007AFF;
        font-weight: bold;
        transform: scale(1.2);
        background: transparent;
        border-radius: 0;
    }
}

.index-tip {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 120rpx;
    height: 120rpx;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 60rpx;
    color: #fff;
    font-weight: bold;
    z-index: 1000;
    box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.3);
}

.footer {
    padding: 20rpx 0 5rpx 0;
    // padding: 20rpx 0;
    text-align: center;
    // background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    border-top: 1rpx solid #eee;
    width: 100%;
    box-sizing: border-box;

    .footer-text {
        font-size: 24rpx;
        color: #666;
        line-height: 34rpx;
        ;
    }
}

/* 适配 iOS 安全区域 */
/* #ifdef APP-PLUS */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
    .list-content {
        padding-bottom: calc(308rpx + env(safe-area-inset-bottom));
    }

    .footer {
        padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    }
}

/* #endif */

/* 适配 android 安全区域 */
/* #ifdef H5 */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
    .list-content {
        padding-bottom: calc(342rpx + env(safe-area-inset-bottom));
    }

    .footer {
        padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    }
}

/* #endif */

.index-bar.small-screen {
    right: 10rpx;
    padding: 8rpx 6rpx;
    width: 24rpx;

    .index-item {
        height: 32rpx;
        line-height: 32rpx;
        font-size: 20rpx;
        margin: 1rpx 0;
    }
}
</style>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { onShow, onReady, onHide,onLoad } from "@dcloudio/uni-app";
import { getGroupList } from '@/api/group.js'
import store from '@/store'
// 定义组件接收的属性
const props = defineProps({
    // 联系人数据，可以从父组件传入
    contactData: {
        type: Array,
        default: () => []
    },
    // 默认选中的分组
    defaultGroup: {
        type: String,
        default: 'A'
    },
    // 是否显示索引栏
    showIndexBar: {
        type: Boolean,
        default: true
    }
});

// 导航栏高度
const navBarHeight = ref(24); // 44px -> 88rpx
const statusBarHeight = ref(44) 
// 存储容器实际高度（px单位）
const containerHeight = ref('70vh');

// 处理滚动事件 - 更新当前显示的分组
const gropList=ref([])
const show=ref(false)
const totalContacts=ref(0)
const onScroll = (e) => {
	console.log('滚动')
}
const getGroupAllList = async () => {
	try {
		const { data: res } = await getGroupList();
		console.log(res)
		if(res.code == 0) {
			gropList.value=res.data
			totalContacts.value = res.data.length;
			// 将群组列表存储到store中
			store.commit('setGroupList', res.data);
			console.log('群组列表已存储到store:', gropList.value)
		} else {
			uni.showToast({
				title: '获取群组列表失败',
				icon: 'none'
			});
		}
	}
	catch (err) {
		console.log(err)
		uni.showToast({
			title: '获取群组列表失败',
			icon: 'none'
		});
	}
}
// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
const searchShow=()=>{
	show.value=true
}
// 初始化处理
onLoad(() => {
    // 获取系统信息
    getGroupAllList()
});
/**
 * const userId = uni.getStorageSync("userId")
    const toid = item.toid===userId ? item.fromid:item.toid
    // 立即更新未读消息状态为0
    if (item.unreadCount > 0) {
        store.commit('updateUnreadCount', { chatId: item.chatid, count: 0 });
    }
    // 设置当前聊天ID，确保进入聊天页面时不会增加未读数
    uni.setStorageSync('currentChatId', item.chatid);
    uni.navigateTo({
        url: `/pages/conversation/conversation?chatid=${item.chatid}&toId=${toid}&nickname=${encodeURIComponent(item.nickname)}&avatar=${encodeURIComponent(item.avatar)}`
    })
 */
// 点击进入群聊聊天界面
/**
 * 群聊逻辑：
 * 1、每个用户都有一个相同的默认群组，用户登录成功之后后台会创建一个默认群组，用户在进入系统之后会自动加入到这个默认群组中
 * 2、用户点击该群聊，进去群聊页面，进入之后获取该群聊的聊天记录，并显示在页面上（数据存在sqllite中）
 * 3、用户点击发送按钮，发送消息到服务器，服务器会广播给所有的用户，用户收到消息之后，根据消息的chatid，查询聊天记录，并更新聊天记录，并显示在页面上
 * 4、用户发送消息逻辑：
 *          调用sendMessage方法，发送消息到服务器，参数和调用示例：
            *  const params = {
                fromid: uni.getStorageSync('userId'),
                toId: Number(toId.value),
                msg: messageContent,
                typecode: 2, //typecode消息类型1好友消息，2群组消息，3通知消息
                typecode2: typecode2,
            }
                sendMessage(params)
 */
const handleContactClick = (item) =>{
    // 群聊的参数
    const params = {
        toId: Number(item.ID),
        nickname: item.GroupName,
        avatar: item.GroupHeader,
        typecode: 2, //typecode消息类型1好友消息，2群组消息，3通知消息
        chatid: item.ID // 群聊使用群组ID作为chatid
    }
    console.log('群聊参数:', params)
    uni.navigateTo({
        url: `/pages/conversation/conversation?chatid=${item.ID}&toId=${item.ID}&sourceTypecode=2&nickname=${encodeURIComponent(item.GroupName)}&avatar=${encodeURIComponent(item.GroupHeader)}`
    })
   
}
</script>
