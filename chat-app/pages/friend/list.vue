<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="status_bar"></view>
    <!-- <view class="nav-bar">
      <view class="nav-left" @click="goBack">
        <view>
          <up-icon name="arrow-left" color="#FD3357" size="20"></up-icon>
        </view>
      </view>
      <view class="nav-title">新的朋友</view>
	   <button @click="handleAddFriend" class="send-btn" size="mini" type="primary">添加好友</button>
    </view> -->
	<view class="nav-bar">
	    <view class="back-btn" @click="goBack">
	        <image class="arrow-left" src="/static/My/arrow-right.png" mode="aspectFit"></image>
	    </view>
		<view class="title">{{ $t('friendList.title') }}</view>
	    <button v-if="config.server?.userADDFriend" @click="handleAddFriend" class="send-btn" size="mini" type="primary">{{ $t('friendList.addFriend') }}</button>
	</view>

    <!-- 搜索框 -->
<!--    <view class="search-box">
      <view class="search-input">
        <up-input placeholder="搜索手机号" prefixIcon="search" prefixIconStyle="font-size: 22px;color: #909399"></up-input>
      </view>
    </view> -->

    <!-- 添加手机联系人 -->
    <!--     <view class="add-contact" @click="addPhoneContact">
      <view class="add-contact-left">
        <view class="contact-icon">
          <text class="iconfont icon-phone" style="color: #FF3366;">&#xe6b8;</text>
        </view>
        <text class="contact-text">添加手机联系人</text>
      </view>
      <text class="iconfont icon-right">&#xe6a3;</text>
    </view> -->

    <!-- 好友申请列表 -->
	<up-empty :text="$t('friendList.noRequests')" v-if="groupedRequests.length==0">
	</up-empty>
    <view class="friend-requests" v-else>
      <!-- 时间分组 -->
      <block v-for="(group, groupIndex) in groupedRequests" :key="groupIndex">
        <!-- 时间标题 -->
        <view class="time-title">{{ group.title }}</view>

        <!-- 该时间段的好友申请列表 -->
        <view class="request-list">
          <view class="request-item" v-for="item in group.items" :key="item.id">
            <view class="avatar">
              <image :src="item.head_img||'/static/My/avatar.jpg'" mode="aspectFill"></image>
            </view>
            <view class="request-info">
              <view class="name">{{item.name}}</view>
              <view class="message">{{ item.msg }}</view>
            </view>
            <view class="action-btn">
              <button class="btn accept" v-if="item.isRedRead === 0" @click="acceptRequest(item)">{{ $t('friendList.accept') }}</button>
              <!-- <button class="btn reject" v-if="item.isRedRead === 0" @click="rejectRequest(item)">{{ $t('friendList.decline') }}</button> -->
              <text class="status-text" v-if="item.isRedRead === 1">{{ $t('friendList.accepted') }}</text>
              <text class="status-text" v-if="item.isRedRead === 2">{{ $t('friendList.declined') }}</text>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import { useStore } from 'vuex';
import { onShow, onReady, onHide } from "@dcloudio/uni-app";
import { t, getCurrentLanguage } from '@/utils/i18n.js';

  import {
    onLoad
} from '@dcloudio/uni-app'
  import { FriendPass } from '@/api/friend.js';
const store = useStore();

// 多语言支持
const currentLang = ref(getCurrentLanguage());

// 监听语言变化事件
uni.$on('languageChanged', (lang) => {
  currentLang.value = lang;
});

// 多语言翻译函数
const $t = (key) => {
  // 访问响应式变量以触发更新
  currentLang.value;
  return t(key);
};

const config = computed(() => store.getters.getConfig);

// 获取通知消息
const notifyMessages = computed(() => {
  // 从Vuex获取通知消息
  const allMessages = store.state.notifyMsg || [];
  console.log('allMessages', allMessages);
  return Object.values(allMessages).filter(msg => msg.typecode === 3);
});

// 按时间分组好友申请
const groupedRequests = computed(() => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const threeDaysAgo = new Date(today);
  threeDaysAgo.setDate(today.getDate() - 3);

  const groups = [
    { title: '近三天', items: [] },
    { title: '三天前', items: [] }
  ];

  // 如果有真实数据，使用真实数据
  if (notifyMessages.value && notifyMessages.value.length > 0) {
	  console.log('notifyMessages.value',notifyMessages.value)
    notifyMessages.value.forEach(msg => {
      const msgDate = new Date(msg.t);
      if (msgDate >= threeDaysAgo) {
        groups[0].items.push(msg);
      } else {
        groups[1].items.push(msg);
      }
    });
  } else {
    return []
  }
  return groups;
});

/**
 * 接受好友请求
 * @param {Object} item - 好友申请项
 */
const acceptRequest = async (item) => {
  console.log('接受好友请求:', item);

  try {
    // 更新状态为已添加
    await updateRequestStatus(item, 1);

    uni.showToast({
      title: '已添加好友',
      icon: 'success'
    });
  } catch (error) {
    console.error('接受好友请求失败:', error);
  }
};

/**
 * 拒绝好友请求
 * @param {Object} item - 好友申请项
 */
const rejectRequest = async (item) => {
  console.log('拒绝好友请求:', item);

  try {
    // 更新状态为已拒绝
    await updateRequestStatus(item, 2);

    uni.showToast({
      title: '已拒绝',
      icon: 'none'
    });
  } catch (error) {
    console.error('拒绝好友请求失败:', error);
  }
};

const handleAddFriend = () => {

  uni.navigateTo({
    url: '/pages/searchContact/index' 
  })
}
/**
 * 更新好友请求状态
 * @param {Object} item - 好友申请项
 * @param {number} status - 状态值 (1: 已添加, 2: 已拒绝)
 */
const updateRequestStatus = async (item, status) => {
  try {
    console.log('处理好友申请:', item);
    
    // 调用API处理好友申请
    const res = await FriendPass(item.id);
    console.log('API调用成功:', res);
    
    // API调用成功后，更新本地store中的消息状态
    const currentNotifyMsg = store.state.notifyMsg || [];
    
    // 找到对应的消息并更新状态
    const updatedNotifyMsg = currentNotifyMsg.map(msg => {
      if (msg.id === item.id) {
        return {
          ...msg,
          isRedRead: status
        };
      }
      return msg;
    });
    
    // 更新store中的notifyMsg
    store.state.notifyMsg = updatedNotifyMsg;
    uni.setStorageSync('notifyMsg', updatedNotifyMsg);
    
    console.log('本地状态更新成功');
    
  } catch (error) {
    console.error('处理好友申请失败:', error);
    
    // 显示错误提示
    uni.showToast({
      title: $t('friendList.operationFailed'),
      icon: 'none'
    });
  }
};

// 添加手机联系人
const addPhoneContact = () => {
  uni.showToast({
    title: $t('common.loading'),
    icon: 'none'
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 页面加载时执行
onMounted(() => {
  console.log('页面已加载');

  // 如果没有测试数据，添加一些测试数据
  /*  if (!notifyMessages.value || notifyMessages.value.length === 0) {
     addTestData();
   } */
});

// 页面显示时执行
onShow(() => {
  console.log('页面已显示');
});
</script>

<style lang="scss">
.status_bar {
    height: var(--status-bar-height);
    width: 100%;
    background-color: #fff;
}
.container {
  background-color: #f7f7f7;
  min-height: 100vh;
}

.nav-bar {
    position: relative;
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-bottom: 1rpx solid #f0f0f0;
    
}

.back-btn {
    position: absolute;
    left: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    color: #FF3366;
}

.arrow-left {
    width: 26rpx;
    height: 26rpx;
    transform: rotate(180deg);
}

.title {
    font-size: 32rpx;
    color: #FF3366;
    text-align: center;
    font-weight: 500;
}


.search-box {
  padding: 20rpx 30rpx;
}
.send-btn-wrapper {
    padding: 0 30rpx;
    margin-top: 660rpx;
}

.send-btn {
	position: absolute;
	right: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1;
	background-color: #FD3357 !important;
}

.send-btn:active {
    opacity: 0.9;
}
.search-input {
 /*  height: 72rpx;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx; */
}

.icon-search {
  font-size: 32rpx;
  color: #999999;
  margin-right: 10rpx;
}

.placeholder {
  font-size: 28rpx;
  color: #999999;
}

.add-contact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.add-contact-left {
  display: flex;
  align-items: center;
}

.contact-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.icon-phone {
  font-size: 40rpx;
}

.contact-text {
  font-size: 32rpx;
  color: #333333;
}

.icon-right {
  font-size: 32rpx;
  color: #cccccc;
}

.time-title {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #999999;
}

.request-list {
  background-color: #ffffff;
}

.request-item {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
}

.avatar image {
  width: 100%;
  height: 100%;
}

.request-info {
  flex: 1;
}

.name {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 6rpx;
}

.message {
  font-size: 28rpx;
  color: #999999;
}

.action-btn {
  display: flex;
  //   flex-direction: column;
  align-items: flex-end;
  gap: 10rpx;
}

.btn {
  min-width: 120rpx;
  height: 60rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 20rpx;
  margin: 0;
}

.accept {
  background-color: #ff3366;
  color: #ffffff;
}

.reject {
  background-color: #f0f0f0;
  color: #999999;
}

.status-text {
  font-size: 28rpx;
  color: #999999;
}

::v-deep .uicon-arrow-left {
  font-weight: 600 !important;
}
::v-deep .u-input--radius{
  border-radius: 30rpx;
  background-color: #fff;
}
</style>