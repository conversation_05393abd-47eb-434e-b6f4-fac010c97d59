<template>
  <view class="profile-container">
    <!-- 状态栏占位 -->
    <view class="status_bar"></view>
    
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="back-btn" @click="goBack">
        <image class="arrow-left" src="/static/My/arrow-right.png" mode="aspectFit"></image>
      </view>
      <view class="title">{{ $t('friendSetting.title') }}</view>
    </view>
    
  
    <!-- 功能列表 -->
    <view class="list-area">
      <!-- 查看聊天记录 -->
      <view class="list-item-wrapper" @click="toSetName">
        <view class="list-item">
          <view class="item-left">
            <text class="function-text">{{ $t('friendSetting.setRemark') }}</text>
          </view>
          <image class="arrow-right" src="/static/My/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
      <view class="list-item-wrapper" @click="pushFriends">
        <view class="list-item">
          <view class="item-left">
            <text class="function-text">{{ $t('friendSetting.recommendToFriend') }}</text>
          </view>
          <image class="arrow-right" src="/static/My/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
      <!-- 消息置顶 -->
      <view class="list-item-wrapper">
        <view class="list-item" >
          <view class="item-left">
            <text class="function-text">{{ userInfo.Ban ? $t('friendSetting.removeFromBlacklist') : $t('friendSetting.addToBlacklist') }}</text>
          </view>
          <switch :checked="userInfo.Ban" @change="togglePinMessage" color="#FF3366" />
        </view>
      </view>
      

      
      <!-- 危险操作区域 -->
      <view class="danger-section">
        <!-- 删除好友 -->
        <view class="list-item-wrapper danger-item" @click="deleteFriend">
          <view class="list-item" style="justify-content: center;">
            <view class="item-left">
              <text class="function-text danger-text">{{ $t('friendSetting.deleteFriend') }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 自定义对话框 -->
    <custom-modal 
      v-model:visible="modalConfig.visible"
      :content="modalConfig.content"
      :confirm-text="modalConfig.confirmText"
      :cancel-text="modalConfig.cancelText"
      @confirm="modalConfig.onConfirm"
      @cancel="modalConfig.onCancel"
    />
  </view>
</template>

<script setup>
import { ref, onUnmounted, getCurrentInstance, nextTick } from 'vue';
import { onShow, onReady, onHide,onLoad } from "@dcloudio/uni-app";
import CustomModal from '@/components/CustomModal.vue';
import { setBanFriend, delFriend } from '@/utils/commonOper.js'
import { clearChatMessages } from '@/utils/db.js'
import { t, getCurrentLanguage } from '@/utils/i18n.js';


let canvasCtx = null;
let canvasWidth = 0;
let canvasHeight = 0;
let animationIntervalId = null;
let animationFrameId = null;
let wavePhase = 0;
const instance = getCurrentInstance();
let isCanvasReady = false; // 标记 Canvas 是否已成功初始化过
let isPageVisible = true; // 标记页面是否可见

// 多语言支持
const currentLang = ref(getCurrentLanguage());

// 监听语言变化事件
uni.$on('languageChanged', (lang) => {
  currentLang.value = lang;
});

// 多语言翻译函数
const $t = (key) => {
  // 访问响应式变量以触发更新
  currentLang.value;
  return t(key);
};

// 用户信息
const userInfo = ref({
  id: null,
  name: '', //
  Ban: false,
});

// 设置选项
const settings = ref({
  pinMessage: false,
  muteNotifications: true
});

// 定义波浪参数
const waves = [
  {
    amplitude: 25,
    frequency: 0.02,
    speed: 0.018, // 增加速度
    color: "rgba(255, 255, 255, 0.22)",
    offset: 0,
  },
  {
    amplitude: 30,
    frequency: 0.015,
    speed: 0.02, // 增加速度
    color: "rgba(255, 255, 255, 0.16)",
    offset: Math.PI / 2,
  },
  {
    amplitude: 20,
    frequency: 0.025,
    speed: 0.026, // 增加速度
    color: "rgba(255, 255, 255, 0.11)",
    offset: Math.PI,
  },
];

// --- 动画循环控制 ---

// 停止所有动画循环
const stopAnimationLoop = () => {
  // #ifdef H5
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    console.log("Cancelled requestAnimationFrame:", animationFrameId);
    animationFrameId = null;
  }
  // #endif
  // #ifdef APP-PLUS || MP
  if (animationIntervalId) {
    clearInterval(animationIntervalId);
    animationIntervalId = null;
  }
  // #endif
};

// 启动动画循环 (根据平台选择方式)
const startAnimationLoop = () => {
  stopAnimationLoop();

  if (!canvasCtx || !isPageVisible) {
    return;
  }

  // #ifdef H5
  animateH5();
  // #endif

  // #ifdef APP-PLUS || MP
  if (!animationIntervalId) {
    animationIntervalId = setTimeout(() => {
      if (!canvasCtx || !isPageVisible) {
        stopAnimationLoop();
        return;
      }
      updateWavePhase();
      drawWaves();
      startAnimationLoop(); // 递归调用以实现更平滑的动画
    }, 16); // 使用16ms的间隔（约60fps）
  }
  // #endif
};

// --- Canvas 初始化与绘制 ---

const initCanvas = () => {
  // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ || APP-PLUS || H5
  // 确保在 DOM 更新后执行
  nextTick(() => {
    const query = uni.createSelectorQuery().in(instance.proxy);
    query
      .select("#waveCanvas")
      .boundingClientRect((data) => {
        console.log("Attempting to get Canvas boundingClientRect. Data:", data);
        if (data && data.width > 0 && data.height > 0) {
          canvasWidth = data.width;
          canvasHeight = data.height;
          console.log(
            `Canvas dimensions obtained: ${canvasWidth}x${canvasHeight}`
          );

          // 确保传递 instance.proxy
          canvasCtx = uni.createCanvasContext("waveCanvas", instance.proxy);
          console.log(
            "Canvas context created:",
            canvasCtx ? "Success" : "Failed"
          );

          if (canvasCtx) {
            isCanvasReady = true; // 标记 Canvas 初始化成功
            startAnimationLoop(); // 初始化成功后启动动画
          } else {
            isCanvasReady = false;
            console.error("Failed to create canvas context.");
          }
        } else {
          isCanvasReady = false;
          console.error("无法获取 Canvas 尺寸或尺寸无效:", data);
        }
      })
      .exec();
  });
  // #endif
};

const drawWaves = () => {
  if (!canvasCtx) return;

  // 使用离屏绘制优化
  canvasCtx.clearRect(0, 0, canvasWidth, canvasHeight);

  // 预计算波浪点
  const points = waves.map(wave => {
    const wavePoints = [];
    for (let x = 0; x < canvasWidth; x++) {
      wavePoints.push({
        x,
        y: Math.sin(x * wave.frequency + wavePhase * wave.speed + wave.offset) * wave.amplitude + canvasHeight * 0.39
      });
    }
    return wavePoints;
  });

  // 绘制每个波浪
  waves.forEach((wave, index) => {
    canvasCtx.beginPath();
    canvasCtx.moveTo(0, canvasHeight);

    const wavePoints = points[index];
    for (let i = 0; i < wavePoints.length; i++) {
      canvasCtx.lineTo(wavePoints[i].x, wavePoints[i].y);
    }

    canvasCtx.lineTo(canvasWidth, canvasHeight);
    canvasCtx.closePath();
    canvasCtx.fillStyle = wave.color;
    canvasCtx.fill();
  });

  canvasCtx.draw();
};

const updateWavePhase = () => {
  wavePhase += 1.5; // 增加相位更新速率
};

// #ifdef H5
const animateH5 = () => {
  if (!isPageVisible) return; // H5 也检查页面可见性
  updateWavePhase();
  drawWaves();
  animationFrameId = requestAnimationFrame(animateH5);
};
// #endif

// --- 生命周期与页面事件 ---

// 页面首次渲染完成时尝试初始化
onReady(() => {
  console.log("Page Ready (onReady). Attempting initial canvas init.");
  initCanvas();
});
onLoad((options)=>{
	userInfo.value=JSON.parse(options.userInfo)
})
// 页面显示/从后台返回前台时
onShow(() => {
  isPageVisible = true; // 标记页面可见
  console.log("Page Show (onShow). Checking canvas status.");
  // 如果 Canvas 之前已就绪，尝试重启动画；否则尝试重新初始化
  if (isCanvasReady && canvasCtx) {
    console.log("Canvas was ready. Restarting animation loop.");
    startAnimationLoop();
  } else {
    console.log("Canvas not ready or context lost. Re-initializing canvas.");
    // 确保清理旧的动画和上下文引用
    stopAnimationLoop();
    canvasCtx = null;
    isCanvasReady = false;
    initCanvas(); // 尝试重新初始化
  }
});

// 页面隐藏/进入后台时
onHide(() => {
  isPageVisible = false; // 标记页面不可见
  console.log("Page Hide (onHide). Stopping animation loop.");
  stopAnimationLoop(); // 停止动画节省资源
});

// 组件卸载时
onUnmounted(() => {
  isPageVisible = false;
  console.log("Page Unmounted. Stopping animation loop and cleaning context.");
  stopAnimationLoop();
  canvasCtx = null; // 清理上下文引用
  isCanvasReady = false;
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

const toSetName = () => {
  	console.log(111)
    uni.navigateTo({
      url: `/pages/profile/setName?userInfo=${JSON.stringify(userInfo.value)}`
    })
}

// 推荐好友给其他联系人
const pushFriends = () => {
  // 准备好友信息数据
  const friendInfo = {
    id: userInfo.value.id || userInfo.value.Friend,
    name: userInfo.value.Name || userInfo.value.name,
    avatar: userInfo.value.head_img,
    phone: userInfo.value.iphone_num
  };
  
  // 跳转到转发选择页面
  uni.navigateTo({
    url: `/pages/forward/index?friendInfo=${encodeURIComponent(JSON.stringify(friendInfo))}&type=recommendFriend`
  });
};

// 加入黑名单
const togglePinMessage = async (e) => {
  console.log(userInfo.value.Friend);
  const res = await setBanFriend(userInfo.value.Friend, e.detail.value)
  console.log(res);
  if(res.data.code == 0){
      uni.showToast({
      title: e.detail.value ? $t('friendSetting.addedToBlacklist') : $t('friendSetting.removedFromBlacklist'),
      icon: 'none'
    })
  } else {
    uni.showToast({
      title: res.data.msg,
      icon:'error'
    })
  }

  // settings.value.pinMessage = e.detail.value;
};

// 切换消息免打扰
const toggleMuteNotifications = (e) => {
  settings.value.muteNotifications = e.detail.value;
};

// 对话框配置
const modalConfig = ref({
  visible: false,
  content: '',
  confirmText: '确认',
  cancelText: '取消',
  onConfirm: () => {},
  onCancel: () => {}
});

// 显示对话框的通用方法
const showCustomModal = (options) => {
  modalConfig.value = {
    visible: true,
    content: options.content || '',
    confirmText: options.confirmText || '确认',
    cancelText: options.cancelText || '取消',
    onConfirm: options.success ? () => options.success({ confirm: true }) : () => {},
    onCancel: options.success ? () => options.success({ confirm: false }) : () => {}
  };
};

// 清空聊天记录
const clearChatHistory = () => {
  showCustomModal({
    content: '确定删除和发财的聊天记录吗？',
    confirmText: '清空',
    success: function(res) {
      if (res.confirm) {
        // 执行清空聊天记录的操作
        uni.showToast({
          title: '聊天记录已清空',
          icon: 'success'
        });
      }
    }
  });
};
// 删除好友
const deleteFriend = () => {
  showCustomModal({
    content: $t('friendSetting.confirmDeleteFriend'),
    confirmText: $t('common.confirm'),
    cancelText: $t('common.cancel'),
    success: async (res) =>{
      if (res.confirm) {
        try {
          const res = await delFriend(userInfo.value.Friend)
          if(res.data.code == 0){
            //  删除聊天记录（SQLite）
            const chatId = userInfo.value.Friend.toString();
            try {
              await clearChatMessages(chatId);
              console.log('聊天记录删除成功');
            } catch (error) {
              console.error('删除聊天记录失败:', error);
            }
            
            // 删除聊天列表项（Vuex）
            uni.$u.store.commit('delChatItem', {
              chatid: chatId
            });
            
            // 更新好友列表（从Vuex中移除该好友）
            const currentFriendList = uni.$u.store.state.friendList || [];
            const updatedFriendList = currentFriendList.filter(friend => 
              friend.id != userInfo.value.Friend && 
              friend.Friend != userInfo.value.Friend
            );
            uni.$u.store.commit('setFriendList', updatedFriendList);
            
            uni.hideLoading();
            uni.showToast({
              title: $t('friendSetting.friendDeleted'),
              icon: 'none'
            });
            
            setTimeout(() => {
              uni.navigateBack({
                delta: 2 // 返回到好友列表页面
              });
            }, 1000);
          } else {
            uni.hideLoading();
            uni.showToast({
              title: res.data.msg || $t('friendSetting.operationFailed'),
              icon:'none'
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('删除好友过程中出错:', error);
          uni.showToast({
            title: $t('friendSetting.operationFailed'),
            icon: 'none'
          });
        }
      }
    }
  });
};
</script>

<style lang="scss" scoped>
/* 页面根元素样式 */
page {
  height: 100vh;
  overflow: hidden;
  background-color: #f8f8f8;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.profile-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  /* iOS 滚动橡皮筋效果禁用 */
  overscroll-behavior: none;
  -webkit-overflow-scrolling: none;
  touch-action: none;
}

/* 状态栏占位 */
.status_bar {
  height: var(--status-bar-height);
  width: 100%;
  background-color: #F9F4F6;
  flex-shrink: 0;
}

/* 顶部导航栏 */
.nav-bar {
  position: relative;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  z-index: 100;
  background-color: #F9F4F6;
  border-bottom: 1rpx solid #F9F4F6;
  .title{
      color: #FF3366;
  }
}

.back-btn {
  background-color: white;
  width: 53rpx;
  height: 53rpx;
  border-radius:50%;
  position: absolute;
  left: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-left {
  width: 26rpx;
  height: 26rpx;
  transform: rotate(180deg); /* 水平翻转箭头图标 */
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.header-section {
  position: relative;
  height: 430rpx;
  margin-bottom: 0;
  flex-shrink: 0;
  overflow: visible;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(180deg, #FF3366 0%, #FF5A5F 100%);
  overflow: hidden;
  z-index: 2;
}

.header-background::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  /* 调整渐变，使其包含三个颜色点：透明、白色和白色 */
  background: linear-gradient(170deg,
      transparent 60%,
      /* 上部分保持透明 */
      #fff 60.5%,
      /* 开始斜线 */
      #fff 100%
      /* 底部填充白色 */
    );
  z-index: 3;
}

.wave-canvas {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 4;
}

.user-info-area {
  position: absolute;
  top: 250rpx;
  left: 40rpx;
  right: 40rpx;
  display: flex;
  align-items: flex-start;
  z-index: 10;
}

.avatar-container {
  position: relative;
  margin-right: 38rpx;
}

.avatar {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  border: 4rpx solid #fff;
  background-color: #eee;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.camera-icon-wrapper {
  position: absolute;
  bottom: 12rpx;
  right: 0rpx;
  width: 44rpx;
  height: 44rpx;
  background-color: #ff5a5f;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.camera-icon {
  width: 42rpx;
  height: 42rpx;
}

.gender-icon-wrapper {
  position: absolute;
  bottom: 12rpx;
  right: 0rpx;
  width: 44rpx;
  height: 44rpx;
  background-color: #ff5a5f;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.gender-icon {
  font-size: 32rpx;
  color: #fff;
}

.text-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-top: 70rpx;

  .nickname {
    font-size: 32rpx;
    color: #252525;
    margin-bottom: 10rpx;
    font-weight: 600;
    line-height: 1;
  }

  .phone {
    font-size: 24rpx;
    color: #5C5C5C;
    font-weight: 500;
    line-height: 1;
  }
}

.list-area {
  margin-top: 0;
  flex: 1;
  background-color: #f8f8f8;
  overflow: hidden;
  position: relative;
  /* iOS 滚动橡皮筋效果禁用 */
  overscroll-behavior: none;
  -webkit-overflow-scrolling: none;
  touch-action: none;
}

.list-item-wrapper {
  background-color: #fff;
  margin-bottom: 30rpx;
  overflow: hidden;

  // &:active {
  //   background-color: #f9f9f9;
  // }
}

.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 45rpx;

  .item-left {
    display: flex;
    align-items: center;
  }
}

.function-text {
  font-size: 32rpx;
  color: #333;
}

.danger-section {
  margin-top: 40rpx;
}

.danger-item {
  margin-top: 10rpx;
}

.danger-text {
  color: #FF3366;
  text-align: center;
}

/* 图标样式 */
.iconfont {
  font-family: "iconfont";
}

.icon-right:after {
  content: ">";
  color: #ccc;
}

.icon-left:after {
  content: "<";
}

/* 开关样式调整 */
switch {
  transform: scale(0.8);
}

/* 添加全局样式 */
::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

/* 右箭头图标 */
.arrow-right {
  width: 24rpx;
  height: 24rpx;
}

/* 删除不再需要的图标样式 */
/* .icon-right:after {
  content: ">";
  color: #ccc;
} */
</style>