<template>
  <view class="chat-history-container">
    <!-- 状态栏占位 -->
    <view class="status_bar"></view>

    <!-- 固定顶部导航栏 -->
    <view class="fixed-header" :style="{ top: statusBarHeight + 'rpx' }">
      <view class="nav-bar">
        <view class="nav-left" @click="goBack">
          <image class="nav-icon-img" src="/static/conversation/left.png"></image>
        </view>
        <view class="nav-title">
          <text>{{ chatTitle }}</text>
        </view>
        <view class="nav-right">
          <!-- 空的右侧按钮，保持布局一致 -->
        </view>
      </view>
    </view>

    <!-- 聊天记录列表 -->
    <scroll-view
      class="message-list"
      scroll-y="true"
      :scroll-top="scrollTop"
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
      :style="{
        marginTop: statusBarHeight + navBarHeight + 'rpx'
      }"
    >
      <view class="message-wrapper" v-for="(message, index) in messageList" :key="message.id || index">
        <!-- 消息卡片 -->
        <view class="message-card">
          <!-- 头像 -->
          <view class="avatar-container">
            <image
              class="avatar"
              :src="getDisplayAvatar(message)"
              mode="aspectFill"
            ></image>
          </view>

          <!-- 消息内容区域 -->
          <view class="message-content">
            <!-- 发送者昵称 -->
            <view class="sender-info">
              <text class="sender-name">{{ getDisplayNickname(message) }}</text>
              <text class="message-time">{{ formatSimpleTime(message.t) }}</text>
            </view>

            <!-- 消息内容 -->
            <view class="message-body">
              <!-- 文本消息 typecode2=0 -->
              <text class="message-text" v-if="message.typecode2 == 0">{{ getMessageContent(message) }}</text>

              <!-- 语音消息 typecode2=1 -->
              <view class="voice-message" v-else-if="message.typecode2 == 1" @click="playVoice(message)">
                <text class="message-type-label">语音</text>
                <text class="voice-duration">{{ getVoiceDuration(message) }}</text>
              </view>

              <!-- 图片消息 typecode2=2 -->
              <text class="message-type-label" v-else-if="message.typecode2 == 2" @click="previewImage(getImageUrl(message))">图片</text>

              <!-- 视频消息 typecode2=3 -->
              <text class="message-type-label" v-else-if="message.typecode2 == 3" @click="playVideo(message)">视频</text>

              <!-- 转发消息 typecode2=4 -->
              <text class="message-type-label" v-else-if="message.typecode2 == 4" @click="viewForwardMessage(message)">转发消息</text>

              <!-- 撤回消息 typecode2=5 -->
              <text class="message-type-label recall" v-else-if="message.typecode2 == 5">{{ getRecallText(message) }}</text>

              <!-- 语音通话 typecode2=9 -->
              <text class="message-type-label" v-else-if="message.typecode2 == 9">语音通话</text>

              <!-- 语音通话终止 typecode2=12 -->
              <text class="message-type-label" v-else-if="message.typecode2 == 12">{{ getVoiceCallEndText(message) }}</text>

              <!-- 其他类型消息 -->
              <text class="message-text" v-else>{{ getMessageContent(message) }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多提示 -->
      <view class="load-more" v-if="hasMore">
        <text class="load-text">{{ loading ? $t('common.loading') : $t('chatHistory.loadMore') }}</text>
      </view>

      <!-- 没有更多数据提示 -->
      <view class="no-more" v-else-if="messageList.length > 0">
        <text class="no-more-text">{{ $t('chatHistory.noMoreMessages') }}</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="messageList.length === 0 && !loading">
        <text class="empty-text">{{ $t('chatHistory.emptyState') }}</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { getChatMessages } from '@/utils/db.js';
import store from '@/store';
import { t, getCurrentLanguage } from '@/utils/i18n.js';

// 多语言支持
const currentLang = ref(getCurrentLanguage());

// 监听语言变化事件
uni.$on('languageChanged', (lang) => {
  currentLang.value = lang;
});

// 多语言翻译函数
const $t = (key) => {
  // 访问响应式变量以触发更新
  currentLang.value;
  return t(key);
};

// 页面参数
const chatId = ref('');
const messageList = ref([]);
const loading = ref(false);
const refreshing = ref(false);
const hasMore = ref(true);
const currentPage = ref(1);
const pageSize = ref(20);
const scrollTop = ref(0);

// 状态栏和导航栏高度
const statusBarHeight = ref(0);
const navBarHeight = ref(88); // 导航栏高度固定为88rpx

// 聊天类型相关参数
const isGroupChat = ref(false);
const sourceTypecode = ref(1); // 1=私聊，2=群聊
const chatNickname = ref('');
const chatAvatar = ref('');

// 页面已优化完成

// 用户信息
const userInfo = computed(() => store.state.userInfo || {});

// 聊天标题
const chatTitle = computed(() => {
  // 如果有传入的聊天昵称，优先使用
  if (chatNickname.value) {
    return isGroupChat.value
      ? $t('chatHistory.groupChatHistory').replace('{name}', chatNickname.value)
      : $t('chatHistory.privateChatHistory').replace('{name}', chatNickname.value);
  }

  if (messageList.value.length === 0) return $t('chatHistory.title');

  if (isGroupChat.value) {
    // 群聊逻辑：显示群聊名称或参与者列表
    const participants = new Set();

    // 添加消息中的发送者昵称
    messageList.value.forEach(message => {
      if (message.senderNickname) {
        participants.add(message.senderNickname);
      }
    });

    // 确保包含当前用户的昵称
    const currentUserName = userInfo.value.name || userInfo.value.nickname;
    if (currentUserName) {
      participants.add(currentUserName);
    }

    const participantNames = Array.from(participants);

    if (participantNames.length === 0) {
      return $t('chatHistory.title');
    } else if (participantNames.length <= 3) {
      return $t('chatHistory.groupChatHistory').replace('{name}', participantNames.join('、'));
    } else {
      const groupName = `${participantNames.slice(0, 2).join('、')}等${participantNames.length}人`;
      return $t('chatHistory.groupChatHistory').replace('{name}', groupName);
    }
  } else {
    // 私聊逻辑：显示对方昵称
    const otherParticipants = new Set();
    const currentUserId = uni.getStorageSync("userId");

    messageList.value.forEach(message => {
      if (message.fromid != currentUserId && message.senderNickname) {
        otherParticipants.add(message.senderNickname);
      }
    });

    const otherNames = Array.from(otherParticipants);
    if (otherNames.length > 0) {
      return $t('chatHistory.privateChatHistory').replace('{name}', otherNames[0]);
    }

    return $t('chatHistory.title');
  }
});

// 日期范围显示
const dateRange = computed(() => {
  if (messageList.value.length === 0) return '';

  const firstMessage = messageList.value[messageList.value.length - 1];
  const lastMessage = messageList.value[0];

  if (!firstMessage || !lastMessage) return '';

  const startDate = formatDate(firstMessage.t);
  const endDate = formatDate(lastMessage.t);

  if (startDate === endDate) {
    return startDate;
  }

  return `${startDate} 至 ${endDate}`;
});

// 页面挂载时获取状态栏高度
onMounted(() => {
  try {
    const systemInfo = uni.getSystemInfoSync();
    statusBarHeight.value = Math.round(systemInfo.statusBarHeight * 2); // 转换为rpx
  } catch (e) {
    console.error('获取系统信息失败:', e);
    statusBarHeight.value = 88; // 默认值
  }
});

// 页面加载
onLoad(async (options) => {
  console.log('聊天记录页面参数:', options);

  if (options.chatid) {
    chatId.value = options.chatid;

    // 处理聊天类型参数
    if (options.sourceTypecode) {
      sourceTypecode.value = parseInt(options.sourceTypecode);
      isGroupChat.value = sourceTypecode.value === 2;
    } else if (options.isGroup) {
      isGroupChat.value = options.isGroup === 'true';
      sourceTypecode.value = isGroupChat.value ? 2 : 1;
    }

    // 处理聊天昵称和头像
    if (options.nickname) {
      chatNickname.value = decodeURIComponent(options.nickname);
    }
    if (options.avatar) {
      chatAvatar.value = decodeURIComponent(options.avatar);
    }

    console.log('聊天类型信息:', {
      isGroupChat: isGroupChat.value,
      sourceTypecode: sourceTypecode.value,
      chatNickname: chatNickname.value,
      chatAvatar: chatAvatar.value
    });

    await loadMessages();
  } else {
    uni.showToast({
      title: $t('common.failed'),
      icon: 'none'
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

// 加载消息
const loadMessages = async (isRefresh = false) => {
  if (loading.value) return;

  try {
    loading.value = true;

    if (isRefresh) {
      currentPage.value = 1;
      hasMore.value = true;
    }

    console.log('加载聊天记录:', {
      chatId: chatId.value,
      page: currentPage.value,
      size: pageSize.value
    });

    const messages = await getChatMessages(chatId.value, currentPage.value, pageSize.value);
    console.log('获取到的消息:', messages);

    if (isRefresh) {
      messageList.value = messages || [];
    } else {
      messageList.value = [...messageList.value, ...(messages || [])];
    }

    // 检查是否还有更多数据
    if (!messages || messages.length < pageSize.value) {
      hasMore.value = false;
    }

    currentPage.value++;

  } catch (error) {
    console.error('加载聊天记录失败:', error);
    uni.showToast({
      title: $t('common.failed'),
      icon: 'none'
    });
  } finally {
    loading.value = false;
    refreshing.value = false;
  }
};

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true;
  await loadMessages(true);
};

// 加载更多
const loadMore = async () => {
  if (hasMore.value && !loading.value) {
    await loadMessages();
  }
};

// 判断是否为自己发送的消息
const isOwnMessage = (message) => {
  return message.fromid == uni.getStorageSync("userId");
};

// 获取显示的头像
const getDisplayAvatar = (message) => {
  if (isOwnMessage(message)) {
    // 自己发的消息显示自己的头像
    return uni.getStorageSync('userInfo').head_img || '/static/My/avatar.jpg';
  } else {
    // 其他人发的消息
    if (isGroupChat.value) {
      // 群聊中显示发送者的头像
      return message.senderAvatar || '/static/My/avatar.jpg';
    } else {
      // 私聊中显示对方的头像，优先使用消息中的发送者头像，其次使用传入的聊天头像
      return message.senderAvatar || chatAvatar.value || '/static/My/avatar.jpg';
    }
  }
};

// 获取显示的昵称
const getDisplayNickname = (message) => {
  if (isOwnMessage(message)) {
    // 自己发的消息显示自己的昵称
    return uni.getStorageSync('userInfo').name || '我';
  } else {
    // 其他人发的消息
    if (isGroupChat.value) {
      // 群聊中显示发送者的昵称
      return message.senderNickname || `用户${message.fromid}`;
    } else {
      // 私聊中显示对方的昵称，优先使用消息中的发送者昵称，其次使用传入的聊天昵称
      return message.senderNickname || chatNickname.value || '对方';
    }
  }
};

// 这些函数暂时保留，以备将来扩展功能时使用

// 格式化日期（用于日期范围）
const formatDate = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
};

// 格式化简单时间（用于消息列表）
const formatSimpleTime = (timestamp) => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours();
  const minutes = date.getMinutes();

  return `${year}年${month}月${day}日 ${hours}:${minutes.toString().padStart(2, '0')}`;
};

// 预览图片
const previewImage = (imageUrl) => {
  uni.previewImage({
    urls: [imageUrl],
    current: imageUrl
  });
};

// 获取消息内容
const getMessageContent = (message) => {
  if (!message.msg) return '';

  // 如果是JSON字符串，尝试解析
  try {
    if (typeof message.msg === 'string' && (message.msg.startsWith('{') || message.msg.startsWith('['))) {
      const parsed = JSON.parse(message.msg);
      return parsed.content || parsed.text || message.msg;
    }
  } catch (e) {
    console.warn('解析消息内容失败:', e);
  }

  return message.msg;
};

// 获取图片URL
const getImageUrl = (message) => {
  try {
    if (typeof message.msg === 'string' && message.msg.startsWith('{')) {
      const parsed = JSON.parse(message.msg);
      return parsed.url || parsed.src || message.msg;
    }
  } catch (e) {
    console.warn('解析图片URL失败:', e);
  }
  return message.msg;
};

// 获取语音时长
const getVoiceDuration = (message) => {
  try {
    if (typeof message.msg === 'string' && message.msg.startsWith('{')) {
      const parsed = JSON.parse(message.msg);
      return parsed.duration ? `${parsed.duration}"` : '';
    }
  } catch (e) {
    console.warn('解析语音时长失败:', e);
  }
  return message.duration ? `${message.duration}"` : '';
};

// 视频和转发消息相关函数已简化

// 获取撤回消息文本
const getRecallText = (message) => {
  try {
    if (typeof message.msg === 'string' && message.msg.startsWith('{')) {
      const parsed = JSON.parse(message.msg);
      return parsed.other || '消息已撤回';
    }
  } catch (e) {
    console.warn('解析撤回消息失败:', e);
  }
  return isOwnMessage(message) ? '你撤回了一条消息' : `${getDisplayNickname(message)}撤回了一条消息`;
};

// 获取语音通话终止消息文本
const getVoiceCallEndText = (message) => {
  try {
    if (typeof message.msg == 'string' && message.msg.startsWith('{')) {
      const parsed = JSON.parse(message.msg);
      const apply = parsed.apply;
      switch (apply) {
        case 4:
          return '发起方中断通话';
        case 5:
          return '接收方中断通话';
        case 6:
          return '通话异常中断';
        default:
          return '通话已结束';
      }
    }
  } catch (e) {
    console.warn('解析语音通话终止消息失败:', e);
  }
  return '通话已结束';
};

// 播放语音
const playVoice = (message) => {
  console.log('播放语音:', message);
  uni.showToast({
    title: '语音播放功能待实现',
    icon: 'none'
  });
};

// 播放视频
const playVideo = (message) => {
  console.log('播放视频:', message);
  uni.showToast({
    title: '视频播放功能待实现',
    icon: 'none'
  });
};

// 查看转发消息
const viewForwardMessage = (message) => {
  console.log('查看转发消息:', message);
  uni.showToast({
    title: '转发消息详情',
    icon: 'none'
  });
};

// 页面功能已简化

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.chat-history-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

/* 状态栏样式 */
.status_bar {
  background-color: white;
}

/* 固定顶部导航栏容器 */
.fixed-header {
  height: 88rpx; // 44px -> 88rpx
  overflow: hidden;
  position: fixed;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
}

/* 导航栏样式 */
.nav-bar {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx; // 44px -> 88rpx
  background-color: #fff;
  position: relative;
  padding: 0 10rpx;
  box-sizing: border-box;

  .nav-left,
  .nav-right {
    width: 88rpx;
    height: 88rpx; // 44px -> 88rpx
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx;
    box-sizing: border-box;

    .nav-icon-img {
      width: 44rpx;
      height: 44rpx;
    }
  }

  .nav-title {
    flex: 1;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    line-height: 88rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}


.message-list {
  flex: 1;
  padding: 0;
  background-color: #f5f5f5;
  height: calc(100vh - var(--status-bar-height) - 88rpx);
}

.message-wrapper {
  margin-bottom: 0;
}

.message-card {
  display: flex;
  align-items: flex-start;
  padding: 24rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
}

.avatar-container {
  margin-right: 24rpx;
  flex-shrink: 0;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.message-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.sender-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.sender-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.message-time {
  font-size: 24rpx;
  color: #999;
  flex-shrink: 0;
}

.message-body {
  flex: 1;
}

.message-text {
  font-size: 30rpx;
  line-height: 1.5;
  color: #333;
  word-wrap: break-word;
}

.message-type-label {
  font-size: 30rpx;
  color: #333;
  cursor: pointer;

  &.recall {
    color: #999;
    font-style: italic;
  }
}

.voice-message {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.voice-duration {
  font-size: 26rpx;
  color: #666;
}

.load-more {
  display: flex;
  justify-content: center;
  padding: 40rpx;
  background-color: #f5f5f5;
}

.load-text {
  font-size: 28rpx;
  color: #999;
}

.no-more {
  display: flex;
  justify-content: center;
  padding: 40rpx;
  background-color: #f5f5f5;
}

.no-more-text {
  font-size: 28rpx;
  color: #ccc;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  background-color: #f5f5f5;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
}
</style>