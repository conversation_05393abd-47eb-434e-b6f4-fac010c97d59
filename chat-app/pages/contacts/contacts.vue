<template>
	<view class="page-container">
		<!-- 状态栏占位 -->
		<view class="status_bar" />
		<CustomNavbarVue :title="$t('contacts.title')" />
		
		<view class="content-container">
			<view class="cell-group">
				<view class="cell" @click="go(1)" v-if="unreadCount!=0">
					<up-avatar shape="circle" size="40" :src="lastItem.head_img" randomBgColor />
					<view class="contact-name new-user contact-1">
							<text class="user">{{ lastItem?.name|| '未知'}}</text> 
							<text class="user desc">{{ lastItem.msg }} </text>
						</view>
					<view class="badge">
						<text class="badge-text">{{ unreadCount }}</text>
					</view>
				</view>
				<view class="cell" @click="go(1)" v-else>
					<up-avatar shape="circle" size="40" src="/static/icons/tags.png" randomBgColor />
					<text class="contact-name contact-1">{{ $t('contacts.newFriends') }}</text>
				</view>

				<view class="cell" @click="go(2)">
					<up-avatar shape="circle" size="40" src="/static/icons/official-accounts.png" randomBgColor />
					<text class="contact-name">{{ $t('contacts.groupChats') }}</text>
				</view>

				<!-- 默认好友列表 -->
				<view class="cell" v-for="friend in defaultFriends" :key="'default-friend-' + friend.ID" @click="goToFriendChat(friend)">
					<up-avatar shape="circle" size="40" :src="friend.head_img" randomBgColor />
					<text class="contact-name">{{ friend.Name }}</text>
				</view>

				<!-- 默认群组列表 -->
				<view class="cell" v-for="group in defaultGroups" :key="'default-group-' + group.ID" @click="goToGroupChat(group)">
					<up-avatar shape="circle" size="40" :src="group.GroupHeader" randomBgColor />
					<text class="contact-name">{{ group.GroupName }}</text>
				</view>
			</view>
			<contact-list :contactData="contactData" />
			<!-- 底部统计信息 -->
			<view class="contacts-footer">
				<text class="footer-text">{{ $t('contacts.totalContacts').replace('{count}', totalContacts) }}</text>
			</view>
		</view>
		<!-- 底部标签栏 -->
		<CustomTabbarVue :current-tab="'contacts'" @tabChange="switchTab" />
	</view>
</template>

<script setup>
import {
	ref,
	onMounted,
	computed,
} from 'vue';
import CustomNavbarVue from '../../components/CustomNavbar.vue';
import CustomTabbarVue from '../../components/CustomTabbar.vue';
import ContactList from '@/components/ContactList.vue'
import { getFriendList } from '@/api/friend.js';
import { getGroupList } from '@/api/group.js';
import { useStore } from 'vuex';
import { t, getCurrentLanguage } from '@/utils/i18n.js';
import { onShow } from "@dcloudio/uni-app";
import { pinyin } from 'pinyin-pro';


const store = useStore();

// 多语言支持
const currentLang = ref(getCurrentLanguage());

// 监听语言变化事件
uni.$on('languageChanged', (lang) => {
  currentLang.value = lang;
});

// 多语言翻译函数
const $t = (key) => {
  // 访问响应式变量以触发更新
  currentLang.value;
  return t(key);
};

// 使用 computed 响应式获取通知消息
const notifyMessages = computed(() => {
  return store.state.notifyMsg || [];
});

// 过滤出未读的好友申请消息
const unreadFriendRequests = computed(() => {
  return notifyMessages.value.filter(msg => 
    msg.typecode == 3 && msg.isRedRead == 0
  );
});

const lastItem = computed(() => {
  return unreadFriendRequests.value.length > 0
    ? unreadFriendRequests.value[unreadFriendRequests.value.length - 1]
    : null;
});

const totalContacts = ref(0);
const contactData = ref([]);
const defaultFriends = ref([]);
const defaultGroups = ref([]);

// 监听联系人列表组件的总数变化
const handleTotalChange = (total) => {
	totalContacts.value = total;
};

onMounted(() => {
	uni.hideTabBar()
})
onShow(() => {
	getUserFriendList();
})
const switchTab = (tab) => {
	const routes = {
		'chats': '/pages/chats/chats',
		'contacts': '/pages/contacts/contacts',
		'channels': '/pages/channels/channels',
		'me': '/pages/me/me'
	};

	if (routes[tab]) {
		uni.switchTab({
			url: routes[tab]
		});
	}
};
const go =(type)=>{
	if(type == 1) {
		uni.navigateTo({
		    url: `/pages/friend/list`
		})
	} else {
			uni.navigateTo({
			url: `/pages/group/list`
		})
	}

}

// 跳转到好友聊天页面
const goToFriendChat = (friend) => {
	const params = {
		chatid: friend.Friend,
		toId: friend.Friend,
		nickname: friend.Name,
		avatar: friend.head_img,
		sourceTypecode: 1
	};
	console.log('好友聊天参数:', params);
	uni.navigateTo({
		url: `/pages/conversation/conversation?chatid=${friend.Friend}&toId=${friend.Friend}&sourceTypecode=1&nickname=${encodeURIComponent(friend.Name)}&avatar=${encodeURIComponent(friend.head_img || '/static/My/avatar.jpg')}`
	});
}

// 跳转到群组聊天页面
const goToGroupChat = (group) => {
	const params = {
		chatid: group.ID,
		toId: group.ID,
		nickname: group.GroupName,
		avatar: group.GroupHeader,
		sourceTypecode: 2
	};
	console.log('群组聊天参数:', params);
	uni.navigateTo({
		url: `/pages/conversation/conversation?chatid=${group.ID}&toId=${group.ID}&sourceTypecode=2&nickname=${encodeURIComponent(group.GroupName)}&avatar=${encodeURIComponent(group.GroupHeader || '/static/icons/official-accounts.png')}`
	});
}
const getUserFriendList = async () => {
	try {
		// 获取配置信息
		const config = store.getters.getConfig;
		const defaultFriendIds = config?.server?.defaultFriend || [];
		const defaultGroupIds = config?.server?.defaultGroupID || [];

		console.log('默认好友ID:', defaultFriendIds);
		console.log('默认群组ID:', defaultGroupIds);

		// 获取好友列表
		const { data: res } = await getFriendList();
		console.log('好友列表响应:', res);

		if(res.code == 0) {
			// 过滤掉系统用户
			let filteredFriends = res.data.filter(item => item.Friend != 27);

			// 分离默认好友和普通好友
			const defaultFriendList = [];
			const normalFriends = [];

			filteredFriends.forEach(friend => {
				if (defaultFriendIds.includes(friend.Friend)) {
					defaultFriendList.push({
						ID: friend.ID,
						Name: friend.Name?.trim() || friend.User?.iphone_num || '未知用户',
						head_img: friend.User?.head_img || '/static/My/avatar.jpg',
						Friend: friend.Friend,
						iphone_num: friend.User?.iphone_num
					});
				} else {
					normalFriends.push(friend);
				}
			});

			// 设置默认好友
			defaultFriends.value = defaultFriendList;
			console.log('默认好友列表:', defaultFriends.value);

			// 处理普通好友的分组
			const groupedData = normalFriends.reduce((groups, friend) => {
				const name = friend.Name?.trim() || friend.User?.iphone_num || '未知用户';
				if (name.length === 0) return groups;

				const firstChar = name[0];
				const isChinese = /^[\u4E00-\u9FA5]$/.test(firstChar);
				let groupKey;

				if (isChinese) {
					// 中文转拼音首字母
					groupKey = pinyin(firstChar, { pattern: 'first', toneType: 'none' })[0]?.toUpperCase() || '#';
				} else {
					// 非中文按原逻辑处理
					groupKey = firstChar.toUpperCase();
				}

				groupKey = /^[A-Z]$/.test(groupKey) ? groupKey : '#';
				if (!groups[groupKey]) groups[groupKey] = [];
				groups[groupKey].push({
					ID: friend.ID,
					Name: name,
					head_img: friend.User?.head_img || '/static/My/avatar.jpg',
					Friend: friend.Friend,
					iphone_num: friend.User?.iphone_num
				});

				return groups;
			}, {});

			contactData.value = Object.entries(groupedData)
				.map(([letter, contacts]) => ({
					title: letter,
					contacts: contacts.sort((a, b) => a.Name.localeCompare(b.Name))
				}))
				.sort((a, b) => a.title === '#' ? 1 : b.title === '#' ? -1 : a.title.localeCompare(b.title));

			// 获取默认群组信息
			await getDefaultGroups(defaultGroupIds);

			// 计算总联系人数（不包括默认好友和群组，避免重复计算）
			totalContacts.value = normalFriends.length;

			// 将好友列表存储到store中
			store.commit('setFriendList', res.data);
			console.log('好友列表已存储到store:', res.data);
			console.log('普通好友分组数据:', contactData.value);
			console.log('总联系人数:', totalContacts.value);
		} else {
			uni.showToast({
				title: '获取好友列表失败',
				icon: 'none'
			});
		}
	}
	catch (err) {
		console.log('获取好友列表错误:', err);
		uni.showToast({
			title: '获取好友列表失败',
			icon: 'none'
		});
	}
}

// 获取默认群组信息
const getDefaultGroups = async (defaultGroupIds) => {
	if (!defaultGroupIds || defaultGroupIds.length === 0) {
		defaultGroups.value = [];
		return;
	}

	try {
		const { data: res } = await getGroupList();
		console.log('群组列表响应:', res);

		if (res.code === 0) {
			const defaultGroupList = res.data.filter(group =>
				defaultGroupIds.includes(group.ID)
			).map(group => ({
				ID: group.ID,
				GroupName: group.GroupName || '未知群组',
				GroupHeader: group.GroupHeader || '/static/icons/official-accounts.png'
			}));

			defaultGroups.value = defaultGroupList;
			console.log('默认群组列表:', defaultGroups.value);
		} else {
			console.warn('获取群组列表失败:', res);
			defaultGroups.value = [];
		}
	} catch (err) {
		console.error('获取默认群组错误:', err);
		defaultGroups.value = [];
	}
};

const unreadCount = computed(() => {
	return unreadFriendRequests.value.length;
});
</script>

<style lang="scss">
page {
	height: 100%;
	overflow: hidden;
}

.page-container {
	height: 100vh;
	width: 100%;
	display: flex;
	flex-direction: column;
	background-color: #fff;
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
}

.status_bar {
	height: var(--status-bar-height);
	width: 100%;
	background-color: white;
}

.content-container {
	flex: 1;
	position: relative;
	display: flex;
	flex-direction: column;
	height: calc(100vh - var(--status-bar-height) - 44px - 50px);
	.cell-group{
		background: #FFFFFF;
		padding: 0 20rpx;
		padding-top: 40rpx;
		.cell{
			display: flex;
			align-items: center;
			height: 140rpx;
			.contact-name{
				margin-left: 20rpx;
				width: 100%;
				height: 100rpx;
				line-height: 100rpx;
				font-size: 28rpx;
				color: #333;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				display: flex;
				flex-direction: column;
				.user{
					display: inline-block;
					height: 40rpx;
				}
				.desc{
					font-size: 24rpx;
					color: #999;
				}
			}
			.contact-1{
				border-bottom: 1px solid #E6E6E6;
			}
			.new-user{
				height: 140rpx;
			}
			.badge {
				position: absolute;
				right: 20rpx;
				background-color: #FF5D5B;
				width: 36rpx;
				height: 36rpx;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				.badge-text {
					color: #FFFFFF;
					font-size: 24rpx;
					text-align: center;
				}
			}
		}
	}
}

.contacts-footer {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 10px 0;
	background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 20%);
	text-align: center;
	z-index: 100;

	.footer-text {
		font-size: 24rpx;
		color: #666;
		line-height: 34rpx;
	}
}

/* 适配 iOS 安全区域 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) {
	.contacts-footer {
		padding-bottom: calc(10px + constant(safe-area-inset-bottom));
	}
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {
	.contacts-footer {
		padding-bottom: calc(10px + env(safe-area-inset-bottom));
	}
}
</style>