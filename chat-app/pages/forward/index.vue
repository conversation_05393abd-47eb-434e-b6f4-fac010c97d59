<template>
    <view class="select-chat-container">
        <!-- 状态栏占位 -->
        <view class="status_bar"></view>
        <!-- 顶部导航栏 -->
        <view class="nav-bar">
            <view class="back-btn" @click="goBack">
                <image class="arrow-left" src="/static/My/arrow-right.png" mode="aspectFit"></image>
            </view>
            <view class="title">选择一个聊天</view>
        </view>

        <!-- 搜索框 -->
        <view class="search-box">
            <view class="search-input-wrapper">
                <view class="search-icon-wrapper">
                    <image class="search-icon" src="/static/search.webp"></image>
                </view>
                <input class="search-input" type="text" v-model="searchText" placeholder="搜索"
                    placeholder-class="placeholder" confirm-type="search" @input="onSearchInput" />
                <view v-if="searchText" class="clear-icon-wrapper" @tap="clearSearch">
                    <text class="clear-icon">×</text>
                </view>
            </view>
        </view>

        <!-- 最近聊天 -->
        <scroll-view class="chat-list-container" scroll-y>
            <view class="section-title">最近聊天</view>

            <!-- 加载状态 -->
            <view v-if="isLoading" class="loading-container">
                <view class="loading-icon"></view>
                <text class="loading-text">加载好友列表中...</text>
            </view>

            <!-- 好友列表 -->
            <view v-else class="chat-list">
                <view v-if="filteredRecentChats.length === 0" class="empty-state">
                    <text class="empty-text">暂无好友</text>
                </view>
                <view v-else class="chat-item" v-for="(item, index) in filteredRecentChats" :key="'recent-' + index"
                    @tap="selectChat(item)">
                    <view class="radio">
                        <view class="radio-inner"
                            :class="{ 'checked': selectedItem && selectedItem.id === item.id && selectedItem.type === item.type }">
                        </view>
                    </view>
                    <image class="avatar" :src="item.avatar" mode="aspectFill"></image>
                    <view class="chat-info">
                        <view class="chat-name">{{ item.name }}</view>
                    </view>
                </view>
            </view>
        </scroll-view>

        <!-- 底部发送按钮 -->
        <view class="bottom-btn" v-if="selectedItem">
            <button class="send-button" @click="sendToSelected">发送</button>
        </view>

        <!-- 发送确认弹窗 -->
        <view class="confirm-overlay" v-if="showConfirm" @click="cancelConfirm">
            <view class="confirm-dialog" @click.stop>
                <view class="confirm-title">发送给</view>
                <view class="confirm-content">
                    <view class="confirm-avatar-list">
                        <view class="confirm-avatar-item">
                            <image class="confirm-avatar" :src="selectedItem.avatar" mode="aspectFill"></image>
                            <text class="confirm-name">{{ selectedItem.name }}</text>
                        </view>
                    </view>
                </view>
                <view class="confirm-message">
                    <text class="message-preview">{{ messagePreview }}</text>
                </view>
                <view class="confirm-buttons">
                    <view class="confirm-button cancel" @click="cancelConfirm">取消</view>
                    <view class="confirm-button confirm" @click="confirmSend">发送</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app'
import { sendMessage } from '../../utils/chatService';
import { decryptAESBase64, encryptAESBase64 } from '@/utils/decrypt.js'

const searchText = ref('');
const selectedItem = ref(null);
const showConfirm = ref(false);
const friendToRecommend = ref(null);
const forwardType = ref('');
const messagePreview = ref('');

const recentChats = ref([]);
const isLoading = ref(false);
const messageContent = ref({});
const initMessage = ref({});



// 根据搜索文本过滤
const filteredRecentChats = computed(() => {
    if (!searchText.value) return recentChats.value;
    return recentChats.value.filter(item =>
        item.name.toLowerCase().includes(searchText.value.toLowerCase())
    );
});


onLoad(async (options) => {
    console.log('聊天页面参数:', options);
    console.log('消息内容:', JSON.parse(decodeURIComponent(options.message)));
    console.log('消息内容初始化:', JSON.parse(decodeURIComponent(options.initMessage)));
    messageContent.value = JSON.parse(decodeURIComponent(options.message));
    initMessage.value = JSON.parse(decodeURIComponent(options.initMessage));
    // decodeURIComponent(JSON.parse(options.message))
    // 判断是推荐好友还是转发消息
    forwardType.value = options.type || 'message';

    // 加载好友列表
    await loadFriendList();

    if (forwardType.value === 'recommendFriend' && options.friendInfo) {
        try {
            // 解析好友信息
            friendToRecommend.value = JSON.parse(decodeURIComponent(options.friendInfo));

            // 生成预览信息
            if (friendToRecommend.value && friendToRecommend.value.name) {
                messagePreview.value = `[个人名片]${friendToRecommend.value.name}`;
            } else {
                messagePreview.value = '[个人名片]';
            }
        } catch (error) {
            console.error('解析好友信息失败', error);
            uni.showToast({
                title: '好友数据错误',
                icon: 'none'
            });
        }
    } else if (options.message) {
        try {
            // 解析消息信息（用于普通消息转发）
            const messageToForward = JSON.parse(decodeURIComponent(options.message));

            // 生成消息预览
            if (messageToForward) {
                if (messageToForward.type === 'text') {
                    messagePreview.value = messageToForward.content;
                } else if (messageToForward.type === 'image') {
                    messagePreview.value = '[图片]';
                } else if (messageToForward.type === 'voice') {
                    messagePreview.value = '[语音]';
                } else if (messageToForward.type === 'video') {
                    messagePreview.value = '[视频]';
                } else {
                    messagePreview.value = '[消息]';
                }
            }
        } catch (error) {
            console.error('解析消息失败', error);
            uni.showToast({
                title: '消息数据错误',
                icon: 'none'
            });
        }
    }
});

// 加载好友列表
const loadFriendList = async () => {
    if (isLoading.value) return;

    isLoading.value = true;
    try {
        const { getFriendList } = await import('@/api/friend.js');
        const { data: res } = await getFriendList({});

        console.log('好友列表API响应:', res);

        if (res && res.code === 0 && Array.isArray(res.data)) {
            // 过滤掉特定用户（参考contacts页面的逻辑）
            const filteredData = res.data.filter(item => item.Friend != 27);

            // 转换好友数据格式，使用正确的字段映射
            recentChats.value = filteredData.map(friend => {
                const name = friend.Name?.trim() || friend.User?.iphone_num || '未知用户';
                return {
                    id: friend.Friend, // 使用Friend字段作为好友ID
                    name: name,
                    avatar: friend.User?.head_img || '/static/My/avatar.jpg',
                    type: 'contact',
                    // 保存原始数据以备后用
                    originalData: friend
                };
            });

            console.log('转换后的好友列表:', recentChats.value);
        } else {
            console.warn('API返回数据格式异常:', res);
        }
    } catch (error) {
        console.error('获取好友列表失败:', error);
        uni.showToast({
            title: '获取好友列表失败',
            icon: 'none'
        });
    } finally {
        isLoading.value = false;
    }
};

// 清空搜索
const clearSearch = () => {
    searchText.value = '';
};

// 搜索输入处理
const onSearchInput = () => {
    // 实时过滤已经通过计算属性处理
};

// 
const selectChat = (item) => {
    if (selectedItem.value && selectedItem.value.id === item.id && selectedItem.value.type === item.type) {
        selectedItem.value = null;
    } else {
        selectedItem.value = item;
    }
};

const sendToSelected = () => {
    if (!selectedItem.value) {
        uni.showToast({
            title: '请选择一个聊天',
            icon: 'none'
        });
        return;
    }

    showConfirm.value = true;
};

const cancelConfirm = () => {
    showConfirm.value = false;
};

const confirmSend = async () => {
    if (!selectedItem.value) {
        uni.showToast({
            title: '请选择一个聊天',
            icon: 'none'
        });
        return;
    }
    try {
        if (forwardType.value === 'recommendFriend' && friendToRecommend.value) {
            // 推荐好友逻辑
            await sendRecommendFriend();
        } else {
            // 转发消息逻辑
            await sendForwardMessage(messageContent.value);
        }
        setTimeout(() => {
            showConfirm.value = false;
            goBack();
        }, 1500);
    } catch (error) {
        console.error('发送失败:', error);
        uni.hideLoading();

        // 获取错误信息
        let errorMessage = '发送失败，请重试';
        try {
            if (error && typeof error === 'object') {
                if (error.message) {
                    errorMessage = error.message;
                } else if (error.data && error.data.message) {
                    errorMessage = error.data.message;
                } else if (error.errMsg) {
                    errorMessage = error.errMsg;
                }
            } else if (error && typeof error === 'string') {
                errorMessage = error;
            }
        } catch (parseError) {
            console.error('解析错误信息失败:', parseError);
            errorMessage = '发送失败，请重试';
        }
        uni.showToast({
            title: errorMessage,
            icon: 'none'
        });
    }
};

// 发送推荐好友
const sendRecommendFriend = async () => {
    // 构建推荐好友消息
    const messageData = {
        toUserId: selectedItem.value.id,
        typecode: 0, // 文本消息
        content: `[个人名片]${friendToRecommend.value.name}`,
        messageType: 'friend_card',
        friendInfo: friendToRecommend.value
    };

    console.log('发送推荐好友消息:', messageData);

    // 调用发送消息API
    const response = await sendMessage(messageData);

    console.log('推荐好友API响应:', response);

    // 验证响应
    if (!response || (response.code && response.code !== 200 && response.code !== 0)) {
        throw new Error(response?.message || '发送推荐好友失败');
    }

    return response;
};

// 发送转发消息
const sendForwardMessage = async (messageJson) => {
    const messageToForward = messageJson;
    console.log('要转发的消息:', messageToForward);
    console.log('原始消息:', initMessage.value);
    // 根据消息类型构建发送数据
    const initSendParams = {
        typecode: initMessage.value.typecode,
        typecode2: initMessage.value.typecode2,
        fromid: initMessage.value.fromid,
        toid: initMessage.value.toid,
        msg: encryptAESBase64(initMessage.value.msg),
    }
    let messageData = {
        fromid: uni.getStorageSync('userId'),
        toId: selectedItem.value.id,
        msg: JSON.stringify({
            proxy: [
                {
                   ...initSendParams
                }
            ]
        }),
        typecode: 1,
        typecode2: 4,
    };
    console.log('发送转发消息:', messageData);

    // 调用发送消息API
    const response = await sendMessage(messageData);

    console.log('转发消息API响应:', response);

    // 验证响应
    if (response.code != 0) {
        throw new Error(response?.message || '转发消息失败');
    }
    return response;
};

// 获取消息类型代码
const getTypeCode = (messageType) => {
    switch (messageType) {
        case 'text':
            return 0; // 文本
        case 'voice':
            return 1; // 音频
        case 'image':
            return 2; // 图片
        case 'video':
            return 3; // 视频
        case 'file':
            return 6; // 文件
        default:
            return 0; // 默认文本
    }
};

const goBack = () => {
    uni.navigateBack();
};
</script>

<style lang="scss">
.select-chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f5f5f5;
}

.status_bar {
    height: var(--status-bar-height);
    width: 100%;
    background-color: #fff;
}

.nav-bar {
    position: relative;
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-bottom: 1rpx solid #f0f0f0;
}

.back-btn {
    position: absolute;
    left: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.arrow-left {
    width: 26rpx;
    height: 26rpx;
    transform: rotate(180deg);
}

.title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
}

.search-box {
    padding: 20rpx;
    background-color: #fff;
}

.search-input-wrapper {
    height: 68rpx;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 36rpx;
    padding: 0 20rpx;
}

.search-icon-wrapper {
    width: 50rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}

.search-icon {
    width: 36rpx;
    height: 36rpx;
}

.search-input {
    flex: 1;
    height: 68rpx;
    font-size: 28rpx;
    margin-left: 10rpx;
}

.placeholder {
    color: #999999;
}

.clear-icon-wrapper {
    width: 50rpx;
    height: 50rpx;
    border-radius: 25rpx;
    background-color: #c8c8c8;
    display: flex;
    justify-content: center;
    align-items: center;
}

.clear-icon {
    font-size: 30rpx;
    color: #ffffff;
    font-weight: bold;
}

.chat-list-container {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 130rpx;
    /* 为底部按钮留出空间 */
}

.section-title {
    padding: 20rpx 30rpx;
    font-size: 26rpx;
    color: #999999;
    background-color: #f5f5f5;
}

.chat-list {
    background-color: #ffffff;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 20rpx;
}

.loading-icon {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #07c160;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-text {
    font-size: 28rpx;
    color: #999;
}

.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60rpx 20rpx;
}

.empty-text {
    font-size: 28rpx;
    color: #999;
}

.chat-item {
    display: flex;
    padding: 20rpx 30rpx;
    align-items: center;
    border-bottom: 1rpx solid #f5f5f5;
}

.radio {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    border: 1rpx solid #ddd;
    margin-right: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.radio-inner {
    width: 24rpx;
    height: 24rpx;
    border-radius: 50%;
    background-color: transparent;
}

.radio-inner.checked {
    background-color: #FF3366;
}

.avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 10rpx;
    margin-right: 20rpx;
}

.chat-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.chat-name {
    font-size: 30rpx;
    color: #333333;
}

/* 底部发送按钮 */
.bottom-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 120rpx;
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20rpx;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.send-button {
    width: 80%;
    height: 80rpx;
    line-height: 80rpx;
    background-color: #FF3366;
    color: #fff;
    font-size: 30rpx;
    border-radius: 40rpx;
    text-align: center;
    letter-spacing: 2rpx;
}

/* 确认弹窗 */
.confirm-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.confirm-dialog {
    width: 80%;
    background-color: #ffffff;
    border-radius: 20rpx;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.confirm-title {
    padding: 30rpx;
    font-size: 32rpx;
    border-bottom: 1rpx solid #f5f5f5;
}

.confirm-content {
    padding: 30rpx 20rpx;
    max-height: 300rpx;
    overflow-y: auto;
}

.confirm-avatar-list {
    display: flex;
    //   justify-content: center;
}

.confirm-avatar-item {
    width: 100%;
    margin: 0 20rpx;
    display: flex;
    //   flex-direction: column;
    align-items: center;
}

.confirm-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
}

.confirm-name {
    font-size: 24rpx;
    color: #333;
    margin-top: 10rpx;
    text-align: center;
    //   width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 30rpx;
}

.confirm-message {
    padding: 0 30rpx 30rpx 30rpx;
    //   background-color: #f5f5f5;
    //   border-top: 1rpx solid #ebebeb;
}

.message-preview {
    font-size: 28rpx;
    color: #666;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.confirm-buttons {
    display: flex;
    border-top: 1rpx solid #f5f5f5;
}

.confirm-button {
    flex: 1;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    font-size: 32rpx;
}

.confirm-button.cancel {
    color: #666;
    border-right: 1rpx solid #f5f5f5;
}

.confirm-button.confirm {
    color: #FF3366;
}
</style>