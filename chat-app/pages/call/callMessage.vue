<template>
	<view v-if="visible" class="voice-call-alert" @click="handleAction">
		<view class="alert-content">
			<view class="alert-header">
				<image class="caller-avatar" :src="callerAvatar" mode="aspectFill"></image>
				<text class="caller-name">{{callerName}}</text>
			</view>
			<view class="alert-body">
				<text class="call-type">{{isVideo ? '视频通话' : '语音通话'}}</text>
				<text class="call-status">{{statusText}}</text>
			</view>
			<view class="alert-actions">
				<button v-if="showAccept" class="accept-btn" @click.stop="acceptCall">接听</button>
				<button v-if="showReject" class="reject-btn" @click.stop="rejectCall">拒绝</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'VoiceCallAlert',
		data() {
			return {
				visible: false,
				callerName: '',
				callerAvatar: '',
				isVideo: false,
				statusText: '来电中...',
				showAccept: true,
				showReject: true,
				timer: null,
				duration: 0,
				toid:''
			}
		},
		created() {

		},
		methods: {
			show(options) {
				this.callerName = options.name || '未知来电'
				this.callerAvatar = options.avatar || '/static/default-avatar.png'
				this.isVideo = options.isVideo || false
				this.visible = true
				console.log(3333, options)
				this.toid = options.message.fromid
				// 30秒未接听自动挂断
				this.timer = setTimeout(() => {
					this.rejectCall()
				}, 30000)
			},

			hide() {
				this.visible = false
				clearTimeout(this.timer)
			},
			// 接听
			acceptCall() {
				this.hide()
				this.showAccept = false
				this.showReject = false
				// this.sendToServer(0)
				// uni.navigateTo({
				//   url: `/pages/call/voice-call?toId=${this.toid}&isCaller=${false}`,
				//   success: () => {
				//     console.log('成功跳转到语音通话页面');
				//   },
				//   fail: (err) => {
				//     console.error('打开语音通话页面失败', err);
				//     // 如果失败，尝试再次检查pages.json配置
				//     uni.showToast({
				//       title: '打开语音通话失败，请检查页面路径',
				//       icon: 'none',
				//       duration: 3000
				//     });
				//   }
				// });
				

			},
			// 发送普通消息
			sendToServer(apply) {

				console.log('发送消息messagemessagemessagemessage', apply)
				uni.sendSocketMessage({
					data: JSON.stringify({
						typecode: 1,
						typecode2: 10,
						fromid: uni.getStorageSync('userId'),
						toid: Number(this.toid),
						// 0是接通  1是拒绝
						msg: JSON.stringify({
							apply: apply
						})
					})
				})
				if(apply==1){
					this.hide()
				}

			},

			rejectCall() {
				this.hide()
				// 触发拒绝事件
				this.sendToServer(1)
			},

			handleAction() {
				// 点击整个组件区域的处理
			},

			formatTime(seconds) {
				const mins = Math.floor(seconds / 60)
				const secs = seconds % 60
				return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
			}
		},

		beforeDestroy() {
			clearTimeout(this.timer)
			clearInterval(this.timer)
		}
	}
</script>

<style scoped>
	.voice-call-alert {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.7);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 9999;
	}

	.alert-content {
		background-color: #fff;
		width: 80%;
		border-radius: 12px;
		padding: 20px;
		text-align: center;
	}

	.caller-avatar {
		width: 80px;
		height: 80px;
		border-radius: 50%;
		margin-bottom: 10px;
	}

	.caller-name {
		font-size: 18px;
		font-weight: bold;
		display: block;
		margin-bottom: 10px;
	}

	.call-type {
		font-size: 16px;
		color: #666;
		display: block;
	}

	.call-status {
		font-size: 14px;
		color: #999;
		display: block;
		margin: 10px 0;
	}

	.alert-actions {
		display: flex;
		justify-content: space-around;
		margin-top: 20px;
	}

	.accept-btn,
	.reject-btn {
		width: 45%;
		height: 40px;
		line-height: 40px;
		border-radius: 20px;
		font-size: 16px;
	}

	.accept-btn {
		background-color: #4CAF50;
		color: white;
	}

	.reject-btn {
		background-color: #F44336;
		color: white;
	}
</style>