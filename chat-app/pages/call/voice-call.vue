<template>
	<view class="voice-call-container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'rpx' }"></view>

		<!-- 通话信息区域 -->
		<view class="call-info">
			<view class="back-button" @tap="hangupCall">
				<image class="back-icon" src="/static/conversation/left.png"></image>
			</view>
			<view class="caller-info">
				<image class="caller-avatar" src="/static/My/avatar.jpg"></image>
				<text class="caller-name">{{ callerName }}</text>
				<text class="call-status">{{ callStatusText }}</text>
				<text class="call-time" v-if="isCallConnected">{{ formatCallTime }}</text>
			</view>
		</view>

		<!-- 通话控制区域 -->
		<view class="call-controls">
			<view class="control-button" :class="{ disabled: !isMicrophoneOn }" @tap="toggleMicrophone" v-if="loadingStaus==2">
				<view class="control-button-bg">
					<image class="control-icon" mode="aspectFit"
						:src="isMicrophoneOn ? '/static/call/mic_on.png' : '/static/call/mic_off.png'"></image>
				</view>
				<text class="control-text">{{ isMicrophoneOn ? '麦克风已开' : '麦克风已关' }}</text>
			</view>

			<view class="control-button" :class="{ disabled: !isSpeakerOn }" @tap="toggleSpeaker" v-if="loadingStaus==2">
				<view class="control-button-bg">
					<image   mode="aspectFit"
						:src="isSpeakerOn ? '/static/call/speaker_on.png' : '/static/call/speaker_off.png'"></image>
				</view>
				<text class="control-text">{{ isSpeakerOn ? '扬声器已开' : '扬声器已关' }}</text>
			</view>

			<view class="hangup-button" @tap="hangupCall" >
				<view class="hangup-button-bg">
					<image  mode="aspectFit" src="/static/call/hangup.png"></image>
				</view>
				<text class="control-text">{{isCaller? '取消':'挂断'}}</text>
			</view>
			
			<view class="hangup-button" @tap="acceptCall" v-if="!isCaller&&loadingStaus==1">
				<view class="hangup-button-bg">
					<image  mode="aspectFit" src="/static/call/jieting.png"></image>
				</view>
				<text class="control-text">接听</text>
			</view>
		</view>
	</view>
</template>

<script >
	import {
		decryptAESBase64,
		encryptAESBase64
	} from '@/utils/decrypt.js'
	import { sendMessage } from '../../utils/chatService';
	// const nimCallKit = uni.requireNativePlugin('netease-CallKit'))
	import permision from "@/NERtcUniappSDK-JS/permission.js";
	import NERTC from "@/NERtcUniappSDK-JS/lib/index";
	// import NertcLocalView from "@/NERtcUniappSDK-JS/nertc-view/NertcLocalView";
	// import NertcRemoteView from "@/NERtcUniappSDK-JS/nertc-view/NertcRemoteView";
	import {
		NERTCRenderMode,
		NERTCChannelConnectionState,
		NERTCMirrorMode,
		NERtcVideoStreamType,
		NERtcVideoFrameRate,
		NERtcVideoCropMode,
		NERtcDegradationPreference,
		NERtcVideoOutputOrientationMode,
		NERtcVideoProfileType,
		NERtcRemoteVideoStreamType,
		NERTCAudioDevice,
		NERTCAudioDeviceType,
		NERTCAudioDeviceState,
		NERTCVideoDeviceState,
		NERTCConnectionType,
		NERTCErrorCode,
		NERtcAudioVolumeInfo,
		NERTCAudioProfile,
		NERTCAudioScenario,
		NERTCChannelProfile,
		NERTCUserRole,
		NERtcSubStreamContentPrefer
	} from '@/NERtcUniappSDK-JS/lib/NERtcDefines';
	export default {
		data() {
			return {
				// 获取页面参数
				callerName: '',
				callStatusText: '正在连接...',
				isCallConnected: false,
				callStartTime: 0,
				callDuration: 0,
				timerInterval: null,

				// 控制状态
				isMicrophoneOn: true,
				isSpeakerOn: true,
				statusBarHeight: 44,
				ringtone: null,
				engine: null,
				localId: null,
				toid: null,
				roomId: null,
				isCaller:true,
				loadingStaus:0,  //0  初始   1发起呼叫  2 接通
				id:''
			}
		},
		// 格式化通话时间
		computed: {
			formatCallTime() {
				const seconds = this.callDuration % 60;
				const minutes = Math.floor(this.callDuration / 60) % 60;
				const hours = Math.floor(this.callDuration / 3600);

				if (hours > 0) {
					return `${hours}:${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
				} else {
					return `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
				}
			}
		},

		onUnload() {
			if (this.timerInterval) {
				clearInterval(this.timerInterval);
			}
			uni.$off('chat-voice-received');
			// 关闭屏幕常亮
			uni.setKeepScreenOn({
				keepScreenOn: false
			});
		},
		onShow() {
			console.log(222222)
		},
		onLoad(options) {
			this.isCaller=options.isCaller=='true'
			this.id=Number(options.id)
			try {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight * 2; // 转换为rpx
			} catch (error) {
				console.error('获取系统信息失败', error);
			}
			console.log(1111111111111111111)
			// 模拟通话流程
			this.connectCall();

			// 保持屏幕常亮
			uni.setKeepScreenOn({
				keepScreenOn: true
			});
			console.log('options', options)
			this.toid = options.toId
			this.localId=uni.getStorageSync('userId')
			
			this.token = uni.getStorageSync("token")
			this.createEngine()
			// this.initAudioContexts()
			uni.$on('chat-voice-received', async (message) => {
				console.log('开始正式通话:', message);
				console.log('-------',options)
				await this.handleServerMessage(message)
			})
			
			
			// 发送通话请求
			// if(options.isCaller=='true'){
			// 	// this.sendCallRequest()
			// }else{
			// 	this.acceptCall()
			// }
			

			// 检测设备兼容性
			// this.checkCompatibility()
		},
		onBackPress(event) {
			console.log('onBackPress: 监听页面返回, 返回 event = {from:backbutton、 navigateBack} ，backbutton 表示来源是左上角返回按钮或 android 返回键；navigateBack表示来源是 uni.navigateBack ');
		    // this.destroyEngine();
			this.engine?.removeAllEventListener()
			//释放资源
			this.engine?.destroyEngine()
			this.engine = null
		},
		methods: {
			appendActionInfo(m) {
				console.log(m)
			},
			// 处理服务器消息
			async handleServerMessage(message) {
				if(message.typecode2==11){
					// 房间分配
					await this.handleRoomAssignment(message)
				}
				
				if(message.typecode2==12){
					// 语音通话终止
					console.log('收到语音通话终止消息:', message);

					if (this.timerInterval) {
						clearInterval(this.timerInterval);
					}
					this.destroyEngine()
					
				}

			},

			handleIncomingCall(message) {
				console.log(message)
				this.toid = message.fromid;

			},
			// 发送通话请求
			sendCallRequest() {
				
				let params={
					typecode: 1,
					typecode2: 9,
					
					fromid: Number(this.localId),
					toId: Number(this.toid),
					msg: '发起语音通话'
				}
				sendMessage(params);
			},
			// 接听来电
			async acceptCall() {


				let params={
					typecode: 1,
					typecode2: 10,
					fromid: Number(this.localId),
					id:this.id,
					toid: Number(this.toid),
					// 0是接通  1是拒绝
					msg: JSON.stringify({
						apply: 0
					})
					// msg: encryptAESBase64(JSON.stringify({ apply: 0 }))
				}
				console.log('接听来电',params)
				sendMessage(params);
			},

			// 挂断通话
			hangupCall() {
				let params={
					typecode: 1,
					typecode2: 12,
					fromid: Number(this.localId),
					id:this.id,
					toid: Number(this.toid),
					// apply: 4表示发起方中断，5表示接收方中断，6表示异常中断
					msg: JSON.stringify({ apply: this.isCaller ? 4 : 5,timer:this.formatCallTime })
				}
				sendMessage(params);
				// setTimeout(() => {
				// 	if (this.timerInterval) {
				// 		clearInterval(this.timerInterval);
				// 	}
				// 	this.destroyEngine()
				// 	uni.navigateBack();
				// }, 500); 
			},
			

			// 处理房间分配
			async handleRoomAssignment(message) {
				try {
					// message.msg = JSON.parse(decryptAESBase64(message.msg))
					// message.msg = JSON.parse(message.msg)
					if(typeof(message.msg)=='string'){
						message.msg = JSON.parse(message.msg)
					}
					this.roomId = message.msg.room
					console.log('获取token----', message)
					console.log(message.msg.token, this.roomId, this.localId * 1)
					this.engine.joinChannel({
						token: message.msg.token,
						channelName: this.roomId, //自定义房间名称
						myUid: this.localId * 1 //该房间中个人userID标识，要求number类型（不要超出number范围），房间中唯一
					});
					this.loadingStaus=2
					this.callStatusText = '语音通话中';
					this.isCallConnected = true;
					this.callStartTime = Date.now();
					
					// 开始计时
					this.timerInterval = setInterval(() => {
						this.callDuration = Math.floor((Date.now() - this.callStartTime) / 1000);
					}, 1000);
					// this.joinChannel({
					// 	token: message.msg.token, //如果云信业务后台开启了安全模式，需要填写token
					// 	channelName: this.roomId, //自定义房间名称
					// 	myUid: this.localId*1 //该房间中个人userID自定义标识，要求number类型（不要超出number范围），房间中唯一
					// });

				} catch (error) {
					console.error('房间处理错误:', error)
				}
			},



			// 发送普通消息
			sendToServer(message) {

				console.log('发送消息messagemessagemessagemessage', message)
				uni.sendSocketMessage({
					data: JSON.stringify(message)
				})

			},

			createEngine() {
				try{
					console.log('初始化NERTC引擎');
					this.engine = NERTC.setupEngineWithContext({
						appKey: '65e8972096018e57b568c4bd5e714ac8', // your appkey
						logDir: '', // expected log directory
						logLevel: 3
					});
					
				}catch (error) {
					console.error('初始化处理错误:', error)
				}
				this.engine.setChannelProfile(NERTCChannelProfile.COMMUNICATION);
				this.engine.enableLocalVideo({
					enable: false, //true表示设置启动摄像头，false表示关闭摄像头
					videoStreamType: 0 //0表示视频，1表示屏幕共享
				})
				console.log('初始化引擎完成，开始设置本地视频画布');
				
				// this.engine.setupLocalVideoCanvas({
				// 	renderMode: 0, // 0表示使用视频，视频等比缩放，1表示适应区域，会裁剪，2：折中方案 
				// 	mirrorMode: 1, //1表示启用镜像，2表示不启用
				// 	isMediaOverlay: false //表示小画布置于大画布上面（从5.3.8002版本开始，该参数已废弃）
				// })

				// this.appendActionInfo('初始化引擎完成，启动视频')
				// this.engine.enableLocalVideo({
				// 	enable: false, //true表示设置启动摄像头，false表示关闭摄像头
				// 	videoStreamType: 0 //0表示视频，1表示屏幕共享 //当前demo先使用数字
				// })
				// this.engine.enableLocalAudio(true) 
				//判断权限
				if (uni.getSystemInfoSync().platform === "android") {
					permision.requestAndroidPermission(
						"android.permission.RECORD_AUDIO"
					);
					// permision.requestAndroidPermission(
					// 	"android.permission.CAMERA"
					// );
				}

				//注册NERTC的事件
				this.engine.addEventListener("onError", (code, message, extraInfo) => {
					message = `onError通知：code = ${code}, message = ${message}, extraInfo = ${extraInfo}`
					console.log(message)
				});

				this.engine.addEventListener("onWaring", (code, message, extraInfo) => {
					message = `onWaring通知：code = ${code}, message = ${message}, extraInfo = ${extraInfo}`
					console.log(message)
				});

				// //重要：UI视频view渲染的时间点不确定，有可能会出现设置videoCanvas时，对应的视频view还没有渲染好，导致设置videoCanvas失败，用户需要监听该事件去重新设置对应的videoCanvas
				// this.engine.addEventListener("onVideoCanvas", (direction, videoStreamType, userID) => {
				// 	const imessage =
				// 		`onVideoCanvas通知：direction = ${direction}, videoStreamType = ${videoStreamType}, userID = ${userID}`
				// 	console.log(imessage)
				// 	if (direction == 'local') {
				// 		if (videoStreamType == 'video') {
				// 			//重新设置本地Video（即摄像头）的画布
				// 			this.engine.setupLocalVideoCanvas({
				// 				renderMode: 0, // 0表示使用视频，视频等比缩放，1表示适应区域，会裁剪，2：折中方案 
				// 				mirrorMode: 1, //1表示启用镜像，2表示不启用
				// 				isMediaOverlay: false //表示视图优先级，是否覆盖其他视频画布（从5.3.8002版本开始，该参数已废弃）
				// 			})
				// 		} else if (videoStreamType == 'subStreamVideo') {
				// 			//重新设置本地subStramVideo(即屏幕共享)的画布
				// 			this.engine.setupLocalSubStreamVideoCanvas({
				// 				renderMode: 0, // 0表示使用视频，视频等比缩放，1表示适应区域，会裁剪，2：折中方案 
				// 				mirrorMode: 1, //1表示启用镜像，2表示不启用
				// 				isMediaOverlay: false //表示视图优先级，是否覆盖其他视频画布（从5.3.8002版本开始，该参数已废弃）
				// 			})
				// 		}

				// 	} else if (direction == 'remote') {
				// 		if (videoStreamType == 'video') {
				// 			//重新设置远端Video（即摄像头）的画布
				// 			this.engine.setupRemoteVideoCanvas({
				// 				renderMode: NERTCRenderMode.Fit, // Fit表示应区域。视频尺寸等比缩放,保证所有区域被填满,视频超出部分会被裁剪
				// 				mirrorMode: NERTCMirrorMode.AUTO, //AUTO表示使用默认值,由sdk控制
				// 				isMediaOverlay: false, //表示视图优先级，是否覆盖其他视频画布（从5.3.8002版本开始，该参数已废弃）
				// 				userID,
				// 			})
				// 		} else if (videoStreamType == 'subStreamVideo') {
				// 			//重新设置远端subStramVideo(即屏幕共享)的画布
				// 			this.engine.setupRemoteSubStreamVideoCanvas({
				// 				renderMode: NERTCRenderMode.Fit, // Fit表示应区域。视频尺寸等比缩放,保证所有区域被填满,视频超出部分会被裁剪
				// 				mirrorMode: NERTCMirrorMode.AUTO, //AUTO表示使用默认值,由sdk控制
				// 				isMediaOverlay: false, //表示视图优先级，是否覆盖其他视频画布（从5.3.8002版本开始，该参数已废弃）
				// 				userID,
				// 			})
				// 		}
				// 	}
				// });

				this.engine.addEventListener("onJoinChannel", (result, channelId, elapsed, userID) => {
					const message =
						`onJoinChannel通知：自己加入房间状况，result = ${result}, channelId = ${channelId}, elapsed = ${elapsed}, userID = ${userID}`
					console.log(message)
				});

				this.engine.addEventListener("onLeaveChannel", (result) => {
					const message = `onLeaveChannel通知：自己离开房间状况，result = ${result}`
					console.log(message)
				});

				this.engine.addEventListener("onUserJoined", (userID) => {
					const message = `onUserJoined通知：有人加入房间，userID = ${userID}`
					console.log(message)
				});

				this.engine.addEventListener("onUserLeave", (userID, reason) => {
					const message = `onUserLeave通知：有人离开房间，userID = ${userID}, reason = ${reason}`
					console.log(message)
				});

				this.engine.addEventListener("onUserAudioStart", (userID) => {
					const message = `onUserAudioStart通知：对方开启音频，userID = ${userID}`
					console.log(message)
				});

				this.engine.addEventListener("onUserAudioStop", (userID) => {
					const message = `onUserAudioStop通知：对方关闭音频，userID = ${userID}`
					console.log(message)
				});

				// this.engine.addEventListener("onUserVideoStart", (userID, maxProfile) => {
				// 	const message = `onUserVideoStart通知：对方开启视频，userID = ${userID}, maxProfile = ${maxProfile}`
				// 	console.log(message)

				// 	//nertc-remote-view组件 viewID和userID的数据格式是String类型，onUserVideoStart事件通知的userID是number，这里做一下数据格式转换
				// 	const remoteUserID = `${userID}`
				// 	if (!this.remoteUserIdVideoList.includes(remoteUserID)) {
				// 		this.remoteUserIdVideoList.push(remoteUserID);
				// 		//保证当前nertc-remote-view组件渲染完成，在执行设置画布的接口
				// 		this.$nextTick(() => {
				// 			console.log('此时远端视频 nertc-remote-view 渲染完成')
				// 			//需要开发者主动去做订阅对方视频的逻辑动作
				// 			this.subscribeRemoteVideo(userID)
				// 		})
				// 	}
				// });

				// this.engine.addEventListener("onUserVideoStop", (userID) => {
				// 	const message = `onUserVideoStop通知：对方关闭视频，userID = ${userID}`
				// 	console.log(message)
				// });

			},

			destroyEngine() {
				console.log('销毁NERTC引擎')
				//清除注册的所有事件
				this.engine?.removeAllEventListener()
				//释放资源
				this.engine?.destroyEngine()
				this.engine = null
				uni.navigateBack();

			},

			joinChannel(token, roomid, userid) {
				console.log('加入房间')
				this.engine.joinChannel({
					token: token,
					channelName: roomid, //自定义房间名称
					myUid: userid //该房间中个人userID标识，要求number类型（不要超出number范围），房间中唯一
				});
			},

			leaveChannel() {
				console.log('离开房间')
				this.engine.leaveChannel();
				this.remoteUserIdVideoList = []
			},

			subscribeRemoteVideo(remoteUserID) {
				console.log(`开始拉流: ${remoteUserID}, 首先设置对端视频画布`)
				this.engine.setupRemoteVideoCanvas({
					renderMode: NERTCRenderMode.Fit, // Fit表示应区域。视频尺寸等比缩放,保证所有区域被填满,视频超出部分会被裁剪
					mirrorMode: NERTCMirrorMode.AUTO, //AUTO表示使用默认值,由sdk控制
					isMediaOverlay: false, //表示小画布置于大画布上面（从5.3.8002版本开始，该参数已废弃）
					userID: remoteUserID,
				});

				console.log(`开始拉流: ${remoteUserID}, 然后订阅对端视频`)
				this.engine.subscribeRemoteVideo({
					userID: remoteUserID,
					streamType: 0, //0表示大流，1表示小流
					subscribe: true //true表示订阅，false表示取消订阅
				})
			},

			// 模拟接通通话
			connectCall() {
				setTimeout(() => {
					
					console.log(this.isCaller)
					if(this.isCaller){
						this.callStatusText = '等待对方接受邀请';
					}else{
						this.callStatusText = '邀请您语音通话';
					}
					this.loadingStaus=1
					

					// 播放提示音
					try {
						const innerAudioContext = uni.createInnerAudioContext();
						innerAudioContext.src = '/static/ringtone.mp3';
						innerAudioContext.play();
					} catch (error) {
						console.error('播放提示音失败', error);
					}
				}, 2000);
			},

			// 切换麦克风
			toggleMicrophone() {
				this.isMicrophoneOn = !this.isMicrophoneOn;
				uni.showToast({
					title: this.isMicrophoneOn ? '麦克风已打开' : '麦克风已关闭',
					icon: 'none'
				});
				this.engine.enableLocalAudio(this.isSpeakerOn) 
								

			},

			// 切换扬声器
			toggleSpeaker() {
				this.isSpeakerOn = !this.isSpeakerOn;
				uni.showToast({
					title: this.isSpeakerOn ? '扬声器已打开' : '扬声器已关闭',
					icon: 'none'
				});
				this.engine.setSpeakerphoneOn(this.isSpeakerOn)
				// this.engine.rePlayAudio()	
			},

			





		}

	}
</script>

<style lang="scss" scoped>
	.voice-call-container {
		position: relative;
		width: 100%;
		height: 100vh;
		background: linear-gradient(to bottom, #333, #111);
		display: flex;
		flex-direction: column;
		align-items: center;
		color: #fff;
	}

	.status-bar {
		width: 100%;
	}

	.call-info {
		position: relative;
		width: 100%;
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 100rpx;
	}

	.back-button {
		position: absolute;
		top: 20rpx;
		left: 30rpx;
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		background-color: rgba(255, 255, 255, 0.2);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10;
	}

	.back-icon {
		width: 40rpx;
		height: 40rpx;
	}

	.caller-info {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.caller-avatar {
		width: 200rpx;
		height: 200rpx;
		border-radius: 100rpx;
		margin-bottom: 40rpx;
		border: 6rpx solid rgba(255, 255, 255, 0.3);
	}

	.caller-name {
		font-size: 48rpx;
		font-weight: 500;
		margin-bottom: 20rpx;
	}

	.call-status {
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.7);
		margin-bottom: 10rpx;
	}

	.call-time {
		font-size: 30rpx;
		color: rgba(255, 255, 255, 0.6);
	}

	.call-controls {
		width: 100%;
		padding: 60rpx 40rpx;
		display: flex;
		justify-content: space-around;
		align-items: center;
		margin-bottom: 80rpx;
	}

	.control-button {
		display: flex;
		flex-direction: column;
		align-items: center;
		opacity: 1;
		transition: opacity 0.3s;

		&.disabled {
			opacity: 0.5;
		}
	}

	.control-button-bg {
		width: 173rpx;
		height: 173rpx;
		border-radius: 86rpx;
		// background-color: rgba(255, 255, 255, 0.2);
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
	}

	.control-icon {
		width: 100%;
		height: 100%;
	}

	.control-text {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.hangup-button {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 128rpx;
		height: 128rpx;
		border-radius: 64rpx;
	}

	.hangup-button-bg {
		width: 100%;
		height: 100%;
		border-radius: 50%;
		background-color: #e53935;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
	}

	.hangup-icon {
		width: 60rpx;
		height: 60rpx;
	}
</style>