<template>

  <view class="my-page-container">

    <!-- 状态栏占位 -->
    <view class="status_bar"></view>
    <!-- 顶部背景和用户信息 -->
    <view class="header-section">
      <!-- 渐变背景 -->
      <view class="header-background">
        <canvas canvas-id="waveCanvas" id="waveCanvas" class="wave-canvas"></canvas>
      </view>

      <!-- 用户信息区域 -->
      <view class="user-info-area">
        <view class="avatar-container">
          <image class="avatar" :src="userInfo.head_img" mode="aspectFill" @click="upload"></image>
          <view class="camera-icon-wrapper">
            <image class="camera-icon" src="@/static/My/camera.png" mode="aspectFit"></image>
          </view>
        </view>
        <view class="text-info">
          <text class="nickname">{{ userInfo.name }}</text>
          <text class="phone">{{ $t('me.phoneNumber') }}：{{maskPhoneNumber(userInfo.iphone_num)  }}</text>
        </view>
      </view>
    </view>
    <!-- 功能列表区域 -->
    <view class="list-area">
      <!-- 个人信息 -->
      <view class="list-item-wrapper" @click="navigateTo('/pages/me/setting')">
        <view class="list-item">
          <view class="item-left">
            <image class="item-icon" src="/static/My/profile.png" mode="aspectFit"></image>
            <text class="item-text">{{ $t('me.personalInfo') }}</text>
          </view>
          <image class="arrow-right" src="/static/My/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>
	  
	  <view class="list-item-wrapper" v-for="(item,index) in config.server.PlugIn" :key='index'>
	    <view class="list-item-plugin">
		  <iframe style="height: 100%;border: 0;width:100%;" :fullscreen='false' :src="item.val" v-if="item.type==2"></iframe>
	      <image class="arrow-right"  style="width: 100%;height: 100%;" @click="openUrl(item)" :src="item.val" mode="scaleToFill" v-if="item.type==3"></image>
		  <view class="arrow-right"  style="width: 100%;height: 100%;"  v-html="item.val" v-if="item.type==1"></view>
	    </view>
	  </view>
	  
	  <view class="list-item-wrapper" @click="navigateTo('/pages/setting/index')">
	    <view class="list-item">
	      <view class="item-left">
	        <image class="item-icon" src="/static/My/setting.png" mode="aspectFit"></image>
	        <text class="item-text">{{ $t('me.settings') }}</text>
	      </view>
	      <image class="arrow-right" src="/static/My/arrow-right.png" mode="aspectFit"></image>
	    </view>
	  </view>
      <!-- 设置 -->
      
	  
	 <!-- <view class="list-item-wrapper" @click="navigateTo('/pages/test/index')">
	    <view class="list-item">
	      <view class="item-left">
	        <image class="item-icon" src="/static/My/setting.png" mode="aspectFit"></image>
	        <text class="item-text">test</text>
	      </view>
	      <image class="arrow-right" src="/static/My/arrow-right.png" mode="aspectFit"></image>
	    </view>
	  </view> -->
	  

      <!-- 数据库调试 -->
     <!--  <view class="list-item-wrapper" @click="navigateTo('/pages/debug/db-test')">
        <view class="list-item">
          <view class="item-left">
            <image class="item-icon" src="/static/My/setting.png" mode="aspectFit"></image>
            <text class="item-text">数据库调试</text>
          </view>
          <image class="arrow-right" src="/static/My/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view> -->

      <!-- 退出登录 -->
     <!--  <view class="list-item-wrapper" @click="logout">
        <view class="list-item">
          <view class="item-left">
            <image class="item-icon" src="/static/My/setting.png" mode="aspectFit"></image>
            <text class="item-text">退出登录</text>
          </view>
          <image class="arrow-right" src="/static/My/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view> -->
    </view>

    <!-- 底部导航栏 -->
    <custom-tabbar :current-tab="'me'" @tabChange="switchTab" />
  </view>
</template>

<script setup>
import { ref, onUnmounted, getCurrentInstance, nextTick ,computed} from "vue";
import { useStore } from 'vuex';
import { onShow, onReady, onHide ,onLoad} from "@dcloudio/uni-app";
import  {setMeInfo}  from  "../../api/api";
import CustomTabbar from '@/components/CustomTabbar.vue';
import { t, getCurrentLanguage } from '@/utils/i18n.js';

const store = useStore();

// 多语言支持
const currentLang = ref(getCurrentLanguage());

// 监听语言变化事件
uni.$on('languageChanged', (lang) => {
  currentLang.value = lang;
});

// 多语言翻译函数
const $t = (key) => {
  // 访问响应式变量以触发更新
  currentLang.value;
  return t(key);
};

const config = computed(() => store.getters.getConfig);

console.log(config.value.server.PlugIn)

let canvasCtx = null;
let canvasWidth = 0;
let canvasHeight = 0;
let animationIntervalId = null; // setInterval ID (App/MP)
let animationFrameId = null; // requestAnimationFrame ID (H5)
let wavePhase = 0;
const instance = getCurrentInstance();
let isCanvasReady = false; // 标记 Canvas 是否已成功初始化过
let isPageVisible = true; // 标记页面是否可见

const webviewStyles=ref({
	width:'100%',
	height:"100%",
	border:0
})
// 用户信息
const userInfo = ref({
  name: "",
  iphone_num: "",
  head_img: "/static/My/avatar.jpg",
});

// 定义波浪参数
const waves = [
  {
    amplitude: 25,
    frequency: 0.02,
    speed: 0.018, // 增加速度
    color: "rgba(255, 255, 255, 0.22)",
    offset: 0,
  },
  {
    amplitude: 30,
    frequency: 0.015,
    speed: 0.02, // 增加速度
    color: "rgba(255, 255, 255, 0.16)",
    offset: Math.PI / 2,
  },
  {
    amplitude: 20,
    frequency: 0.025,
    speed: 0.026, // 增加速度
    color: "rgba(255, 255, 255, 0.11)",
    offset: Math.PI,
  },
];

const openUrl=(item)=>{
	plus.runtime.openURL(item.openUrl, (err) => {
	    if (err) {
	      uni.showToast({ title: '打开失败', icon: 'none' })
	    }
	  })
	// uni.openWebView({
	//   url: item.openUrl
	// })
}
// --- 动画循环控制 ---

// 停止所有动画循环
const stopAnimationLoop = () => {
  // #ifdef H5
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    console.log("Cancelled requestAnimationFrame:", animationFrameId);
    animationFrameId = null;
  }
  // #endif
  // #ifdef APP-PLUS || MP
  if (animationIntervalId) {
    clearInterval(animationIntervalId);
    // console.log("Cleared setInterval:", animationIntervalId);
    animationIntervalId = null;
  }
  // #endif
};
const maskPhoneNumber = (phone) => {
  if (!phone || typeof phone !== 'string') {
    return phone || '';
  }
  
  // 移除所有非数字字符
  const cleanPhone = phone.replace(/\D/g, '');
  
  // 检查是否为有效的11位手机号
  if (cleanPhone.length !== 11) {
    return phone;
  }
  
  // 格式化为 182****9901
  return `${cleanPhone.slice(0, 3)}****${cleanPhone.slice(-4)}`;
};
// 启动动画循环 (根据平台选择方式)
const startAnimationLoop = () => {
  stopAnimationLoop();

  if (!canvasCtx || !isPageVisible) {
    return;
  }

  // #ifdef H5
  animateH5();
  // #endif

  // #ifdef APP-PLUS || MP
  if (!animationIntervalId) {
    animationIntervalId = setTimeout(() => {
      if (!canvasCtx || !isPageVisible) {
        stopAnimationLoop();
        return;
      }
      updateWavePhase();
      drawWaves();
      startAnimationLoop(); // 递归调用以实现更平滑的动画
    }, 16); // 使用16ms的间隔（约60fps）
  }
  // #endif
};

// --- Canvas 初始化与绘制 ---

const initCanvas = () => {
  // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ || APP-PLUS || H5
  // 确保在 DOM 更新后执行
  nextTick(() => {
    const query = uni.createSelectorQuery().in(instance.proxy);
    query
      .select("#waveCanvas")
      .boundingClientRect((data) => {
        console.log("Attempting to get Canvas boundingClientRect. Data:", data);
        if (data && data.width > 0 && data.height > 0) {
          canvasWidth = data.width;
          canvasHeight = data.height;
          console.log(
            `Canvas dimensions obtained: ${canvasWidth}x${canvasHeight}`
          );

          // 确保传递 instance.proxy
          canvasCtx = uni.createCanvasContext("waveCanvas", instance.proxy);
          console.log(
            "Canvas context created:",
            canvasCtx ? "Success" : "Failed"
          );

          if (canvasCtx) {
            isCanvasReady = true; // 标记 Canvas 初始化成功
            startAnimationLoop(); // 初始化成功后启动动画
          } else {
            isCanvasReady = false;
            console.error("Failed to create canvas context.");
          }
        } else {
          isCanvasReady = false;
          console.error("无法获取 Canvas 尺寸或尺寸无效:", data);
          // 如果仍然失败，可以考虑增加延迟或检查 Canvas 是否被 v-if 条件隐藏
          // setTimeout(initCanvas, 500); // 简单的延迟重试示例
        }
      })
      .exec();
  });
  // #endif
};


const upload = () => {

	uni.chooseImage({
		count:1,
		sourceType: ['album'], 
		success: (chooseImageRes) => {

			const tempFilePaths = chooseImageRes.tempFilePaths;
			uni.uploadFile({
				url: `http://43.198.105.182:82/api/upload?type=${3}`,
				filePath: tempFilePaths[0],
				name: 'file',
				header:{
					'x-token': uni.getStorageSync("token") || '',

				},
				// formData: {
				// 	'file': tempFilePaths[0]
				// },
				success: (uploadFileRes) => {
					console.log(uploadFileRes.data);
					let res=JSON.parse(uploadFileRes.data)
					let header_img=res.data.filepath
					const {name}=userInfo.value
					setMeInfo({name,header_img}).then(res=>{
						userInfo.value.head_img=header_img
						uni.setStorageSync("userInfo", userInfo.value);
					})
				}
			});
		}
	});
};
const drawWaves = () => {
  if (!canvasCtx) return;

  // 使用离屏绘制优化
  canvasCtx.clearRect(0, 0, canvasWidth, canvasHeight);

  // 预计算波浪点
  const points = waves.map(wave => {
    const wavePoints = [];
    for (let x = 0; x < canvasWidth; x++) {
      wavePoints.push({
        x,
        y: Math.sin(x * wave.frequency + wavePhase * wave.speed + wave.offset) * wave.amplitude + canvasHeight * 0.39
      });
    }
    return wavePoints;
  });

  // 绘制每个波浪
  waves.forEach((wave, index) => {
    canvasCtx.beginPath();
    canvasCtx.moveTo(0, canvasHeight);

    const wavePoints = points[index];
    for (let i = 0; i < wavePoints.length; i++) {
      canvasCtx.lineTo(wavePoints[i].x, wavePoints[i].y);
    }

    canvasCtx.lineTo(canvasWidth, canvasHeight);
    canvasCtx.closePath();
    canvasCtx.fillStyle = wave.color;
    canvasCtx.fill();
  });

  canvasCtx.draw();
};

const updateWavePhase = () => {
  wavePhase += 1.5; // 增加相位更新速率
};

// #ifdef H5
const animateH5 = () => {
  if (!isPageVisible) return; // H5 也检查页面可见性
  updateWavePhase();
  drawWaves();
  animationFrameId = requestAnimationFrame(animateH5);
};
// #endif

// --- 生命周期与页面事件 ---

// 页面首次渲染完成时尝试初始化
onReady(() => {
  console.log("Page Ready (onReady). Attempting initial canvas init.");
  initCanvas();
});
onLoad(()=>{

	console.log(uni.getStorageSync("userInfo"))
})

// 页面显示/从后台返回前台时
onShow(() => {
	userInfo.value=uni.getStorageSync("userInfo");
  uni.hideTabBar();
  isPageVisible = true; // 标记页面可见
  console.log("Page Show (onShow). Checking canvas status.");
  // 如果 Canvas 之前已就绪，尝试重启动画；否则尝试重新初始化
  if (isCanvasReady && canvasCtx) {
    console.log("Canvas was ready. Restarting animation loop.");
    startAnimationLoop();
  } else {
    console.log("Canvas not ready or context lost. Re-initializing canvas.");
    // 确保清理旧的动画和上下文引用
    stopAnimationLoop();
    canvasCtx = null;
    isCanvasReady = false;
    initCanvas(); // 尝试重新初始化
  }
});

// 页面隐藏/进入后台时
onHide(() => {
  isPageVisible = false; // 标记页面不可见
  console.log("Page Hide (onHide). Stopping animation loop.");
  stopAnimationLoop(); // 停止动画节省资源
});

// 组件卸载时
onUnmounted(() => {
  isPageVisible = false;
  console.log("Page Unmounted. Stopping animation loop and cleaning context.");
  stopAnimationLoop();
  canvasCtx = null; // 清理上下文引用
  isCanvasReady = false;
});

// --- 其他函数 ---
// 通用跳转函数
const navigateTo = (url) => {
  if (!url) {
    uni.showToast({ title: "路径未配置", icon: "none" });
    return;
  }
  uni.navigateTo({
    url: url,
  });
};

// 切换底部标签页
const switchTab = (tab) => {
  console.log('切换标签页:', tab); // 添加日志
  const routes = {
    chats: '/pages/chats/chats',
    contacts: '/pages/contacts/contacts',
    channels: '/pages/channels/channels',
    me: '/pages/me/me'
  };

  if (routes[tab]) {
    uni.switchTab({
      url: routes[tab],
      success: () => {
        console.log('页面跳转成功:', routes[tab]);
      },
      fail: (err) => {
        console.error('页面跳转失败:', err);
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  } else {
    console.error('未知的标签页:', tab);
  }
};

// 退出登录
const logout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 清除本地存储的用户信息
        uni.removeStorageSync('token');
        uni.removeStorageSync('baseUrl');
        uni.removeStorageSync('userId');
        uni.removeStorageSync('userInfo');
        uni.removeStorageSync('chatList');
        uni.removeStorageSync('friendMsg');
        uni.removeStorageSync('groupMsg');
        uni.removeStorageSync('notifyMsg');

        // 关闭WebSocket连接
        uni.closeSocket();

        // 跳转到登录页面
        uni.reLaunch({
          url: '/pages/login/login'
        });

        uni.showToast({
          title: '已退出登录',
          icon: 'success'
        });
      }
    }
  });
};

// ... 静态资源路径检查 ...
// /static/avatar.jpg
// /static/icons/camera.png
// /static/icons/profile.png
// /static/icons/setting_blue.png
// /static/icons/arrow-right.png
</script>
<style>
	iframe{
		border: none;
		height: 300rpx;
	}
</style>
<style lang="scss" scoped>
/* 页面根元素样式 */
iframe{
		border: none;
	}
page {
  height: 100vh;
  overflow: hidden;
  background-color: #f8f8f8;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.my-page-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  /* iOS 滚动橡皮筋效果禁用 */
  overscroll-behavior: none;
  -webkit-overflow-scrolling: none;
  touch-action: none;
}

/* 状态栏占位 */
.status_bar {
  height: var(--status-bar-height);
  width: 100%;
  background-color: #FF4B6A;
  flex-shrink: 0;
}

.header-section {
  position: relative;
  height: 430rpx;
  margin-bottom: 0;
  flex-shrink: 0;
  overflow: visible;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(180deg, #FF4B6A 0%, #FF5A5F 100%);
  overflow: hidden;
  z-index: 2;
}

.header-background::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  /* 调整渐变，使其包含三个颜色点：透明、白色和白色 */
  background: linear-gradient(170deg,
      transparent 60%,
      /* 上部分保持透明 */
      #fff 60.5%,
      /* 开始斜线 */
      #fff 100%
      /* 底部填充白色 */
    );
  z-index: 3;
}

.wave-canvas {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 4;
}

.user-info-area {
  position: absolute;
  top: 250rpx;
  left: 40rpx;
  right: 40rpx;
  display: flex;
  align-items: flex-start;
  z-index: 10;
}

.avatar-container {
  position: relative;
  margin-right: 38rpx;
}

.avatar {
  width: 150rpx;
  /* 增大头像尺寸 */
  height: 150rpx;
  border-radius: 50%;
  border: 4rpx solid #fff;
  background-color: #eee;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.camera-icon-wrapper {
  position: absolute;
  bottom: 12rpx;
  right: 0rpx;
  width: 44rpx;
  /* 稍微增大相机图标 */
  height: 44rpx;
  background-color: #ff5a5f;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);

  .camera-icon {
    width: 42rpx;
    height: 42rpx;
  }
}

.text-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-top: 70rpx;
  /* 增加上边距 */

  .nickname {
    font-size: 32rpx;
    color: #252525;
    margin-bottom: 10rpx;
    font-weight: 600;
    line-height: 1;
  }

  .phone {
    font-size: 24rpx;
    color: #5C5C5C;
    font-weight: 500;
    line-height: 1;
  }
}

.list-area {
  margin-top: 0;
  /* 移除顶部间距 */
  flex: 1;
  background-color: #f8f8f8;
  overflow: hidden;
  position: relative;
  /* iOS 滚动橡皮筋效果禁用 */
  overscroll-behavior: none;
  -webkit-overflow-scrolling: none;
  touch-action: none;
}

.list-item-wrapper {
  background-color: #fff;
  margin: 30rpx 0;
  overflow: hidden;

  &:active {
    background-color: #f9f9f9;
  }
}
.list-item-plugin{
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0;
	height: 106rpx;
}
.list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 45rpx;

  .item-left {
    display: flex;
    align-items: center;

    .item-icon {
      width: 40rpx;
      height: 40rpx;
      margin-right: 20rpx;
    }

    .item-text {
      font-size: 30rpx;
      font-weight: 500;
      color: #333;
    }
  }

  .arrow-right {
    width: 24rpx;
    height: 24rpx;
  }
}

/* 添加全局样式 */
::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.line {
  height: 190rpx;
  width: 100%;
  background-color: white;
  position: absolute;
  top: 200rpx;
  left: 0;
  right: 0;
  z-index: 1;
}
</style>
