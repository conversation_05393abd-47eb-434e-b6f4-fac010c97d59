<template>
  <view class="profile-container">
    <!-- 状态栏占位 -->
    <view class="status_bar"></view>

    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="back-btn" @click="goBack">
        <image class="arrow-left" src="/static/My/arrow-right.png" mode="aspectFit"></image>
      </view>
      <view class="title">{{ $t('chatInfo.title') }}</view>
    </view>

    <!-- 顶部背景和用户信息 -->
    <view class="header-section">
      <!-- 渐变背景 -->
      <view class="header-background">
        <!-- Canvas 波浪动画 -->
        <canvas canvas-id="waveCanvas" id="waveCanvas" class="wave-canvas"></canvas>
      </view>

      <!-- 用户信息区域 -->
      <view class="user-info-area">
        <view class="avatar-container">
          <image class="avatar" :src="pageParams.avatar" mode="aspectFill"></image>
          <view class="camera-icon-wrapper">
            <image class="camera-icon" src="@/static/My/camera.png" mode="aspectFit"></image>
          </view>
        </view>
        <view class="text-info">
          <text class="nickname">{{ pageParams.nickname }}</text>
          <text class="phone" v-if="!pageParams.isGroup">{{ $t('me.phoneNumber') }}：{{ maskPhoneNumber(pageParams.phone) }}</text>
        </view>
      </view>
    </view>

    <!-- 功能列表 -->
    <view class="list-area">
      <!-- 查看聊天记录 -->
      <view class="list-item-wrapper" @click="viewChatHistory">
        <view class="list-item">
          <view class="item-left">
            <text class="function-text">{{ $t('chatInfo.viewChatHistory') }}</text>
          </view>
          <image class="arrow-right" src="/static/My/arrow-right.png" mode="aspectFit"></image>
        </view>
      </view>

      <!-- 消息置顶 -->
      <!-- <view class="list-item-wrapper">
        <view class="list-item">
          <view class="item-left">
            <text class="function-text">消息置顶</text>
          </view>
          <switch :checked="settings.pinMessage" @change="togglePinMessage" color="#FF3366" />
        </view>
      </view> -->

      <!-- 消息免打扰 -->
      <!-- <view class="list-item-wrapper">
        <view class="list-item">
          <view class="item-left">
            <text class="function-text">消息免打扰</text>
          </view>
          <switch :checked="settings.muteNotifications" @change="toggleMuteNotifications" color="#FF3366" />
        </view>
      </view> -->

      <!-- 危险操作区域 -->
      <view class="danger-section">
        <!-- 清空聊天记录 -->
        <view class="list-item-wrapper danger-item" @click="clearChatHistory">
          <view class="list-item">
            <view class="item-left">
              <text class="function-text danger-text">{{ $t('chatInfo.clearChatHistory') }}</text>
            </view>
          </view>
        </view>

        <!-- 加入黑名单 -->
        <view class="list-item-wrapper danger-item" @click="addToBlacklist" v-if="pageParams.sourceTypecode!=2">
          <view class="list-item">
            <view class="item-left">
              <text class="function-text danger-text">{{ $t('chatInfo.addToBlacklist') }}</text>
            </view>
          </view>
        </view>

        <!-- 删除好友 -->
        <view class="list-item-wrapper danger-item" @click="deleteFriend" v-if="pageParams.sourceTypecode!=2">
          <view class="list-item">
            <view class="item-left">
              <text class="function-text danger-text">{{ $t('chatInfo.deleteFriend') }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 自定义对话框 -->
    <custom-modal
      v-model:visible="modalConfig.visible"
      :content="modalConfig.content"
      :confirm-text="modalConfig.confirmText"
      :cancel-text="modalConfig.cancelText"
      @confirm="modalConfig.onConfirm"
      @cancel="modalConfig.onCancel"
    />
  </view>
</template>

<script setup>
import { ref, onUnmounted, getCurrentInstance, nextTick, onMounted } from 'vue';
import { onShow, onReady, onHide, onLoad } from "@dcloudio/uni-app";
import CustomModal from '@/components/CustomModal.vue';
import { GetFriendByID } from '@/api/friend.js';
import { clearChatMessages, clearAllChatMessages } from '@/utils/db.js';
import { setBanFriend, delFriend } from '@/utils/commonOper.js'
import store from '@/store';
import { t, getCurrentLanguage } from '@/utils/i18n.js';

let canvasCtx = null;
let canvasWidth = 0;
let canvasHeight = 0;
let animationIntervalId = null;
let animationFrameId = null;
let wavePhase = 0;
const instance = getCurrentInstance();
let isCanvasReady = false; // 标记 Canvas 是否已成功初始化过
let isPageVisible = true; // 标记页面是否可见

// 页面参数
const pageParams = ref({
  chatid: '',
  nickname: '',
  avatar: '',
  phone: '',
  sourceTypecode: 1, // 1=私聊，2=群聊
  isGroup: false
});
const userInfo = ref({})

// 多语言支持
const currentLang = ref(getCurrentLanguage());

// 监听语言变化事件
uni.$on('languageChanged', (lang) => {
  currentLang.value = lang;
});

// 多语言翻译函数
const $t = (key) => {
  // 访问响应式变量以触发更新
  currentLang.value;
  return t(key);
};

// 页面加载时获取参数
onLoad(async (options) => {
  console.log('Profile页面参数:', options);

  if (options) {
    // 处理聊天类型参数
    const sourceTypecode = options.sourceTypecode ? parseInt(options.sourceTypecode) : 1;
    const isGroup = sourceTypecode === 2;

    // 如果是群聊，直接使用传入的参数
    if (isGroup) {
      pageParams.value = {
        chatid: options.chatId || options.chatid,
        nickname: options.nickname ? decodeURIComponent(options.nickname) : '群聊',
        avatar: options.avatar ? decodeURIComponent(options.avatar) : '/static/My/avatar.jpg',
        phone: '',
        sourceTypecode: sourceTypecode,
        isGroup: true
      };
      console.log('群聊页面参数:', pageParams.value);
    } else {
      // 私聊需要查询用户信息
      const { data: res } = await GetFriendByID(options.toId)
      console.log(res)
      if(res.code==0){
        userInfo.value = res.data
        pageParams.value = {
          chatid: options.chatId || options.chatid || options.toId,
          nickname: res.data.User.name || res.data.User.iphone_num,
          avatar: res.data.User.head_img,
          phone: res.data.User.iphone_num,
          sourceTypecode: sourceTypecode,
          isGroup: false
        };
        console.log('私聊页面参数:', pageParams.value);
      }
    }
  }
});

// 设置选项
const settings = ref({
  pinMessage: false,
  muteNotifications: true
});

// 定义波浪参数
const waves = [
  {
    amplitude: 25,
    frequency: 0.02,
    speed: 0.018, // 增加速度
    color: "rgba(255, 255, 255, 0.22)",
    offset: 0,
  },
  {
    amplitude: 30,
    frequency: 0.015,
    speed: 0.02, // 增加速度
    color: "rgba(255, 255, 255, 0.16)",
    offset: Math.PI / 2,
  },
  {
    amplitude: 20,
    frequency: 0.025,
    speed: 0.026, // 增加速度
    color: "rgba(255, 255, 255, 0.11)",
    offset: Math.PI,
  },
];

// --- 动画循环控制 ---

// 停止所有动画循环
const stopAnimationLoop = () => {
  // #ifdef H5
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    console.log("Cancelled requestAnimationFrame:", animationFrameId);
    animationFrameId = null;
  }
  // #endif
  // #ifdef APP-PLUS || MP
  if (animationIntervalId) {
    clearInterval(animationIntervalId);
    animationIntervalId = null;
  }
  // #endif
};

// 启动动画循环 (根据平台选择方式)
const startAnimationLoop = () => {
  stopAnimationLoop();

  if (!canvasCtx || !isPageVisible) {
    return;
  }

  // #ifdef H5
  animateH5();
  // #endif

  // #ifdef APP-PLUS || MP
  if (!animationIntervalId) {
    animationIntervalId = setTimeout(() => {
      if (!canvasCtx || !isPageVisible) {
        stopAnimationLoop();
        return;
      }
      updateWavePhase();
      drawWaves();
      startAnimationLoop(); // 递归调用以实现更平滑的动画
    }, 16); // 使用16ms的间隔（约60fps）
  }
  // #endif
};

// --- Canvas 初始化与绘制 ---

const initCanvas = () => {
  // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ || APP-PLUS || H5
  // 确保在 DOM 更新后执行
  nextTick(() => {
    const query = uni.createSelectorQuery().in(instance.proxy);
    query
      .select("#waveCanvas")
      .boundingClientRect((data) => {
        console.log("Attempting to get Canvas boundingClientRect. Data:", data);
        if (data && data.width > 0 && data.height > 0) {
          canvasWidth = data.width;
          canvasHeight = data.height;
          console.log(
            `Canvas dimensions obtained: ${canvasWidth}x${canvasHeight}`
          );

          // 确保传递 instance.proxy
          canvasCtx = uni.createCanvasContext("waveCanvas", instance.proxy);
          console.log(
            "Canvas context created:",
            canvasCtx ? "Success" : "Failed"
          );

          if (canvasCtx) {
            isCanvasReady = true; // 标记 Canvas 初始化成功
            startAnimationLoop(); // 初始化成功后启动动画
          } else {
            isCanvasReady = false;
            console.error("Failed to create canvas context.");
          }
        } else {
          isCanvasReady = false;
          console.error("无法获取 Canvas 尺寸或尺寸无效:", data);
        }
      })
      .exec();
  });
  // #endif
};

const drawWaves = () => {
  if (!canvasCtx) return;

  // 使用离屏绘制优化
  canvasCtx.clearRect(0, 0, canvasWidth, canvasHeight);

  // 预计算波浪点
  const points = waves.map(wave => {
    const wavePoints = [];
    for (let x = 0; x < canvasWidth; x++) {
      wavePoints.push({
        x,
        y: Math.sin(x * wave.frequency + wavePhase * wave.speed + wave.offset) * wave.amplitude + canvasHeight * 0.39
      });
    }
    return wavePoints;
  });

  // 绘制每个波浪
  waves.forEach((wave, index) => {
    canvasCtx.beginPath();
    canvasCtx.moveTo(0, canvasHeight);

    const wavePoints = points[index];
    for (let i = 0; i < wavePoints.length; i++) {
      canvasCtx.lineTo(wavePoints[i].x, wavePoints[i].y);
    }

    canvasCtx.lineTo(canvasWidth, canvasHeight);
    canvasCtx.closePath();
    canvasCtx.fillStyle = wave.color;
    canvasCtx.fill();
  });

  canvasCtx.draw();
};

const updateWavePhase = () => {
  wavePhase += 1.5; // 增加相位更新速率
};

// #ifdef H5
const animateH5 = () => {
  if (!isPageVisible) return; // H5 也检查页面可见性
  updateWavePhase();
  drawWaves();
  animationFrameId = requestAnimationFrame(animateH5);
};
// #endif

// --- 生命周期与页面事件 ---

// 页面首次渲染完成时尝试初始化
onReady(() => {
  console.log("Page Ready (onReady). Attempting initial canvas init.");
  initCanvas();
});

// 页面显示/从后台返回前台时
onShow(() => {
  isPageVisible = true; // 标记页面可见
  console.log("Page Show (onShow). Checking canvas status.");
  // 如果 Canvas 之前已就绪，尝试重启动画；否则尝试重新初始化
  if (isCanvasReady && canvasCtx) {
    console.log("Canvas was ready. Restarting animation loop.");
    startAnimationLoop();
  } else {
    console.log("Canvas not ready or context lost. Re-initializing canvas.");
    // 确保清理旧的动画和上下文引用
    stopAnimationLoop();
    canvasCtx = null;
    isCanvasReady = false;
    initCanvas(); // 尝试重新初始化
  }
});

// 页面隐藏/进入后台时
onHide(() => {
  isPageVisible = false; // 标记页面不可见
  console.log("Page Hide (onHide). Stopping animation loop.");
  stopAnimationLoop(); // 停止动画节省资源
});

// 组件卸载时
onUnmounted(() => {
  isPageVisible = false;
  console.log("Page Unmounted. Stopping animation loop and cleaning context.");
  stopAnimationLoop();
  canvasCtx = null; // 清理上下文引用
  isCanvasReady = false;
});

/**
 * 手机号脱敏处理
 * @param {string} phone - 手机号码
 * @returns {string} 脱敏后的手机号
 */
const maskPhoneNumber = (phone) => {
  if (!phone || typeof phone !== 'string') {
    return phone || '';
  }
  
  // 移除所有非数字字符
  const cleanPhone = phone.replace(/\D/g, '');
  
  // 检查是否为有效的11位手机号
  if (cleanPhone.length !== 11) {
    return phone;
  }
  
  // 格式化为 182****9901
  return `${cleanPhone.slice(0, 3)}****${cleanPhone.slice(-4)}`;
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 查看聊天记录
const viewChatHistory = () => {
  if (!pageParams.value.chatid) {
    uni.showToast({
      title: '无法获取聊天ID',
      icon: 'none'
    });
    return;
  }

  // 构建跳转参数，包含聊天类型信息
  const params = {
    chatid: pageParams.value.chatid,
    sourceTypecode: pageParams.value.sourceTypecode || 1, // 使用页面参数中的聊天类型
    nickname: encodeURIComponent(pageParams.value.nickname || ''),
    avatar: encodeURIComponent(pageParams.value.avatar || '')
  };

  const queryString = Object.entries(params)
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  uni.navigateTo({
    url: `/pages/chat-history/chat-history?${queryString}`
  });
};

// 切换消息置顶
const togglePinMessage = (e) => {
  settings.value.pinMessage = e.detail.value;
};

// 切换消息免打扰
const toggleMuteNotifications = (e) => {
  settings.value.muteNotifications = e.detail.value;
};

// 对话框配置
const modalConfig = ref({
  visible: false,
  content: '',
  confirmText: '确认',
  cancelText: '取消',
  onConfirm: () => {},
  onCancel: () => {}
});

// 显示对话框的通用方法
const showCustomModal = (options) => {
  modalConfig.value = {
    visible: true,
    content: options.content || '',
    confirmText: options.confirmText || '确认',
    cancelText: options.cancelText || '取消',
    onConfirm: options.success ? () => options.success({ confirm: true }) : () => {},
    onCancel: options.success ? () => options.success({ confirm: false }) : () => {}
  };
};

// 清空聊天记录
const clearChatHistory = () => {
  const chatName = pageParams.value.nickname || userInfo.value.nickname;
  console.log(chatName, 'chatNamechatName')
  showCustomModal({
    content: $t('chatInfo.confirmClearHistory'),
    confirmText: $t('chatInfo.clearChatHistory'),
    cancelText: $t('common.cancel'),
    success: async function(res) {
      if (res.confirm) {
        try {
          console.log('开始清空聊天记录...');
          console.log('聊天参数:', pageParams.value);
          
          let result;
          if (pageParams.value.chatid) {
            result = await clearChatMessages(pageParams.value.chatid);
          } else {
            // 如果没有chatid，提示错误
            throw new Error('无法获取聊天ID，清空失败');
          }
          console.log('清空结果:', result);

          // 删除聊天列表中的对应记录
          console.log('删除聊天列表记录，chatid:', pageParams.value.chatid);
          store.commit('delChatItem', {
            chatid: pageParams.value.chatid
          });
          console.log('聊天列表记录已删除');

          uni.hideLoading();
          uni.showToast({
            title: $t('chatInfo.historyCleared'),
            icon: 'none',
            duration: 2000
          });
          
          // 通知聊天页面刷新
          uni.$emit('chatHistoryCleared', {
            chatid: pageParams.value.chatid
          });

          // 通知聊天列表页面刷新
          uni.$emit('chatListUpdated', {
            action: 'delete',
            chatid: pageParams.value.chatid
          });
        } catch (error) {
          uni.hideLoading();
          console.error('清空聊天记录失败:', error);
        }
      }
    }
  });
};

// 加入黑名单
const addToBlacklist = () => {
  showCustomModal({
    content: $t('chatInfo.confirmAddBlacklist'),
    confirmText: $t('common.confirm'),
    cancelText: $t('common.cancel'),
    success: async (res) => {
      if (res.confirm) {
        const { data: res } = await setBanFriend(userInfo.value.Friend, true)
        if(res.code==0) {
            uni.showToast({
              title: $t('chatInfo.addedToBlacklist'),
              icon: 'none'
          });
        }
        
      }
    }
  });
};

// 删除好友
const deleteFriend = () => {
  showCustomModal({
    content: $t('chatInfo.confirmDeleteFriend'),
    confirmText: $t('common.confirm'),
    cancelText: $t('common.cancel'),
    success: async (res)=> {
      if (res.confirm) {
        try {
          const res = await delFriend(userInfo.value.Friend)
          if(res.data.code == 0){
            //  删除聊天记录（SQLite）
            const chatId = userInfo.value.Friend.toString();
            try {
              await clearChatMessages(chatId);
              console.log('聊天记录删除成功');
            } catch (error) {
              console.error('删除聊天记录失败:', error);
            }
            
            // 删除聊天列表项（Vuex）
            uni.$u.store.commit('delChatItem', {
              chatid: chatId
            });
            
            // 更新好友列表（从Vuex中移除该好友）
            const currentFriendList = uni.$u.store.state.friendList || [];
            const updatedFriendList = currentFriendList.filter(friend => 
              friend.id != userInfo.value.Friend && 
              friend.Friend != userInfo.value.Friend
            );
            uni.$u.store.commit('setFriendList', updatedFriendList);
            
            uni.hideLoading();
            uni.showToast({
              title: $t('chatInfo.friendDeleted'),
              icon: 'none'
            });
            
            setTimeout(() => {
              uni.navigateBack({
                delta: 3 // 返回到好友列表页面
              });
            }, 1000);
          } else {
            uni.hideLoading();
            uni.showToast({
              title: res.data.msg || $t('common.failed'),
              icon:'none'
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('删除好友过程中出错:', error);
          uni.showToast({
            title: $t('common.failed'),
            icon: 'none'
          });
        }
      }
    }
  });
};
</script>

<style lang="scss" scoped>
/* 页面根元素样式 */
page {
  height: 100vh;
  overflow: hidden;
  background-color: #f8f8f8;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.profile-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  /* iOS 滚动橡皮筋效果禁用 */
  overscroll-behavior: none;
  -webkit-overflow-scrolling: none;
  touch-action: none;
}

/* 状态栏占位 */
.status_bar {
  height: var(--status-bar-height);
  width: 100%;
  background-color: #FF3366;
  flex-shrink: 0;
}

/* 顶部导航栏 */
.nav-bar {
  position: relative;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FF3366;
  color: #fff;
  z-index: 100;
}

.back-btn {
  background-color: white;
  width: 53rpx;
  height: 53rpx;
  border-radius:50%;
  position: absolute;
  left: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-left {
  width: 26rpx;
  height: 26rpx;
  transform: rotate(180deg); /* 水平翻转箭头图标 */
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.header-section {
  position: relative;
  height: 430rpx;
  margin-bottom: 0;
  flex-shrink: 0;
  overflow: visible;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(180deg, #FF3366 0%, #FF5A5F 100%);
  overflow: hidden;
  z-index: 2;
}

.header-background::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  /* 调整渐变，使其包含三个颜色点：透明、白色和白色 */
  background: linear-gradient(170deg,
      transparent 60%,
      /* 上部分保持透明 */
      #fff 60.5%,
      /* 开始斜线 */
      #fff 100%
      /* 底部填充白色 */
    );
  z-index: 3;
}

.wave-canvas {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 4;
}

.user-info-area {
  position: absolute;
  top: 250rpx;
  left: 40rpx;
  right: 40rpx;
  display: flex;
  align-items: flex-start;
  z-index: 10;
}

.avatar-container {
  position: relative;
  margin-right: 38rpx;
}

.avatar {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  border: 4rpx solid #fff;
  background-color: #eee;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.camera-icon-wrapper {
  position: absolute;
  bottom: 12rpx;
  right: 0rpx;
  width: 44rpx;
  height: 44rpx;
  background-color: #ff5a5f;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.camera-icon {
  width: 42rpx;
  height: 42rpx;
}

.gender-icon-wrapper {
  position: absolute;
  bottom: 12rpx;
  right: 0rpx;
  width: 44rpx;
  height: 44rpx;
  background-color: #ff5a5f;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.gender-icon {
  font-size: 32rpx;
  color: #fff;
}

.text-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-top: 70rpx;

  .nickname {
    font-size: 32rpx;
    color: #252525;
    margin-bottom: 10rpx;
    font-weight: 600;
    line-height: 1;
  }

  .phone {
    font-size: 24rpx;
    color: #5C5C5C;
    font-weight: 500;
    line-height: 1;
  }
}

.list-area {
  margin-top: 0;
  flex: 1;
  background-color: #f8f8f8;
  overflow: hidden;
  position: relative;
  /* iOS 滚动橡皮筋效果禁用 */
  overscroll-behavior: none;
  -webkit-overflow-scrolling: none;
  touch-action: none;
}

.list-item-wrapper {
  background-color: #fff;
  margin: 30rpx 0;
  overflow: hidden;

  &:active {
    background-color: #f9f9f9;
  }
}

.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 45rpx;

  .item-left {
    display: flex;
    align-items: center;
  }
}

.function-text {
  font-size: 32rpx;
  color: #333;
}

.danger-section {
  margin-top: 40rpx;
}

.danger-item {
  margin-top: 10rpx;
}

.danger-text {
  color: #FF3366;
  text-align: center;
}

/* 图标样式 */
.iconfont {
  font-family: "iconfont";
}

.icon-right:after {
  content: ">";
  color: #ccc;
}

.icon-left:after {
  content: "<";
}

/* 开关样式调整 */
switch {
  transform: scale(0.8);
}

/* 添加全局样式 */
::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

/* 右箭头图标 */
.arrow-right {
  width: 24rpx;
  height: 24rpx;
}

/* 删除不再需要的图标样式 */
/* .icon-right:after {
  content: ">";
  color: #ccc;
} */
</style>