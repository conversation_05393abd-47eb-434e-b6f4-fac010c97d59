<template>
	<!-- 页面根容器，设置左右外边距 -->
	<view style="margin: 0 5vw">
		<!-- Logo 显示区域 -->
		<view style="
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-content: end;
        padding-top: 26%;
      ">
			<image src="../../static/jlogo.webp" style="height: 245rpx; width: 245rpx" />
		</view>

		<!-- 步骤 1: 输入邀请码 -->
		<view v-show="step === 0">
			<!-- 欢迎语 -->
			<view style="
          font-size: 1rem;
          text-align: center;
          padding-top: 5vh;
          color: #222222;
          font-weight: bold;
        ">
				欢迎使用久聊
			</view>
			<!-- 应用描述 -->
			<view style="
          font-weight: bold;
          color: #909090;
          text-align: center;
          font-size: 0.8rem;
          padding-top: 1.6vh;
        ">
				一款最安全的通讯应用！所有信息都
				是通过端对端加密处理，您可以随时随地畅聊!
			</view>
			<!-- 邀请码输入提示 -->
			<view style="
          padding-top: 8vh;
          color: #fd3357;
          font-weight: bold;
          font-size: 1.1rem;
          display: flex;
          justify-content: left;
          flex-direction: column;
        ">
				<text> 请输入您的邀请码: </text>
				<!-- 装饰性下划线 -->
				<text style="
            width: 40vw;
            border-bottom: 4rpx solid #fd3357;
            padding-bottom: 4rpx;
            border-radius: 4rpx;
          " />
			</view>
			<!-- 邀请码输入框容器 -->
			<view style="
          border: 3rpx solid #cecfcf;
          height: 100rpx;
          border-radius: 12rpx;
          margin-top: 2vh;
          display: flex;
          align-items: center;
          justify-content: center;
        ">
				<!-- 邀请码输入框 (pass) -->
				<input style="height: 100rpx; font-size: 0.9rem; margin: 0 1vh; flex: 6" v-model="pass" type="digit" placeholder="请输入邀请码" />
				<!-- 清空按钮 (pass 有内容时显示) -->
				<view v-if="pass.length > 0" @click="clearPass" style="
            margin: 26rpx;
            width: 43rpx;
            height: 43rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #cecfcf;
            border-radius: 50%;
            cursor: pointer; /* 增加手型指针 */
          ">
					<text style="color: white">X</text>
				</view>
			</view>
			<!-- 确定按钮 (获取服务地址) -->
			<view @click="getService" style="
          font-weight: bold;
          background-color: #fd3357;
          color: white;
          border-radius: 26rpx;
          text-align: center;
          margin-top: 68rpx;
          padding: 2.6vh 0;
          cursor: pointer; /* 增加手型指针 */
        ">
				确定</view>
		</view>

		<!-- 步骤 2: 手机号登录/注册 -->
		<view v-show="step === 1">
			<!-- 标题 -->
			<view style="
          padding-top: 10.5vh;
          color: #222222;
          font-weight: bold;
          font-size: 1.18rem;
          display: flex;
          justify-content: left;
          flex-direction: column;
        ">
				<text> 手机号登录 </text>
			</view>
			<!-- 手机号输入区域 -->
			<view style="
          border: 2rpx solid #cecfcf;
          height: 100rpx;
          border-radius: 12rpx 12rpx 0 0;
          margin-top: 1.8vh;
          border-bottom: 0;
          display: flex;
          align-items: center;
          padding: 0 3vw;
        ">
				<!-- 国家码 -->
				<view style="display: flex; align-items: center">
					<view style="
              color: #222222;
              font-size: 0.9rem;
              font-weight: bold;
              padding-right: 10rpx;
            ">
						+86
					</view>
					<!-- 分隔线 -->
					<view style="background-color: #cecfcf; width: 2.5rpx; height: 45rpx" />
				</view>
				<!-- 手机号输入框 (user) -->
				<input style="
            width: 100%; /* 调整宽度以适应 flex 布局 */
            height: 100rpx;
            font-size: 0.9rem;
            margin: 0 0 0 2vw;
            display: flex; /* input 默认是 inline-block，改为 flex 或 block 可能更好 */
          " v-model="user" type="number" placeholder="请输入手机号" maxlength="11" />
			</view>
			<!-- 手机验证码输入区域 -->
			<!-- 手机验证码输入区域 -->
			<view style="
			          border: 2rpx solid #cecfcf;
			          height: 100rpx;
			          border-radius: 0 0 12rpx 12rpx;
			          display: flex;
			          justify-content: space-between; /* 改为 space-between 让元素靠两边 */
			          align-items: center;
			          padding: 0 3vw; /* 增加内边距 */
			        ">
				<!-- 手机验证码输入框 (security) -->
				<input style="flex: 1; height: 100rpx; font-size: 0.9rem; margin-right: 2vw;" v-model="security"
					type="number" placeholder-style="color:#CECFCF;" placeholder="请输入验证码" maxlength="6" />
				<!-- 获取验证码按钮 -->
				<view @click="handleGetCaptchaClick" :style="{
			            fontSize: '0.9rem',
			            fontWeight: 'bold',
			            color: isCountingDown ? '#999999' : '#fd3357', // 倒计时期间变灰
			            display: 'flex',
			            justifyContent: 'center',
			            alignItems: 'center',
			            whiteSpace: 'nowrap',
			            cursor: isCountingDown ? 'not-allowed' : 'pointer', // 倒计时期间禁用指针
			            pointerEvents: isCountingDown ? 'none' : 'auto' // 倒计时期间禁用事件
			          }">
			          		{{ isCountingDown ? `重新获取 (${countdownSeconds}s)` : '获取验证码' }}
			          </view>
			          </view>
			<!-- 注册并登录按钮 -->
			<view @click="toLogin" style="
          font-weight: bold;
          background-color: #fd3357;
          color: white;
          border-radius: 26rpx;
          text-align: center;
          margin-top: 12vh;
          padding: 2.6vh 0;
          cursor: pointer; /* 增加手型指针 */
        ">
				注册并登录</view>

			<!-- 同意协议区域 -->
			<view style="
          display: flex;
          flex-direction: row;
          margin-top: 22rpx;
          font-size: 0.7rem;
          font-weight: bold;
          align-items: center;
          cursor: pointer; /* 增加手型指针 */
        " @click="AcceptSwitch">
				<!-- 同意勾选框 -->
				<text :class="[isToggled ? 'affirm-on' : 'affirm']" style="
            width: 40rpx;
            height: 40rpx;
            border-radius: 50%;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10rpx; /* 增加右边距 */
          ">√</text>
				<!-- 协议文本 -->
				<text style="color: #909090">我已阅读并同意</text>
				<text style="color: #fd3357">《服务协议》</text>
				<text style="color: #909090">和</text><text style="color: #fd3357">《隐私政策》</text>
			</view>
		</view>

		<!-- 步骤 3: 图形验证码蒙层 -->
		<view v-if="showOverlay" class="overlay" :class="{ show: showOverlay }" @touchmove.prevent>
			<!-- 模态框内容 -->
			<view class="modal-content" @click.stop>
				<!-- 标题 -->
				<text style="font-weight: 800; font-size: 1.1rem; padding-top: 60rpx">验证码</text>
				<!-- 输入和图片区域 -->
				<view style="
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-top: 40rpx;
            margin-bottom: 20rpx;
            justify-content: space-evenly;
            width: 100%; /* 确保宽度 */
            padding: 0 30rpx; /* 增加左右内边距 */
            box-sizing: border-box; /* 防止 padding 影响宽度 */
          ">
					<!-- 图形验证码输入框 (captcha) -->
					<input type="text" v-model="captcha" placeholder="请输入验证码" style="
              background-color: #f5f6f8;
              /* margin: 0 20rpx 0 0; */ /* 移除右边距 */
              padding: 30rpx;
              /* width: 45%; */ /* 改为 flex 控制 */
              flex: 1; /* 占据剩余空间 */
              text-align: center;
              border-radius: 13rpx;
              /* width: 30vw; */ /* 移除固定宽度 */
              font-size: 1rem;
              margin-right: 20rpx; /* 增加右边距 */
            " maxlength="5" />
					<!-- 图形验证码图片区域 -->
					<view style="width: 200rpx; height: 110rpx; flex-shrink: 0;">
						<!-- 图片 -->
						<image :src="captchaSrc" style="width: 200rpx; height: 100rpx; display: block;" @click="refreshCaptcha" />
						<!-- 换一张 文本 -->
						<text @click="refreshCaptcha" style="
                display: flex;
                justify-content: flex-end;
                color: #909090;
                font-size: 0.9rem;
                cursor: pointer; /* 增加手型指针 */
              ">换一张</text>
					</view>
				</view>
				<!-- 底部按钮区域 -->
				<view style="
            display: flex;
            flex-direction: row;
            justify-content: space-around;
            border-top: 2rpx solid #cecfcf;
            width: 100%;
            /* margin: 40rpx 30rpx 30rpx 30rpx; */ /* 移除 margin */
            padding: 20rpx 0; /* 调整 padding */
            font-size: 1rem;
            margin-top: 20rpx; /* 调整上边距 */
          ">
					<!-- 取消按钮 -->
					<view @click="CancelCaptcha" style="color: #222222; font-weight: bold; cursor: pointer;">
						取消
					</view>
					<!-- 确定按钮 (验证图形验证码) -->
					<view @click="verifyCaptcha" style="color: #fd3357; font-weight: bold; cursor: pointer;">
						确定
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		watch,
		onUnmounted, // 引入 onUnmounted
		// onMounted // App.vue 中已有类似逻辑，此处通常不需要
	} from "vue";
	import {
		GetServiceHost, // 获取服务地址列表 API
		GetCaptcha, // 获取图形验证码 API
		GetPhoneCaptcha, // 获取手机验证码 API
		LoginARegister, // 登录或注册 API
		// TokenLogin // App.vue 中已处理 Token 登录
	} from "../../api/api";
	import { getFriendList, getSelfInfo,getDelay} from '@/api/friend.js';
	import { initConnectionService } from "../../utils/connectionService.js";
	// import { onLoad } from "@dcloudio/uni-app"; // 如果需要在 onLoad 中处理逻辑，取消注释
	import {
		ServiceExam, // 检查服务地址是否可用
		SetBaseUrl, // 设置 API 请求的基础 URL
	} from "../../api/http";
	import { useStore } from 'vuex';
	// --- 响应式变量定义 ---
	const pass = ref(""); // 邀请码
	const step = ref(0); // 当前步骤 (0: 邀请码, 1: 手机号登录, 2: 图形验证码 - 通过 showOverlay 控制)
	const user = ref(""); // 手机号码
	const security = ref(""); // 手机验证码
	const isToggled = ref(false); // 是否同意协议
	const showOverlay = ref(false); // 是否显示图形验证码蒙层
	const captcha = ref(""); // 图形验证码输入值
	const captchaID = ref(""); // 图形验证码 ID
	const captchaSrc = ref(""); // 图形验证码图片 Base64 或 URL
	const store = useStore();
	// --- 生命周期与监听 ---

	// 监听步骤变化，旧的 watch(step) 逻辑已通过 v-if="showOverlay" 实现，不再需要显式 watch
	// watch(step, (newVal) => {
	// 	// 当 step 变为特定值时显示蒙层 (现在由 handleGetCaptchaClick 控制)
	// });

	// onLoad(() => {
	//   // 页面加载时执行的逻辑，例如获取路由参数
	// });

	// onMounted(() => {
	//   // 组件挂载后执行的逻辑
	//   // 检查本地 Token 登录的逻辑已移至 App.vue 的 onLaunch 中处理，此处通常不需要重复执行。
	//   // 如果需要在此页面单独处理某些挂载逻辑，可以取消注释。
	//   // let baseurl = uni.getStorageSync("baseUrl");
	//   // let token = uni.getStorageSync("token");
	//   // if (baseurl && token) {
	//   //   uni.showLoading({ title: "自动登录中...", mask: true });
	//   //   SetBaseUrl(baseurl);
	//   //   TokenLogin().then((res) => {
	//   //     console.log("Login page TokenLogin success:", res);
	//   //     uni.switchTab({ url: "/pages/chats/chats" });
	//   //   }).catch(err => {
	//   //     console.error("Login page TokenLogin failed:", err);
	//   //     // Token 失效，停留在登录页，无需额外处理
	//   //   }).finally(() => {
	//   //     uni.hideLoading();
	//   //   });
	//   // }
	// });

	// --- 方法定义 ---

	/**
	 * 清空邀请码输入框
	 */
	const clearPass = () => {
		pass.value = "";
		// uni.showToast({
		// 	title: "已清空!",
		// 	icon: "none",
		// 	duration: 1000, // 缩短提示时间
		// 	position: "top",
		// });
	};

	/**
	 * 切换同意协议状态
	 */
	const AcceptSwitch = () => {
		isToggled.value = !isToggled.value;
	};

	/**
	 * 步骤 1: 点击确定，根据邀请码获取并验证服务地址
	 */
	const getService = async () => {
		// 基础校验
		if (!pass.value) {
			uni.showToast({ title: "请输入邀请码！", icon: "none", position: "top" });
			return;
		}

		uni.showLoading({ title: "验证邀请码...", mask: true });
		try {
			// 1. 获取服务地址列表
			const res = await GetServiceHost(pass.value);
			console.log("GetServiceHost response:", res);

			if (res.statusCode !== 200 || !res.data || res.data.code !== 0) {
				const errorMsg = res.data?.code === 2 ? "邀请码无效或已使用!" : "邀请码验证失败";
				throw new Error(errorMsg);
			}

			const urls = res.data.data;
			if (!urls || urls.length === 0) {
				throw new Error("未获取到有效的服务地址");
			}

			// 2. 逐个验证服务地址可用性
			let successUrl = null;
			uni.showLoading({ title: "连接服务器...", mask: true }); // 更新提示
			for (const url of urls) {
				try {
					console.log(`Testing service URL: ${url}`);
					// ServiceExam 内部应该处理请求和判断状态码
					const examResult = await ServiceExam(url);
					// 假设 ServiceExam 成功时返回包含状态码的对象或直接成功，失败则抛出错误
					// 这里需要根据 ServiceExam 的实际实现来判断
					// 简化假设：如果 ServiceExam 没抛错，就认为成功
					console.log(`Service URL ${url} is available.`);
					successUrl = url;
					break; // 找到一个可用的就停止
				} catch (e) {
					console.warn(`Service URL ${url} failed:`, e.message || e);
					continue; // 测试下一个地址
				}
			}

			// 3. 处理结果
			if (successUrl) {
				console.log(`Using service URL: ${successUrl}`);
				SetBaseUrl(successUrl); // 设置全局 BaseUrl
				uni.setStorageSync("baseUrl", successUrl); // 持久化存储
				step.value = 1; // 进入下一步
			} else {
				throw new Error("无法连接到有效的服务，请稍后重试");
			}
		} catch (error) {
			console.error("getService error:", error);
			uni.showToast({
				title: error.message || "操作失败，请检查网络或邀请码",
				icon: "none",
				duration: 2000,
				position: "top",
			});
		} finally {
			uni.hideLoading();
		}
	};

	/**
	 * 步骤 2: 点击 "获取验证码" 按钮的处理函数
	 */
	const isCountingDown = ref(false); // Add this line to declare and initialize isCountingDown
	const countdownSeconds = ref(60); // Assuming you also need this for the countdown logic
	const handleGetCaptchaClick = async () => {
		if (isCountingDown.value) { // Access reactive refs with .value
			return; // Do nothing if already counting down
		}
		// 手机号格式校验
		if (!user.value) {
			uni.showToast({ title: "请输入手机号码！", icon: "none", position: "top" });
			return;
		}
		// 简单的11位数字校验
		if (!/^\d{11}$/.test(user.value)) {
			uni.showToast({ title: "手机号码格式不正确！", icon: "none", position: "top" });
			return;
		}
		// 显示图形验证码蒙层并获取图形验证码
		showOverlay.value = true;
		refreshCaptcha(); // 获取新的图形验证码
	};

	/**
	 * 刷新或获取图形验证码
	 */
	const refreshCaptcha = () => {
		// 清空之前的输入
		captcha.value = "";
		// 调用 API 获取新的图形验证码
		GetCaptcha(Date.now()) // 使用时间戳作为参数避免缓存
			.then((res) => {
				console.log("GetCaptcha response:", res);
				if (res.statusCode === 200 && res.data?.code === 0 && res.data.data) {
					captchaID.value = res.data.data.CaptID;
					// 确保返回的是 Base64 格式，并添加前缀
					if (res.data.data.IMG && !res.data.data.IMG.startsWith('data:image')) {
						captchaSrc.value = 'data:image/png;base64,' + res.data.data.IMG;
					} else {
						captchaSrc.value = res.data.data.IMG || "";
					}
				} else {
					throw new Error(res.data?.msg || "获取图形验证码失败");
				}
			})
			.catch(error => {
				console.error("refreshCaptcha error:", error);
				uni.showToast({ title: error.message || "获取图形验证码失败", icon: "none", position: "top" });
				// 获取失败时可以考虑关闭蒙层或提示用户重试
				// showOverlay.value = false;
			});
	};


	/**
	 * 步骤 3: 点击图形验证码蒙层的 "确定" 按钮，验证图形验证码并获取手机验证码
	 */
	const verifyCaptcha = () => {
		// 图形验证码输入校验
		if (!captcha.value) {
			uni.showToast({ title: "请输入图形验证码！", icon: "none", position: "top" });
			return;
		}
		// 通常图形验证码是4-6位，这里假设是5位
		if (captcha.value.length !== 5) {
			uni.showToast({ title: "图形验证码位数不正确！", icon: "none", position: "top" });
			return;
		}

		uni.showLoading({ title: "正在发送...", mask: true });
		GetPhoneCaptcha({
				iphoneNum: user.value,
				code: captcha.value,
				CaptID: captchaID.value,
			})
			.then((res) => {
				console.log("GetPhoneCaptcha response:", res);
				// 检查 set-token 是否存在于 header 中 (uni.request 返回的 header key 可能是小写)
				const tokenKey = Object.keys(res.header || {}).find(key => key.toLowerCase() === 'set-token');
				const receivedToken = tokenKey ? res.header[tokenKey] : null;
				console.log("验证码第一层 token:", receivedToken);

				if (res.statusCode === 200 && res.data?.code === 0) {
					// 手机验证码发送成功
					uni.showToast({ title: "验证码已发送", icon: "success", duration: 1500 });
					if (receivedToken) {
						// 存储获取到的临时 token (如果接口设计如此)
						uni.setStorageSync("token", receivedToken);
					}
					showOverlay.value = false; // 关闭蒙层
					// 可能需要启动一个倒计时器显示在“获取验证码”按钮上
					// 这里可以使用 uni.request 来实现倒计时逻辑
					// 或者使用 uni.createSelectorQuery 来获取按钮并设置倒计时
					isCountingDown.value = true; // 开始倒计时
					const countdownTimer = setInterval(() => {
						countdownSeconds.value--; // 每秒减1
						if (countdownSeconds.value <= 0) {
							clearInterval(countdownTimer); // 倒计时结束
							isCountingDown.value = false; // 停止倒计时
							countdownSeconds.value = 60; // 重置倒计时时间
						}
					}, 1000);
					step.value = 1; // 进入下一步

				} else {
					// 验证码发送失败，提示错误信息
					uni.showToast({
						title: res.data?.msg || "图形验证码错误或发送失败",
						icon: "none",
						duration: 2000,
						position: "top",
					});
					// 刷新图形验证码，让用户重试
					refreshCaptcha();
				}
			})
			.catch(error => {
				console.error("verifyCaptcha error:", error);
				uni.showToast({ title: "请求失败，请重试", icon: "none", position: "top" });
				refreshCaptcha(); // 请求失败也刷新图形验证码
			})
			.finally(() => {
				uni.hideLoading();
			});
	};

	/**
	 * 步骤 3: 点击图形验证码蒙层的 "取消" 按钮
	 */
	const CancelCaptcha = () => {
		showOverlay.value = false; // 关闭蒙层
	};

	/**
	 * 步骤 2: 点击 "注册并登录" 按钮
	 */
	const toLogin = () => {
		// 校验是否同意协议
		if (!isToggled.value) {
			uni.showToast({ title: "请阅读并同意服务协议和隐私政策", icon: "none", duration: 2000, position: "top" });
			return;
		}
		// 校验手机号和手机验证码是否输入
		if (!user.value) {
			uni.showToast({ title: "请输入手机号码！", icon: "none", position: "top" });
			return;
		}
		if (!/^\d{11}$/.test(user.value)) {
			uni.showToast({ title: "手机号码格式不正确！", icon: "none", position: "top" });
			return;
		}
		if (!security.value) {
			uni.showToast({ title: "请输入手机验证码！", icon: "none", position: "top" });
			return;
		}
		// 手机验证码通常是 6 位
		if (security.value.length !== 6) {
			uni.showToast({ title: "手机验证码位数不正确！", icon: "none", position: "top" });
			return;
		}

		uni.showLoading({ title: "登录中...", mask: true });

		// 准备登录参数
		let params = {
			iphoneNum: user.value,
			code: security.value,
		};

		// 获取设备信息，用于区分平台 (可选，根据后端需求)
		try {
			const deviceInfo = uni.getDeviceInfo();
			if (deviceInfo.platform === "ios") {
				params.identifier = "ios"; // 或后端需要的其他标识符
			} else if (deviceInfo.platform === "android") {
				params.identifier = "android"; // 或后端需要的其他标识符
			}
			// 可以添加更多设备信息，如 deviceId, osVersion 等，如果后端需要
			// params.deviceId = deviceInfo.deviceId;
			console.log("Device Info:", deviceInfo);
		} catch (e) {
			console.error("获取设备信息失败:", e);
		}

		console.log("LoginARegister params:", params);

		// 调用登录/注册 API
		LoginARegister(params)
			.then((res) => {
				console.log("LoginARegister response:", res);
				// 检查登录/注册是否成功，并获取最终的 token
				const tokenKey = Object.keys(res.header || {}).find(key => key.toLowerCase() === 'set-token');
				const finalToken = tokenKey ? res.header[tokenKey] : null;
				
				if (res.statusCode === 200 && res.data?.code === 0 && finalToken) {
					// 登录/注册成功
					uni.setStorageSync("userInfo", res.data.data);
					uni.setStorageSync("userId", res.data.data.ID);
					
					uni.setStorageSync("token", finalToken); // 存储最终的有效 token
					uni.removeStorageSync("phoneCaptchaToken"); // 清除临时的手机验证码 token (如果之前存了)
					uni.showToast({ title: "登录成功", icon: "success", duration: 1500 });
					getFriendList().then(res=>{
						console.log(res)
						if(res.data.code==0){
							store.commit('setFriendList', res.data.data);
						}
					})
					// 初始化连接服务（数据库和WebSocket）
					initConnectionService(finalToken,true)
						.then((success) => {
							if (success) {
								console.log('连接服务初始化成功');
								// 跳转到主 Tab 页
								uni.switchTab({
									url: "/pages/chats/chats",
									fail: (err) => {
										console.error("SwitchTab failed:", err);
									}
								});
							} else {
								console.error('连接服务初始化失败');
								uni.showToast({
									title: "服务初始化失败，请重试",
									icon: "none",
									duration: 2000
								});
							}
						})
						.catch((error) => {
							console.error('连接服务初始化异常:', error);
							uni.showToast({
								title: "服务初始化异常，请重试",
								icon: "none",
								duration: 2000
							});
						});
				} else {
					// 登录/注册失败
					throw new Error(res.data?.msg || "登录失败，请检查验证码或稍后重试");
				}
			})
			.catch((err) => {
				console.error("toLogin error:", err);
				uni.showToast({
					title: err.message || "登录请求失败",
					icon: "none",
					duration: 2000,
					position: "top",
				});
			})
			.finally(() => {
				uni.hideLoading();
			});
	};
</script>

<style lang="scss" scoped> /* 改为 scoped 以避免样式污染 */
	/* 同意按钮选中状态 */
	.affirm-on {
		background-color: #fd3357;
	}

	/* 同意按钮未选中状态 */
	.affirm {
		background-color: #cecfcf;
	}

	/* 修复 Web 端蒙层问题 */
	.overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 9999;
		/* 确保足够高 */
		display: flex;
		justify-content: center;
		align-items: center;

		opacity: 0;
		transition: opacity 0.3s ease;
		pointer-events: none;
		/* 初始不可点击 */
	}

	.overlay.show {
		opacity: 1;
		pointer-events: auto;
		/* 显示时可点击 */
	}

	.modal-content {
		background-color: white;
		border-radius: 16rpx;
		margin-top: 20%;
		width: 84%;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		pointer-events: auto;
		/* 确保模态框内容可交互 */
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		overflow: hidden;
		/* 防止内容溢出圆角 */
	}

	/* 可以添加一些全局输入框样式优化 */
	input {
		outline: none;
		border: none;
		background-color: transparent;
		padding: 0;
	}
</style>