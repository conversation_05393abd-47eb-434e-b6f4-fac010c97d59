import http from "./http"

// 获取服务器host
export const GetServiceHost = (key) => {
	return http(`gethost`, {
		"key": key,
	})
}

// 获取验证码or刷新图形验证码的
export const GetCaptcha = (tsmtp) => {
	return http(`user/getCode`, {
		"t": tsmtp,
	}, "GET")
}

// 获取手机验证码or提交图形验证码进行验证登录
export const GetPhoneCaptcha = (options) => {
	return http(`user/getIphoneMsg`, {
		"iphoneNum": options.iphoneNum,
		"code": options.code,
		"CaptID": options.CaptID
	}, "POST")
}

// 登录&注册
export const LoginARegister = (options) => {
	return http(`user/login`, {
		"iphoneNum": options.iphoneNum,
		"code": options.code,
		...(options.identifier ? { identifier: options.identifier } : {})
	}, "POST")
}

// 通过用户id获取用户信息
export const GetUserByID = (id) => {
	return http(`user/getUserByID`, {
		id:id*1
	}, "POST")
}


// tokenLogin
export const TokenLogin = (tsmtp) => {
	return http(`user/ping`)
}



// 通过用户id获取用户信息
export const setMeInfo = (options) => {
	return http(`user/setMe`, options, "POST")
}

export const getConfig = (tsmtp) => {
	return http(`getConfig`)
}
