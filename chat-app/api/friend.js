import http from "./http"

// 获取好友列表
export const getFriendList = (options) => {
    return http(`friend/getList`, {
        ...options
    }, "POST")
}

// 添加好友
export const addFriend = (options) => {
    return http(`friend/add`, {
        ...options
    }, "POST")
}

// 获取等待同意申请列表
export const getDelay = (options) => {
    return http(`friend/getDelay`, {
        ...options
    }, "POST")
}
// 拉黑好友
export const banFriend = (options) => {
    return http(`friend/ban`, {
        ...options
    }, "POST")
}

// 设置好友 
export const setFriend = (options) => {
    return http(`friend/set`, {
        ...options
    }, "POST")
}
// 删除好友
export const removeFriend = (options) => {
    return http(`friend/remove`, {
        ...options
    }, "POST")
}


// 获取自己的信息
export const getSelfInfo = () => {
    return http(`user/getMe`, {}, "POST")
}

// 搜索用户/群组
export const searchUser = (options) => {
    return http(`search`, {
       ...options
    }, "POST")
}

// 通过用户id获取好友信息
export const GetFriendByID = (id) => {
	return http(`friend/get`, {
		id:id*1
	}, "POST")
}


// 通过加好友请求
export const FriendPass = (id) => {
	return http(`friend/pass`, {
		id:id*1
	}, "POST")
}

