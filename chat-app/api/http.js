let baseUrlpre = 'http://43.198.105.182:81'
const api = '/api/'

export const GetBaseUrl = () => {
	return baseUrlpre
}

export const SetBaseUrl = (newUrl) => {
	baseUrlpre = newUrl
}

// 默认初始化http
export default function http(url, data = {}, method = "GET") {
	console.log(uni.getStorageSync('token') )
	return new Promise((resolve, reject) => {
		console.log('baseUrlpre + api + url',baseUrlpre + api + url,data)
		uni.request({
			url: baseUrlpre + api + url,
			data,
			method,
			header: {
				'x-token': uni.getStorageSync("token") || '',
				'Content-Type': 'application/json',
			},
			success: (res) => {
				if (res.statusCode === 200) {
					resolve(res);
				} else {
					reject(res)
				}
			},
			fail: (err) => {
				uni.showToast({
					title: "服务器请求异常",
					icon: "none",
					duration: 2000,
					position: "top",
				})
				reject(err)
			}
		})
	})
}

// 服务器扫描测试
export const ServiceExam = (url) => {
	return new Promise((resolve, reject) => {
		uni.request({
			url: url + api + "ping",
			data: {},
			method: "GET",
			header: {
				'Content-Type': 'application/json',
			},
			success: (res) => {
				resolve({
					url,
					response: res
				})
			},
			fail: (err) => {
				reject({
					url,
					error: err
				});
			}
		})
	})
}