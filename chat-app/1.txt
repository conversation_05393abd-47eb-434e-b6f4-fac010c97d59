第一次请求参数：{
    "fromid": 49,
    "toId": 1,
    "msg": "ApMCkHEChyQqlo87Ii+WTg==",
    "typecode": 2,
    "typecode2": 0,
    "t": "2025-05-30T09:04:39.694Z"
}  返回：{
    "fromid": 49,
    "toid": 49,
    "groupID": 1,
    "id": 7,
    "typecode": 2,
    "typecode2": 0,
    "t": "2025-05-30T09:04:39.694Z",
    "msg": "ApMCkHEChyQqlo87Ii+WTg=="
}

第二次参数：{
    "fromid": 49,
    "toId": 1,
    "msg": "bz+aIdvWZwCT+Cp+pPTUcE5h5NQx0c9W/r3IodXiokc=",
    "typecode": 2,
    "typecode2": 0,
    "t": "2025-05-30T09:05:39.287Z"
}



// 创建 router.js
const navigateLock = new Set()

export const safeNavigate = (url) => {
  if (navigateLock.has(url)) return
  
  navigateLock.add(url)
  uni.navigateTo({
    url,
    complete: () => navigateLock.delete(url)
  })
}

// 调用方式
import { safeNavigate } from './router'
safeNavigate('/pages/call/voice-call')