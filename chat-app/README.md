<!-- 

具体规则
1. 实时消息（连续聊天） 显示第一次的时间
例如：
你10:00发送消息 → 显示“10:00”，
对方10:03再回复 → 不显示时间（因间隔<5分钟）；
若对方10:06回复 → 显示“10:06”，
你10:08再回复 → 不显示时间（因间隔<5分钟）；
大于5分钟的并且当天内：显示 具体时间（如“10:30”）

2. 跨天消息
昨天：显示为 “昨天” + 时间（如“昨天 18:00”）
前天 ~ 6天内：显示 星期 + 时间（如“周二 14:20”）
7天前 ~ 当年内：显示 月-日 + 时间（如“3月15日 09:10”）

跨年消息：显示 完整日期 + 时间（如“2023年12月30日 10:00”）

3. 特殊场景规则
首条消息必显时间：每次打开聊天窗口时，当天第一条消息一定显示时间。
连续消息聚合：若多条消息在5分钟内连续发送，仅第一条显示时间，后续消息隐藏时间戳。
长间隔强制显示：若两条消息间隔 >5分钟，后一条消息强制显示新时间戳。 -->