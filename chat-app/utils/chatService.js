import { chatPush } from '../api/chat.js'
// import store from '../store'
import { decryptAESBase64, encryptAESBase64 } from '../utils/decrypt.js'


export const MessageType = {
  TEXT: 0,      // 文本消息
  IMAGE: 1,     // 图片消息
  VOICE: 2,     // 语音消息
  VIDEO: 3,     // 视频消息
  FILE: 4       // 文件消息
}

const decryptMessage = (encryptedMsg) => {
  return encryptedMsg;
}

const encryptMessage = (msg) => {
  return msg;
}

// 发送消息
export const sendMessage = async (params) => {
  const messageParams = {
    ...params,
    msg: encryptAESBase64(params.msg),
    t: new Date().toISOString(), 
  };
  if(params.typecode2==10){
	  messageParams.msg=params.msg
  }
  console.log(messageParams)
  try {
    console.log(messageParams, 'messageParams')
    const result = await chatPush(messageParams);
    console.log('++++++++++',result)
    return messageParams; // 返回发送的消息对象
  } catch (error) {
    console.error('发送消息错误:', error);
    throw error; // 抛出错误以便调用方处理
  }
};
const mapTypecodeToType = (typecode) => {
  switch (typecode) {
    case MessageType.TEXT:
      return 'text';
    case MessageType.IMAGE:
      return 'image';
    case MessageType.VOICE:
      return 'voice';
    case MessageType.VIDEO:
      return 'video';
    case MessageType.FILE:
      return 'file';
    default:
      return 'text';
  }
};

/* // 更新聊天列表
const updateChatList = (targetId, messageRecord, isGroup = false) => {
  // 根据targetId查找好友信息
  let nickName = isGroup ? '群聊' : '联系人';
  let avatarUrl = isGroup ? '/static/conversation/group_avatar.png' : '/static/My/default_avatar.jpg';
  
  // 如果是用户自己发的，用当前用户头像
  if (messageRecord.self) {
    avatarUrl = uni.getStorageSync('userAvatar') || '/static/My/avatar.jpg';
  } else {
    try {
      const state = store.state;
      if (isGroup) {
        const groups = state.groupList || [];
        const group = groups.find(g => g.ID === targetId);
        if (group) {
          nickName = group.name || '群聊';
          avatarUrl = group.avatar || avatarUrl;
        }
      } else {
        const friendList = state.friendList || [];
        const friend = friendList.find(f => f.Friend === targetId || f.UserID === targetId);
        if (friend && friend.User) {
          nickName = friend.User.name || friend.Name || '联系人';
          avatarUrl = friend.User.head_img || avatarUrl;
        }
      }
    } catch (error) {
      console.error('获取联系人信息失败', error);
    }
  }
  
  const chatItem = {
    chatid: targetId,
    avatar: avatarUrl,
    nickname: nickName,
    lastMessage: formatLastMessage(messageRecord),
    timestamp: new Date(messageRecord.time).getTime(),
    isRedRead: messageRecord.self ? 0 : 1,
    isGroup
  };
  
  store.commit('setChatList', chatItem);
};
 */
const formatLastMessage = (message) => {
  switch (message.type) {
    case 'text':
      return message.content;
    case 'image':
      return '[图片]';
    case 'voice':
      return '[语音]';
    case 'video':
      return '[视频]';
    case 'file':
      return '[文件]';
    case 'voiceCall':
      return '[语音通话]';
    case 'voiceCallEnd':
      return '[语音通话]';
    default:
      return '';
  }
};