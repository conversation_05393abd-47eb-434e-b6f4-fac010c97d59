import { ref, readonly } from 'vue'
import zhLang from '../lang/zh.js'
import enLang from '../lang/en.js'


const messages = {
  zh: zhLang,
  en: enLang
}


const DEFAULT_LANG = 'zh'

// 获取当前语言
export function getCurrentLanguage() {
  return uni.getStorageSync('app_language') || DEFAULT_LANG
}

// 设置语言
export function setLanguage(lang) {
  if (messages[lang]) {
    uni.setStorageSync('app_language', lang)
    return true
  }
  return false
}

// 获取文本 - 支持嵌套路径，如 'setting.title'
export function t(key, defaultValue = '') {
  const lang = getCurrentLanguage()
  const langData = messages[lang] || messages[DEFAULT_LANG]
  
  // 嵌套路径访问
  const keys = key.split('.')
  let result = langData
  
  for (const k of keys) {
    if (result && typeof result === 'object' && result[k] !== undefined) {
      result = result[k]
    } else {
      return defaultValue || key
    }
  }
  
  return result || defaultValue || key
}

// 获取所有可用语言
export function getAvailableLanguages() {
  return Object.keys(messages).map(key => ({
    code: key,
    name: t(`languages.${key}`)
  }))
}

// 创建响应式的多语言 composable
export function useI18n() {
  const currentLang = ref(getCurrentLanguage())
  
  const switchLanguage = (lang) => {
    if (setLanguage(lang)) {
      currentLang.value = lang
      // 触发页面更新
      uni.$emit('languageChanged', lang)
      return true
    }
    return false
  }
  
  const translate = (key, defaultValue = '') => {
    currentLang.value 
    return t(key, defaultValue)
  }
  
  return {
    currentLang: readonly(currentLang),
    switchLanguage,
    t: translate,
    getAvailableLanguages
  }
}

// 全局混入方法
export const i18nMixin = {
  methods: {
    $t(key, defaultValue = '') {
      return t(key, defaultValue)
    },
    $switchLang(lang) {
      if (setLanguage(lang)) {
        // 触发全局语言变化事件
        uni.$emit('languageChanged', lang)
        return true
      }
      return false
    },
    $getCurrentLang() {
      return getCurrentLanguage()
    }
  }
}
