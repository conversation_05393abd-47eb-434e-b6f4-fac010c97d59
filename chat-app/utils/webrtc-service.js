class WebRTCService {
  constructor() {
    this.plugin = uni.requireNativePlugin('MyWebRTCAudioPlugin');
    this.isCaller = false;
    this.iceCandidates = [];
    
    // 监听原生事件
    document.addEventListener('webrtc-event', this.handleNativeEvent.bind(this));
  }
  
  // 初始化WebRTC
  initialize() {
    return new Promise((resolve, reject) => {
      this.plugin.initialize(({success, message}) => {
        if (success) {
          resolve(message);
        } else {
          reject(new Error(message));
        }
      });
    });
  }
  
  // 开始捕获音频
  startCapture() {
    return new Promise((resolve, reject) => {
      this.plugin.startCapture(({success, message}) => {
        if (success) {
          resolve(message);
        } else {
          reject(new Error(message));
        }
      });
    });
  }
  
  // 创建Offer（呼叫方）
  createOffer() {
    this.isCaller = true;
    return new Promise((resolve, reject) => {
      this.plugin.createOffer(({success, message, data}) => {
        if (success) {
          resolve(data);
        } else {
          reject(new Error(message));
        }
      });
    });
  }
  
  // 设置远程描述（应答方）
  setRemoteDescription(sdp, type = 'offer') {
    return new Promise((resolve, reject) => {
      this.plugin.setRemoteDescription(type, sdp, ({success, message}) => {
        if (success) {
          resolve(message);
        } else {
          reject(new Error(message));
        }
      });
    });
  }
  
  // 创建Answer（应答方）
  createAnswer() {
    return new Promise((resolve, reject) => {
      this.plugin.createAnswer(({success, message, data}) => {
        if (success) {
          resolve(data);
        } else {
          reject(new Error(message));
        }
      });
    });
  }
  
  // 添加ICE候选
  addIceCandidate(candidate, sdpMid, sdpMLineIndex) {
    return new Promise((resolve, reject) => {
      this.plugin.addIceCandidate(sdpMid, sdpMLineIndex, candidate, ({success, message}) => {
        if (success) {
          resolve(message);
        } else {
          reject(new Error(message));
        }
      });
    });
  }
  
  // 停止通话
  stop() {
    return new Promise((resolve, reject) => {
      this.plugin.stop(({success, message}) => {
        if (success) {
          resolve(message);
        } else {
          reject(new Error(message));
        }
      });
    });
  }
  
  // 处理原生事件
  handleNativeEvent(event) {
    const detail = event.detail;
    switch(detail.type) {
      case 'onIceCandidate':
        this.handleIceCandidate(detail);
        break;
      // 其他事件处理...
    }
  }
  
  // 处理ICE候选
  handleIceCandidate({candidate, sdpMid, sdpMLineIndex}) {
    if (this.remotePeer) {
      // 实际应用中应该通过信令服务器发送给对等方
      this.remotePeer.addIceCandidate(candidate, sdpMid, sdpMLineIndex);
    } else {
      // 临时存储候选
      this.iceCandidates.push({candidate, sdpMid, sdpMLineIndex});
    }
  }
  
  // 处理远程ICE候选
  async processPendingIceCandidates() {
    for (const {candidate, sdpMid, sdpMLineIndex} of this.iceCandidates) {
      await this.addIceCandidate(candidate, sdpMid, sdpMLineIndex);
    }
    this.iceCandidates = [];
  }
}

export default new WebRTCService();