/**
 * WebRTC 管理器
 * 封装了WebRTC的核心功能，支持P2P语音通话
 */
export class WebRTCManager {
  constructor() {
    this.localStream = null;
        this.remoteStream = null;
        this.peerConnection = null;
        this.dataChannel = null;
        this.isCaller = false;
        this.roomId = null;
        this.pendingSignals = [];
        this.signalRetryTimer = null;
    this.iceServers = [           // ICE服务器配置
		{ 
			urls: 'turn:43.198.105.182:82',
			username: "your_username",              // ← 服务端提供的凭据
			credential: "123456"    ,
		},
      // 可添加TURN服务器
    ];
  }

  /**
   * 初始化本地音频流
   */
  async initLocalStream() {
    try {
      // 获取麦克风权限并创建音频流
      this.localStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          channelCount: 1,
          sampleRate: 16000
        }
      });
      return this.localStream;
    } catch (error) {
      console.error('获取麦克风权限失败:', error);
      throw error;
    }
  }

  /**
   * 创建PeerConnection
   */
  createPeerConnection() {
    this.peerConnection = new RTCPeerConnection({
      iceServers: this.iceServers
    });

    // 添加本地音频轨道
    this.localStream.getTracks().forEach(track => {
      this.peerConnection.addTrack(track, this.localStream);
    });

    // ICE候选收集
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        this.sendSignal({
          type: 'candidate',
          candidate: event.candidate
        });
      }
    };

    // 远程流到达
    this.peerConnection.ontrack = (event) => {
      this.remoteStream = event.streams[0];
      this.onRemoteStreamAdded(this.remoteStream);
    };

    // 如果是呼叫方，创建数据通道
    if (this.isCaller) {
      this.dataChannel = this.peerConnection.createDataChannel('signaling');
	  console.log('dataChannel',this.dataChannel)
      this.setupDataChannel();
    } else {
      this.peerConnection.ondatachannel = (event) => {
        this.dataChannel = event.channel;
        this.setupDataChannel();
      };
    }
  }

  /**
   * 设置数据通道
   */
  setupDataChannel() {
      this.dataChannel.onmessage = (event) => {
        const signal = JSON.parse(event.data);
        this.handleSignal(signal);
      };
  
      this.dataChannel.onopen = () => {
        console.log('数据通道已打开');
        this.flushPendingSignals();
      };
  
      this.dataChannel.onclose = () => {
        console.log('数据通道已关闭');
        if (this.signalRetryTimer) {
          clearInterval(this.signalRetryTimer);
          this.signalRetryTimer = null;
        }
      };
  
      this.dataChannel.onerror = (error) => {
        console.error('数据通道错误:', error);
      };
    }
  /**
   * 处理信令消息
   */
  handleSignal(signal) {
    switch (signal.type) {
      case 'offer':
        this.handleOffer(signal.offer);
        break;
      case 'answer':
        this.handleAnswer(signal.answer);
        break;
      case 'candidate':
        this.handleCandidate(signal.candidate);
        break;
    }
  }

  /**
   * 加入房间
   */
  async joinRoom(roomId, isCaller) {
    this.roomId = roomId;
    this.isCaller = isCaller;
    
    await this.initLocalStream();
    this.createPeerConnection();
    
    if (this.isCaller) {
      await this.createOffer();
    }
  }

  /**
   * 创建Offer
   */
  async createOffer() {
    try {
      const offer = await this.peerConnection.createOffer({
        offerToReceiveAudio: true
      });
      await this.peerConnection.setLocalDescription(offer);
      this.sendSignal({
        type: 'offer',
        offer: offer
      });
    } catch (error) {
      console.error('创建Offer失败:', error);
      throw error;
    }
  }

  /**
   * 处理Offer
   */
  async handleOffer(offer) {
    await this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer));
    const answer = await this.peerConnection.createAnswer();
    await this.peerConnection.setLocalDescription(answer);
    this.sendSignal({
      type: 'answer',
      answer: answer
    });
  }

  /**
   * 处理Answer
   */
  async handleAnswer(answer) {
    await this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer));
  }

  /**
   * 处理ICE候选
   */
  async handleCandidate(candidate) {
    try {
      await this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));
    } catch (error) {
      console.error('添加ICE候选失败:', error);
    }
  }

  /**
   * 发送信令消息
   */
  sendSignal(signal) {
      if (!this.dataChannel) {
        console.warn('数据通道未初始化，将信令加入队列');
        this.pendingSignals.push(signal);
        return;
      }
  
      const sendMessage = () => {
        try {
          if (this.dataChannel.readyState === 'open') {
            this.dataChannel.send(JSON.stringify(signal));
          } else {
            this.pendingSignals.push(signal);
            
            if (!this.signalRetryTimer) {
              this.signalRetryTimer = setInterval(() => {
                if (this.dataChannel.readyState === 'open') {
                  clearInterval(this.signalRetryTimer);
                  this.signalRetryTimer = null;
                  this.flushPendingSignals();
                } else if (this.dataChannel.readyState === 'closed') {
                  clearInterval(this.signalRetryTimer);
                  this.signalRetryTimer = null;
                }
              }, 200);
            }
          }
        } catch (error) {
          console.error('发送信令失败:', error);
        }
      };
  
      sendMessage();
    }

flushPendingSignals() {
    if (!this.pendingSignals || this.pendingSignals.length === 0) return;
    
    console.log(`尝试发送 ${this.pendingSignals.length} 条待发信令`);
    
    const failedSignals = [];
    
    while (this.pendingSignals.length > 0) {
      const signal = this.pendingSignals.shift();
      try {
        if (this.dataChannel && this.dataChannel.readyState === 'open') {
          this.dataChannel.send(JSON.stringify(signal));
        } else {
          failedSignals.push(signal);
        }
      } catch (error) {
        console.error('发送待发信令失败:', error);
        failedSignals.push(signal);
      }
    }
    
    this.pendingSignals = failedSignals;
    
    if (this.pendingSignals.length > 0) {
      console.warn(`${this.pendingSignals.length} 条信令发送失败，将重试`);
    }
  }

  /**
   * 关闭连接
   */

    close() {
      if (this.signalRetryTimer) {
        clearInterval(this.signalRetryTimer);
        this.signalRetryTimer = null;
      }
      
      if (this.peerConnection) {
        this.peerConnection.close();
        this.peerConnection = null;
      }
      
      if (this.localStream) {
        this.localStream.getTracks().forEach(track => track.stop());
        this.localStream = null;
      }
      
      this.dataChannel = null;
      this.pendingSignals = [];
    }
}