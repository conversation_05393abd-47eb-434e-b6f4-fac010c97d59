import { banFriend, removeFriend } from "@/api/friend";

/**
 * 设置好友黑名单状态
 * @param {string|number} id - 好友ID
 * @param {boolean} ban - 是否拉黑，true为拉黑，false为取消拉黑
 * @returns {Promise<Object>} 返回操作结果
 * @example
 */
export const setBanFriend = async (id, ban) => {
  try {
    if (!id) {
      throw new Error('好友ID不能为空')
    }
    
    if (typeof ban != 'boolean') {
      throw new Error('ban参数必须是布尔值')
    }
    const result = await banFriend({
      id,
      ban
    })
    console.log('设置黑名单:', result)
    return result
  } catch (error) {
    console.error('设置黑名单失败:', error)
    throw error
  }
}

/**
 * 删除好友
 * @param {string|number} id - 好友ID
 * @returns {Promise<Object>} 返回操作结果
 * @example
 */
export const delFriend = async (id) => {
  try {
    if (!id) {
      throw new Error('好友ID不能为空')
    }
    const result = await removeFriend({ id: id * 1 })
    console.log('删除好友:', result)
    return result
  } catch (error) {
    console.error('删除好友失败:', error)
    throw error
  }
}