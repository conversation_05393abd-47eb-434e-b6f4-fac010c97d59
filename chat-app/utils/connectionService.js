// 导入相关依赖
import {
	GetUserByID,getConfig
} from '../api/api.js'

import {
	decryptAESBase64
} from './decrypt.js'
import {
	isOpen,
	openDb,
	addTab,
	addTabItem,
	isTable,
	name,
	tabName,
	recallMessageInDB,
	//	deleteVoiceCallMessage
} from '@/utils/db.js'
import store from '../store'

/**
 * 连接服务类 - 负责数据库和WebSocket的初始化
 */
class ConnectionService {
	constructor() {
		this.isInitialized = false
		this.token = ''
	}

	/**
	 * 初始化数据库和WebSocket连接
	 * @param {string} token - 用户token
	 * @returns {Promise<boolean>} 初始化是否成功
	 */
	async initialize(token,resetLogin) {
		if(resetLogin){
			this.isInitialized=false
		}
		if (this.isInitialized) {
			console.log('连接服务已初始化，跳过重复初始化')
			return true
		}

		this.token = uni.getStorageSync("token");
		console.log('开始初始化连接服务...')

		try {
			// 初始化数据库
			const dbInitialized = await this.initDatabase()
			if (!dbInitialized) {
				console.error('数据库初始化失败，但继续初始化WebSocket')
			}

			// 初始化WebSocket连接 - 即使数据库初始化失败也要尝试
			const wsConnected = await this.initWebSocket()
			console.log('WebSocket连接结果:', wsConnected)

			// 设置消息监听器
			this.setupMessageListener()

			// 只要有一个组件成功初始化就认为服务可用
			this.isInitialized = true
			console.log('连接服务初始化完成 - 数据库:', dbInitialized, 'WebSocket:', wsConnected)
			return true
		} catch (error) {
			console.error('连接服务初始化过程中出现错误:', error)
			// 即使出现错误，也尝试设置为已初始化，让应用继续运行
			this.isInitialized = true
			console.log('尽管有错误，仍标记连接服务为已初始化，允许应用继续运行')
			return true
		}
	}

	/**
	 * 初始化数据库
	 * @returns {Promise<boolean>} 数据库初始化是否成功
	 */
	async initDatabase() {
		// #ifdef APP-PLUS
		try {
			console.log('开始初始化数据库...')

			// 打开数据库
			const dbOpened = await openDb()
			console.log('数据库打开结果:', dbOpened)
			if (!dbOpened) {
				throw new Error('数据库打开失败')
			}

			// 创建表
			const tableCreated = await addTab()
			console.log('表创建结果:', tableCreated)
			if (!tableCreated) {
				throw new Error('表创建失败')
			}

			// 验证表是否存在
			const tableExists = await isTable(name, tabName())
			console.log('表是否存在:', tableExists)
			// isTable返回的是数字：0表示不存在，1表示存在
			if (tableExists === 0 || tableExists === false) {
				console.warn('表不存在，但不视为致命错误，继续初始化')
				// 不抛出错误，允许应用继续运行
			}

			console.log('数据库初始化完成')
			return true
		} catch (error) {
			console.error('数据库初始化失败:', error)
			return false
		}
		// #endif

		// #ifndef APP-PLUS
		// 非APP平台直接返回成功
		console.log('非APP平台，跳过数据库初始化')
		return true
		// #endif
	}

	/**
	 * 初始化WebSocket连接
	 * @returns {Promise<boolean>} WebSocket连接是否成功
	 */
	initWebSocket() {
		return new Promise((resolve, reject) => {
			let isResolved = false

			// 设置连接超时 - 增加到20秒
			const timeout = setTimeout(() => {
				if (!isResolved) {
					isResolved = true
					console.warn('WebSocket连接超时，但不视为致命错误')
					// 不直接reject，而是resolve(true)，允许应用继续运行
					resolve(true)
				}
			}, 20000) // 20秒超时

			// 连接WebSocket
			uni.connectSocket({
				url: 'ws://43.198.105.182:82/api/msgSocket?token=' + this.token,
				success: (res) => {
					console.log('WebSocket连接创建成功', res)
					getConfig().then(res=>{
						console.log(res)
						if(res.data.code==0){
							
							
							store.commit('setConfig', res.data.data)
						}
						
					})
					// 连接创建成功后，给onSocketOpen更多时间
					setTimeout(() => {
						if (!isResolved) {
							console.log('WebSocket连接创建成功，等待建立连接...')
						}
					}, 1000)
				},
				fail: (err) => {
					console.error('WebSocket连接创建失败', err)
					if (!isResolved) {
						isResolved = true
						clearTimeout(timeout)
						// 连接创建失败也不视为致命错误
						console.warn('WebSocket连接创建失败，但允许应用继续运行')
						resolve(true)
					}
				}
			})

			// 监听WebSocket连接成功
			uni.onSocketOpen((res) => {
				console.log('WebSocket连接已建立', res)
				if (!isResolved) {
					isResolved = true
					clearTimeout(timeout)
					resolve(true)
				}
			})

			// 监听WebSocket连接错误
			uni.onSocketError((error) => {
				console.error('WebSocket连接错误:', error)
				if (!isResolved) {
					isResolved = true
					clearTimeout(timeout)
					// 连接错误也不视为致命错误
					console.warn('WebSocket连接错误，但允许应用继续运行')
					resolve(true)
				}
			})

			// 监听WebSocket连接关闭
			uni.onSocketClose((res) => {
				console.log('WebSocket连接已关闭:', res)
				this.token = uni.getStorageSync("token");
				if (this.token) {
					this.isInitialized = false
					// 可以在这里添加重连逻辑
					this.scheduleReconnect()
				}

			})
		})
	}

	/**
	 * 设置WebSocket消息监听器
	 */
	setupMessageListener() {
		uni.onSocketMessage((res) => {
			console.log('收到服务器内容：', res)
			try {
				
				const message = JSON.parse(res.data)
				
				// message.msg=decryptAESBase64(message.msg)
				console.log('解析后的消息:', message)
				// 解密消息内容
				// let decryptedMsg = decryptAESBase64(message.msg)
				const currentUserId = uni.getStorageSync('userId')
				let decryptedMsg = ''
				// if(message.typecode2==7){
				// 	message.typecode2=0
				// }
				
				if(message.typecode2==4&&message.typecode==3){
					return
				}
				// if(message.typecode==5){
				// 	console.log('挂断',message)
				// 	uni.$emit('phone-call-message', message)
				// 	return
				// }
				if (message.typecode2 == 11) {
					console.log('发送房间开始通话', message)
					uni.$emit('chat-voice-received', message)
					return
				}
				if (message.typecode2 == 12) {
					console.log('通话截至', message)
					uni.$emit('chat-voice-received', message)
		
				}
				// if(message.typecode2==12){
				// 	 console.log('语音通话终止',message)
				// 	 uni.$emit('phone-call-message', message)
				// 	 // 继续处理消息，不return，让它保存到聊天记录中
				// }
				// if(message.typecode==6){
				// 	// 取消通话
				// }
				// if(message.typecode2>=9){
				// 	 // uni.$emit('chat-message-received', message)
				// 	 // return
				// 	if(message.typecode2==9&&message.fromid!=currentUserId){
				// 		//被呼叫方 弹出语音消息提示框
				// 		uni.$emit('show-call-message', message)
				// 	}
				// 	if(message.typecode2==9&&message.fromid==currentUserId){
				// 		// 对面接听了
				// 		return
				// 	}
				// 	if(message.typecode2==11){
				// 		// 对面接听或者取消了
				// 		 uni.$emit('start-phone-call', message)
				// 		 return
				// 	}
				// 	// uni.$emit('chat-message-received', message)


				// 	decryptedMsg='语音通话'
				// 	message.msg='语音通话'
				// }else{
				// 	// ecryptedMsg = decryptAESBase64(message.msg)
				// 	decryptedMsg = decryptAESBase64(message.msg)
				// }
				// 构建要保存的消息参数
				decryptedMsg = decryptAESBase64(message.msg)

				// 根据消息类型确定chatid
				let chatid
				if (message.typecode == 2 && message.groupID) {
					// 群聊消息：使用groupID作为chatid
					chatid = message.groupID.toString()
				} else {
					// 好友消息：使用对方的id作为chatid
					chatid = message.toid == currentUserId ? message.fromid : message.toid
				}

				// 根据消息类型生成聊天列表显示文本
				let lastMessageText = decryptedMsg;
				switch (message.typecode2) {
					case 1:
						lastMessageText = '[语音]';
						break;
					case 2:
						lastMessageText = '[图片]';
						break;
					case 3:
						lastMessageText = '[视频]';
						break;
					case 4:
						lastMessageText = '[转发消息]';
						break;
					case 5:
						lastMessageText = '[撤回消息]';
						break;
					case 7:
						// 通过好友请求
						console.log('通过好友',decryptedMsg)
						decryptedMsg=JSON.parse(decryptedMsg)
						lastMessageText = decryptedMsg.msg;
						decryptedMsg=decryptedMsg.msg;
						break;
					case 9:
						// 通话请求
						decryptedMsg = message.toid == currentUserId ? '发起语音通话' : '请求语音通话'
						lastMessageText = '[语音通话]';
						break;
					case 10:
						// 通话应答
						lastMessageText = '[语音通话]';
						break;
					case 12:
						// 通话终止
						console.log('decryptedMsg------', decryptedMsg)
						let msgObj = JSON.parse(decryptedMsg)
						// 4 发起方中断，5 接收放中断，6 其他异常中断


						if (msgObj.timer != "0:00") {
							decryptedMsg = '通话时长 ' + msgObj.timer
						} else {
							
							if (msgObj.apply == 4) {
								decryptedMsg = message.toid == currentUserId ? '对方已取消' : '通话已取消'
								// decryptedMsg = '通话已取消'
							}
							if (msgObj.apply == 5) {
								decryptedMsg = message.toid == currentUserId ? '对方已拒绝' : '已拒绝'
								// decryptedMsg = '已拒绝'
							}

						}


						lastMessageText = '[语音通话]';
						break;
					default:
						lastMessageText = decryptedMsg;
				}

				// 语音通话消息合并逻辑：如果是通话终止消息，删除数据库中的通话发起消息
				if (message.typecode2 == 12) {
					// 删除同一聊天中的语音通话发起消息（typecode2=9）
					//deleteVoiceCallMessage(chatid, message.fromid, message.toid)
					//	.then(() => {
					//		console.log('成功删除语音通话发起消息');
					//	})
					//	.catch((err) => {
					//		console.error('删除语音通话发起消息失败:', err);
					//	});
				}

				const params = {
					id: message.id,
					typecode: message.typecode,
					typecode2: message.typecode2 || 0,
					toid: message.toid,
					fromid: message.fromid,
					chatid: chatid,
					t: message.t, // 使用服务器提供的时间戳，不使用客户端时间
					msg: decryptedMsg,
					isRedRead: 0,
					idDel: 0,
					avatar: '', // 将在获取用户信息后更新
					nickname: '', // 将在获取用户信息后更新
					lastMessage: lastMessageText,
					timestamp: message.t ? new Date(message.t).getTime() : new Date()
				.getTime(), // 使用服务器时间戳
					unreadCount: message.fromid !== currentUserId ? 1 : 0,
				}
				console.log('保存的消息参数:', params)

				// 分类处理不同类型的消息
				if (message.typecode == 1 || message.typecode == 2) {
					this.handleChatMessage(message, params, currentUserId, chatid, decryptedMsg)
				} else if (message.typecode == 3) {
					this.handleNotifyMessage(message, decryptedMsg)
				}
			} catch (error) {
				console.error('处理消息失败:', error)
			}
		})
	}
	toVoiceCall(message){
		console.log('语音通话请求------',message,uni.getStorageSync("userId"))
		// 自己发起的
		if(message.typecode2==9&&message.toid==uni.getStorageSync("userId")){
			console.log('语音通话请求')
			uni.navigateTo({
			  url: '/pages/call/voice-call?newMessage=' + JSON.stringify(message)+'&toId='+message.fromid+"&isCaller=false&id="+message.id,
			  success: () => {
				console.log('成功跳转到语音通话页面');
			  },
			  fail: (err) => {
				  
				  
				console.error('打开语音通话页面失败', err);
				// 如果失败，尝试再次检查pages.json配置
				uni.showToast({
				  title: '打开语音通话失败',
				  icon: 'none',
				  duration: 3000
				});
			  }
			});
			
		}
	
	}
	/**
	 * 处理聊天消息
	 * @param {Object} message - 原始消息对象
	 * @param {Object} params - 处理后的消息参数
	 * @param {string} currentUserId - 当前用户ID
	 * @param {string} chatid - 聊天对象ID
	 * @param {string} decryptedMsg - 解密后的消息内容
	 */
	handleChatMessage(message, params, currentUserId, chatid, decryptedMsg) {
		console.log('处理聊天消息，typecode:', message.typecode, 'typecode2:', message.typecode2)

		// 特殊处理撤回消息
		if (message.typecode2 == 5) {
			this.handleRecallMessage(message, params, currentUserId, chatid, decryptedMsg)
			return
		}

		// 根据消息类型获取相应信息
		if (message.typecode === 2 && message.groupID) {
			// 群聊消息：从Vuex中获取群组信息用于聊天列表显示
			const groupList = store.state.groupList || []
			console.log('groupListgroupListgroupListgroupList:', groupList)
			const group = groupList.find(g => g.ID == message.groupID)
			if (group) {
				params.avatar = group.GroupHeader
				params.nickname = group.GroupName
			} else {
				// 如果找不到群组信息，使用默认值
				params.avatar = '/static/conversation/group_avatar.png'
				params.nickname = '群聊111'
			}
			console.log('即将要保存的群聊消息参数:+++++++++++', params)
			// 获取发送者信息用于消息显示
			GetUserByID(message.fromid)
				.then((res) => {
					console.log('获取群聊发送者信息结果:', res.data)
					if (res.data.code == 0) {
						// 为消息添加发送者头像信息
						const messageWithSenderInfo = {
							...params,
							senderAvatar: res.data.data.head_img || '/static/My/avatar.jpg',
							senderNickname: res.data.data.name || '未知用户'
						}
						console.log('群聊消息（包含发送者信息）:', messageWithSenderInfo)
						// 更新聊天列表（使用群组信息）
						store.commit('setChatList', params)
						// 保存包含发送者信息的完整消息到数据库
						addTabItem(messageWithSenderInfo)
							.then((result) => {
								console.log('群聊消息接收成功:', messageWithSenderInfo)
								console.log('群聊消息保存到数据库成功:', result)
								// 发布消息更新事件，通知聊天页面更新（包含发送者信息）
								
								uni.$emit('chat-message-received', messageWithSenderInfo)
								this.toVoiceCall(messageWithSenderInfo)
							})
							.catch((err) => console.error('群聊消息保存到数据库失败:', err))

						// 存储群组消息到Vuex
						console.log('存储群组消息到Vuex')
						store.commit('setGroupMsg', messageWithSenderInfo)
					} else {
						console.error('获取群聊发送者信息失败:', res.data)
						// 即使获取发送者信息失败，也要处理消息
						store.commit('setChatList', params)
						addTabItem(params)
							.then((result) => {
								console.log('群聊消息接收成功（无发送者信息）:', params)
								
								uni.$emit('chat-message-received', params)
								this.toVoiceCall(params)
							})
							.catch((err) => console.error('群聊消息保存到数据库失败:', err))
						store.commit('setGroupMsg', params)
					}
				})
				.catch((err) => {
					console.error('获取群聊发送者信息失败:', err)
					// 即使获取发送者信息失败，也要处理消息
					store.commit('setChatList', params)
					addTabItem(params)
						.then((result) => {
							console.log('群聊消息接收成功（获取发送者信息异常）:', params)
							
							uni.$emit('chat-message-received', params)
							this.toVoiceCall(params)
						})
						.catch((err) => console.error('群聊消息保存到数据库失败:', err))
					store.commit('setGroupMsg', params)
				})
		} else {
			// 好友消息：优先从store中获取好友信息
			const getUserid = message.fromid == currentUserId ? message.toid : message.fromid
			console.log('获取用户信息，getUserid:', getUserid)

			// 先从store中的friendList查找好友信息
			const friendList = store.state.friendList || []
			const friend = friendList.find(f => f.Friend == getUserid)

			if (friend) {
				// 从store中找到好友信息
				console.log('从store中获取到好友信息:', friend)
				params.avatar = friend.User?.head_img || '/static/My/avatar.jpg'
				params.nickname = friend.Name || friend.User?.iphone_num || '未知用户'

				// 为消息添加发送者信息（用于历史消息显示）
				const messageWithSenderInfo = {
					...params,
					senderAvatar: friend.User?.head_img || '/static/My/avatar.jpg',
					senderNickname: friend.Name || friend.User?.iphone_num || '未知用户'
				}

				// 更新聊天列表
				store.commit('setChatList', params)
				console.log('++++',messageWithSenderInfo)
				uni.$emit('chat-message-received', messageWithSenderInfo)
				this.toVoiceCall(messageWithSenderInfo)
				// 保存包含发送者信息的完整消息到数据库
				addTabItem(messageWithSenderInfo)
					.then((result) => {
						console.log('好友消息接收成功（从store获取）:', messageWithSenderInfo)
						console.log('好友消息保存到数据库成功:', result)
						// 发布消息更新事件，通知聊天页面更新
						// uni.$emit('chat-message-received', messageWithSenderInfo)
					})
					.catch((err) => console.error('好友消息保存到数据库失败:', err))

				// 存储好友消息到Vuex
				console.log('存储好友消息到Vuex')
				store.commit('setFriendMsg', messageWithSenderInfo)
			} else {
				// store中没有找到，调用API获取用户信息
				console.log('store中未找到好友信息，调用API获取')
				GetUserByID(getUserid)
					.then((res) => {
						console.log('API获取用户信息结果:', res.data)
						if (res.data.code == 0) {
							params.avatar = res.data.data.head_img || '/static/My/avatar.jpg'
							params.nickname = res.data.data.name || res.data.data.iphone_num || '未知用户'

							// 为消息添加发送者信息（用于历史消息显示）
							const messageWithSenderInfo = {
								...params,
								senderAvatar: res.data.data.head_img || '/static/My/avatar.jpg',
								senderNickname: res.data.data.name || res.data.data.iphone_num || '未知用户'
							}

							// 更新聊天列表
							store.commit('setChatList', params)
							//#ifdef H5
							// uni.$emit('chat-message-received', messageWithSenderInfo)
							//#endif
							// 保存包含发送者信息的完整消息到数据库
							//#ifdef APP-PLUS
							console.log(messageWithSenderInfo)
							addTabItem(messageWithSenderInfo)
								.then((result) => {
									console.log('好友消息接收成功（API获取）:', messageWithSenderInfo)
									console.log('好友消息保存到数据库成功:', result)
									// 发布消息更新事件，通知聊天页面更新
									
									uni.$emit('chat-message-received', messageWithSenderInfo)
									this.toVoiceCall(messageWithSenderInfo)

								})
								.catch((err) => console.error('好友消息保存到数据库失败:', err))

							// 存储好友消息到Vuex
							console.log('存储好友消息到Vuex')
							store.commit('setFriendMsg', messageWithSenderInfo)
						} else {
							console.error('API获取用户信息失败:', res.data)
							// 即使获取用户信息失败，也要处理消息
							params.avatar = '/static/My/avatar.jpg'
							params.nickname = '未知用户'

							// 为消息添加发送者信息（用于历史消息显示）
							const messageWithSenderInfo = {
								...params,
								senderAvatar: '/static/My/avatar.jpg',
								senderNickname: '未知用户'
							}

							store.commit('setChatList', params)
							// 保存包含发送者信息的完整消息到数据库
							addTabItem(messageWithSenderInfo)
								.then((result) => {
									console.log('好友消息接收成功（无用户信息）:', messageWithSenderInfo)
									
									uni.$emit('chat-message-received', messageWithSenderInfo)
									this.toVoiceCall(messageWithSenderInfo)
								})
								.catch((err) => console.error('好友消息保存到数据库失败:', err))

							store.commit('setFriendMsg', messageWithSenderInfo)
							//#endif
						}
					})
					.catch((err) => {
						console.error('API获取用户信息失败:', err)
						// 即使获取用户信息失败，也要处理消息
						params.avatar = '/static/My/avatar.jpg'
						params.nickname = '未知用户'

						// 为消息添加发送者信息（用于历史消息显示）
						const messageWithSenderInfo = {
							...params,
							senderAvatar: '/static/My/avatar.jpg',
							senderNickname: '未知用户'
						}

						store.commit('setChatList', params)
						// 保存包含发送者信息的完整消息到数据库
						addTabItem(messageWithSenderInfo)
							.then((result) => {
								console.log('好友消息接收成功（获取用户信息异常）:', messageWithSenderInfo)
								uni.$emit('chat-message-received', messageWithSenderInfo)
								this.toVoiceCall(messageWithSenderInfo)
							})
							.catch((err) => console.error('好友消息保存到数据库失败:', err))
						store.commit('setFriendMsg', messageWithSenderInfo)
					})
			}
		}
	}

	/**
	 * 处理撤回消息
	 * @param {Object} message - 原始消息对象
	 * @param {Object} params - 处理后的消息参数
	 * @param {string} currentUserId - 当前用户ID
	 * @param {string} chatid - 聊天对象ID
	 * @param {string} decryptedMsg - 解密后的消息内容
	 */
	handleRecallMessage(message, params, currentUserId, chatid, decryptedMsg) {
		console.log('收到撤回消息:', message)
		try {
			// 解析撤回消息内容
			let recallData = JSON.parse(decryptedMsg)
			console.log('撤回消息数据:', recallData)
			// 获取被撤回的消息ID
			const recalledMessageId = recallData.ret
			console.log('被撤回的消息ID:', recalledMessageId)

			// 确定撤回消息的显示文本
			const isSelfRecall = message.fromid == currentUserId

			if (isSelfRecall) {
				// 自己撤回消息
				const recallText = '你撤回了一条消息'
				this.updateRecallMessage(recalledMessageId, recallText, params, chatid)
			} else {
				// 对方撤回消息，需要获取撤回者的用户信息
				GetUserByID(message.fromid)
					.then((res) => {
						console.log('获取撤回用户信息结果:', res.data)
						let recallText = '对方撤回了一条消息' // 默认文本

						if (res.data.code == 0 && res.data.data) {
							// 获取用户信息成功，使用用户名
							const userName = res.data.data.name || res.data.data.iphone_num || '未知用户'
							recallText = `"${userName}"撤回了一条消息`
						} else {
							console.error('获取撤回用户信息失败:', res.data)
						}

						this.updateRecallMessage(recalledMessageId, recallText, params, chatid)
					})
					.catch((err) => {
						console.error('获取撤回用户信息失败:', err)
						// 获取用户信息失败，使用默认文本
						const recallText = '对方撤回了一条消息'
						this.updateRecallMessage(recalledMessageId, recallText, params, chatid)
					})
			}
		} catch (error) {
			console.error('处理撤回消息失败:', error)
		}
	}

	/**
	 * 更新撤回消息
	 * @param {number} recalledMessageId - 被撤回的消息ID
	 * @param {string} recallText - 撤回消息文本
	 * @param {Object} params - 消息参数
	 * @param {string} chatid - 聊天对象ID
	 */
	updateRecallMessage(recalledMessageId, recallText, params, chatid) {
		// 更新数据库中被撤回的消息
		recallMessageInDB(recalledMessageId, recallText)
			.then(() => {
				console.log('撤回消息数据库更新成功')

				// 发布撤回消息事件，通知聊天页面更新
				uni.$emit('message-recalled', {
					messageId: recalledMessageId,
					recallText: recallText,
					chatid: chatid,
				})

				// 更新聊天列表中的最后一条消息
				if (params.chatid in store.state.chatList) {
					const updatedParams = {
						...store.state.chatList[params.chatid],
						lastMessage: recallText,
						msg: recallText,
						timestamp: new Date().getTime(),
					}
					store.commit('setChatList', updatedParams)
				}
			})
			.catch((err) => {
				console.error('撤回消息数据库更新失败:', err)
			})
	}

	/**
	 * 处理通知消息
	 * @param {Object} message - 原始消息对象
	 * @param {string} decryptedMsg - 解密后的消息内容
	 */
	handleNotifyMessage(message, decryptedMsg) {
		console.log('处理通知消息')
		// 获取发送方用户信息
		GetUserByID(message.fromid)
			.then((res) => {
				console.log('获取发送方信息:', res)
				if (res.data.code == 0) {
					let msgContent = JSON.parse(decryptedMsg)
					console.log('发送方信息获取成功:', res.data.data)
					const notifyParams = {
						...message,
						msg: msgContent.msg,
						head_img: res.data.data.head_img,
						name: res.data.data.name,
						isRedRead: 0,
						idDel: 0,
					}
					console.log('vuex存储的通知消息', notifyParams)
					store.commit('setNotifyMsg', notifyParams)
				}
			})
			.catch((err) => {
				console.error('获取发送方信息失败:', err)
			})
	}

	/**
	 * 计划重连
	 */
	scheduleReconnect() {
		if (this.token && !this.isInitialized) {
			console.log('计划在5秒后重连WebSocket...')
			setTimeout(() => {
				if (!this.isInitialized && this.token) {
					console.log('尝试重连WebSocket...')
					this.initWebSocket().then(() => {
						console.log('WebSocket重连成功')
						this.isInitialized = true
						// 重新设置消息监听器
						this.setupMessageListener()
					}).catch((error) => {
						console.error('WebSocket重连失败:', error)
					})
				}
			}, 5000)
		}
	}

	/**
	 * 重置连接状态
	 */
	reset() {
		this.isInitialized = false
		this.token = null
		console.log('连接服务已重置')
	}

	/**
	 * 断开连接
	 */
	disconnect() {
		if (this.isInitialized) {
			uni.closeSocket()
			this.isInitialized = false
			console.log('连接服务已断开')
		}
	}
}

// 创建单例实例
const connectionService = new ConnectionService()

// 导出服务实例和初始化函数
export {
	connectionService
}

/**
 * 初始化连接服务的便捷函数
 * @param {string} token - 用户token
 * @returns {Promise<boolean>} 初始化是否成功
 */
export const initConnectionService = async (token,resetLogin) => {
	return await connectionService.initialize(token,resetLogin)
}

/**
 * 重置连接服务
 */
export const resetConnectionService = () => {
	connectionService.reset()
}

/**
 * 断开连接服务
 */
export const disconnectConnectionService = () => {
	connectionService.disconnect()
}