{
  "pages": [
    {
      "path": "pages/chats/chats",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none",
          "disableScroll": true
        }
      }
    },
    {
      "path": "pages/search/search",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none",
          "disableScroll": false,
          "titleNView": false
        }
      }
    },
    {
      "path": "pages/login/login",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none",
          "disableScroll": true
        }
      }
    },
	{
	  "path": "pages/call/voice-call",
	  "style": {
	    "navigationStyle": "custom",
	    "app-plus": {
	      // "animationType": "slide-in-bottom",
	      // "popGesture": "close"
	    }
	  }
	},
    {
      "path": "pages/me/me",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false,
        "app-plus": {
          "bounce": "none",
          "disableScroll": true
        }
      }
    },
    {
      "path": "pages/channels/channels",
      "style": {
        "navigationBarTitleText": "频道",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false,
        "app-plus": {
          "bounce": "none",
          "disableScroll": true,
          "titleNView": false
        }
      }
    },
    
    {
      "path": "pages/contacts/contacts",
      "style": {
        "navigationStyle": "custom",
        "enablePullDownRefresh": false,
        "app-plus": {
          "bounce": "none",
          "disableScroll": true
        }
      }
    },
    {
      "path": "pages/profile/profile",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none",
          "disableScroll": false
        }
      }
    },
    {
      "path": "pages/chat-history/chat-history",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none",
          "disableScroll": false
        }
      }
    },
    {
      "path": "pages/conversation/conversation",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none",
          "disableScroll": false
        }
      }
    },
    {
      "path": "pages/addFriends/index",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none",
          "disableScroll": false
        }
      }
    },
    {
      "path": "pages/addFriends/applyFor",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none",
          "disableScroll": false
        }
      }
    },
    {
      "path": "pages/addGroup/index",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none",
          "disableScroll": false
        }
      }
    },
    {
      "path": "pages/addGroup/applyForgroup",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none",
          "disableScroll": false
        }
      }
    },
    {
      "path": "pages/searchContact/index",
      "style": {
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none",
          "disableScroll": false
        }
      }
    },
	{
	  "path": "pages/friend/info",
	  "style": {
	    "navigationStyle": "custom",
	    "app-plus": {
	      "bounce": "none",
	      "disableScroll": false
	    }
	  }
	},
	{
	  "path": "pages/friend/setting",
	  "style": {
	    "navigationStyle": "custom",
	    "app-plus": {
	      "bounce": "none",
	      "disableScroll": false
	    }
	  }
	},
	{
	  "path": "pages/profile/setName",
	  "style": {
	    "navigationStyle": "custom",
	    "app-plus": {
	      "bounce": "none",
	      "disableScroll": false
	    }
	  }
	},
	{
	  "path": "pages/group/list",
	  "style": {
	    "navigationStyle": "custom",
	    "app-plus": {
	      "bounce": "none",
	      "disableScroll": false
	    }
	  }
	},
	{
	  "path": "pages/friend/list",
	  "style": {
	    "navigationStyle": "custom",
	    "app-plus": {
	      "bounce": "none",
	      "disableScroll": false
	    }
	  }
	},
	{
	  "path": "pages/forward/index",
	  "style": {
	    "navigationStyle": "custom",
	    "app-plus": {
	      "bounce": "none",
	      "disableScroll": false
	    }
	  }
	},
    {
	  "path": "pages/me/setting",
	  "style": {
	    "navigationStyle": "custom",
	    "app-plus": {
	      "bounce": "none",
	      "disableScroll": false
	    }
	  }
	},
	{
	  "path": "pages/debug/db-viewer",
	  "style": {
	    "navigationStyle": "custom",
	    "app-plus": {
	      "bounce": "none",
	      "disableScroll": false
	    }
	  }
	},
	{
	  "path": "pages/debug/db-test",
	  "style": {
		"navigationStyle": "custom",
		"app-plus": {
		  "bounce": "none",
		  "disableScroll": false
		}
	  }
	},
	{
	  "path": "pages/setting/index",
	  "style": {
	    "navigationStyle": "custom",
	    "app-plus": {
	      "bounce": "none",
	      "disableScroll": false
	    }
	  }
	},
	{
	  "path": "pages/test/index",
	  "style": {
	    "navigationStyle": "custom",
	    "app-plus": {
	      "bounce": "none",
	      "disableScroll": false
	    }
	  }
	},
	{
	  "path": "pages/test/voice-call",
	  "style": {
	    "navigationStyle": "custom",
	    "app-plus": {
	      "bounce": "none",
	      "disableScroll": false
	    }
	  }
	}
  ],
  "easycom": {
    "autoscan": true,
    "custom": {
      "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
    }
  },
  "tabBar": {
    "custom": true,
    "list": [
      {
        "pagePath": "pages/chats/chats",
        "text": "聊天"
      },
      {
        "pagePath": "pages/contacts/contacts",
        "text": "联系人"
      },
      {
        "pagePath": "pages/channels/channels",
        "text": "频道"
      },
      {
        "pagePath": "pages/me/me",
        "text": "我的"
      }
    ]
  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "久聊",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "uniIdRouter": {},
  "condition": {
    "current": 0,
    "list": [
      {
        "name": "",
        "path": "",
        "query": ""
      }
    ]
  }
}