export default {
  // TabBar
  tabBar: {
    chats: '聊天',
    contacts: '联系人',
    channels: '频道',
    me: '我的'
  },

  // 聊天页面
  chat: {
    title: '我的聊天',
    noMessages: '暂无聊天记录',
    searchPlaceholder: '搜索聊天记录'
  },

  // 聊天记录页面
  chatHistory: {
    title: '聊天记录',
    groupChatHistory: '{name}的聊天记录',
    privateChatHistory: '与{name}的聊天记录',
    noMoreMessages: '没有更多聊天记录了',
    loadMore: '上拉加载更多',
    emptyState: '暂无聊天记录'
  },

  // 联系人页面
  contacts: {
    title: '联系人',
    newFriends: '新的朋友',
    groupChats: '群聊',
    totalContacts: '共{count}位联系人',
    addFriend: '添加朋友',
    createGroup: '创建群聊'
  },

  // 个人中心页面
  me: {
    title: '我的',
    personalInfo: '个人信息',
    editProfile: '编辑个人资料',
    avatar: '头像',
    nickname: '昵称',
    phoneNumber: '手机号',
    settings: '设置',
    about: '关于我们',
    help: '帮助与反馈'
  },

  // 聊天信息页面
  chatInfo: {
    title: '聊天信息',
    viewChatHistory: '查看聊天记录',
    clearChatHistory: '清空聊天记录',
    addToBlacklist: '加入黑名单',
    deleteFriend: '删除好友',
    groupChatHistory: '群聊{name}的聊天记录',
    confirmClearHistory: '确定清空聊天记录吗？',
    confirmAddBlacklist: '确定将该好友加入黑名单吗？',
    confirmDeleteFriend: '确定删除该好友吗？',
    historyCleared: '聊天记录已清空',
    addedToBlacklist: '已加入黑名单',
    friendDeleted: '好友已删除'
  },

  // 好友信息页面
  friendInfo: {
    setRemark: '设置备注',
    sendMessage: '发消息',
    voiceCall: '语音通话',
    videoCall: '视频通话',
    remarkPlaceholder: '请输入备注名',
    remarkSet: '备注设置成功',
    phoneNumber: '手机号',
    recommendToFriend: '把他推荐给好友',
    addToBlacklist: '加入黑名单',
    removeFromBlacklist: '移出黑名单',
    confirmAddBlacklist: '确定将该好友加入黑名单吗？',
    confirmRemoveBlacklist: '确定将该好友移出黑名单吗？',
    confirmDeleteFriend: '确定删除该好友吗？',
    addedToBlacklist: '已加入黑名单',
    removedFromBlacklist: '已移出黑名单',
    friendDeleted: '好友已删除'
  },

  // 搜索页面
  search: {
    placeholder: '搜索',
    cancel: '取消',
    contacts: '联系人',
    groups: '群聊',
    chatHistory: '聊天记录',
    noResults: '无搜索结果',
    searchContacts: '搜索联系人',
    searchGroups: '搜索群聊',
    searchMessages: '搜索聊天记录'
  },

  // 好友列表页面
  friendList: {
    title: '新的朋友',
    addFriend: '添加好友',
    noRequests: '暂无好友申请',
    accept: '接受',
    accepted: '已接受',
    decline: '拒绝',
    declined: '已拒绝',
    today: '今天',
    yesterday: '昨天',
    thisWeek: '本周',
    earlier: '更早',
    addPhoneContact: '添加手机联系人',
    searchPhone: '搜索手机号',
    confirmAccept: '确定接受该好友申请吗？',
    confirmDecline: '确定拒绝该好友申请吗？',
    acceptSuccess: '已接受好友申请',
    declineSuccess: '已拒绝好友申请',
    operationFailed: '操作失败，请重试'
  },

  // 好友设置页面
  friendSetting: {
    title: '聊天信息',
    setRemark: '设置备注',
    recommendToFriend: '把他推荐给好友',
    addToBlacklist: '加入黑名单',
    removeFromBlacklist: '移出黑名单',
    deleteFriend: '删除好友',
    confirmAddBlacklist: '确定将该好友加入黑名单吗？',
    confirmRemoveBlacklist: '确定将该好友移出黑名单吗？',
    confirmDeleteFriend: '确定删除该好友吗？',
    addedToBlacklist: '已加入黑名单',
    removedFromBlacklist: '已移出黑名单',
    friendDeleted: '好友已删除',
    operationSuccess: '操作成功',
    operationFailed: '操作失败'
  },
  // 设置页面
  setting: {
    title: '设置',
    language: '应用语言',
    notification: '通知',
    privacy: '隐私',
    about: '关于',
    help: '帮助',
    logout: '退出登录',
    logoutConfirm: '确认退出登录？',
    logoutBtn: '退出',
    cancel: '取消',
    confirm: '确认'
  },
  
  // 语言选项
  languages: {
    zh: '中文',
    en: 'English'
  },
  
  // 通用
  common: {
    back: '返回',
    save: '保存',
    edit: '编辑',
    delete: '删除',
    cancel: '取消',
    confirm: '确认',
    success: '成功',
    failed: '失败',
    loading: '加载中...',
    noData: '暂无数据'
  }
}
