export default {
  // TabBar
  tabBar: {
    chats: 'Chats',
    contacts: 'Contacts',
    channels: 'Channels',
    me: 'Me'
  },

  // Chat page
  chat: {
    title: 'My Chats',
    noMessages: 'No chat records',
    searchPlaceholder: 'Search chat records'
  },

  // Chat history page
  chatHistory: {
    title: 'Chat History',
    groupChatHistory: 'Chat history of {name}',
    privateChatHistory: 'Chat history with {name}',
    noMoreMessages: 'No more chat records',
    loadMore: 'Pull up to load more',
    emptyState: 'No chat records'
  },

  // Contacts page
  contacts: {
    title: 'Contacts',
    newFriends: 'New Friends',
    groupChats: 'Group Chats',
    totalContacts: '{count} contacts in total',
    addFriend: 'Add Friend',
    createGroup: 'Create Group'
  },

  // Me page
  me: {
    title: 'Me',
    personalInfo: 'Personal Info',
    editProfile: 'Edit Profile',
    avatar: 'Avatar',
    nickname: 'Nickname',
    phoneNumber: 'Phone Number',
    settings: 'Settings',
    about: 'About Us',
    help: 'Help & Feedback'
  },

  // Chat info page
  chatInfo: {
    title: 'Chat Info',
    viewChatHistory: 'View Chat History',
    clearChatHistory: 'Clear Chat History',
    addToBlacklist: 'Add to Blacklist',
    deleteFriend: 'Delete Friend',
    groupChatHistory: 'Chat history of group {name}',
    confirmClearHistory: 'Are you sure to clear chat history?',
    confirmAddBlacklist: 'Are you sure to add this friend to blacklist?',
    confirmDeleteFriend: 'Are you sure to delete this friend?',
    historyCleared: 'Chat history cleared',
    addedToBlacklist: 'Added to blacklist',
    friendDeleted: 'Friend deleted'
  },

  // Friend info page
  friendInfo: {
    setRemark: 'Set Remark',
    sendMessage: 'Send Message',
    voiceCall: 'Voice Call',
    videoCall: 'Video Call',
    remarkPlaceholder: 'Please enter remark name',
    remarkSet: 'Remark set successfully',
    phoneNumber: 'Phone Number',
    recommendToFriend: 'Recommend to Friend',
    addToBlacklist: 'Add to Blacklist',
    removeFromBlacklist: 'Remove from Blacklist',
    confirmAddBlacklist: 'Are you sure to add this friend to blacklist?',
    confirmRemoveBlacklist: 'Are you sure to remove this friend from blacklist?',
    confirmDeleteFriend: 'Are you sure to delete this friend?',
    addedToBlacklist: 'Added to blacklist',
    removedFromBlacklist: 'Removed from blacklist',
    friendDeleted: 'Friend deleted'
  },

  // Search page
  search: {
    placeholder: 'Search',
    cancel: 'Cancel',
    contacts: 'Contacts',
    groups: 'Groups',
    chatHistory: 'Chat History',
    noResults: 'No search results',
    searchContacts: 'Search Contacts',
    searchGroups: 'Search Groups',
    searchMessages: 'Search Messages'
  },

  // Friend list page
  friendList: {
    title: 'New Friends',
    addFriend: 'Add Friend',
    noRequests: 'No friend requests',
    accept: 'Accept',
    accepted: 'Accepted',
    decline: 'Decline',
    declined: 'Declined',
    today: 'Today',
    yesterday: 'Yesterday',
    thisWeek: 'This Week',
    earlier: 'Earlier',
    addPhoneContact: 'Add Phone Contact',
    searchPhone: 'Search Phone Number',
    confirmAccept: 'Are you sure to accept this friend request?',
    confirmDecline: 'Are you sure to decline this friend request?',
    acceptSuccess: 'Friend request accepted',
    declineSuccess: 'Friend request declined',
    operationFailed: 'Operation failed, please try again'
  },

  // Friend setting page
  friendSetting: {
    title: 'Chat Info',
    setRemark: 'Set Remark',
    recommendToFriend: 'Recommend to Friend',
    addToBlacklist: 'Add to Blacklist',
    removeFromBlacklist: 'Remove from Blacklist',
    deleteFriend: 'Delete Friend',
    confirmAddBlacklist: 'Are you sure to add this friend to blacklist?',
    confirmRemoveBlacklist: 'Are you sure to remove this friend from blacklist?',
    confirmDeleteFriend: 'Are you sure to delete this friend?',
    addedToBlacklist: 'Added to blacklist',
    removedFromBlacklist: 'Removed from blacklist',
    friendDeleted: 'Friend deleted',
    operationSuccess: 'Operation successful',
    operationFailed: 'Operation failed'
  },
  // Settings page
  setting: {
    title: 'Settings',
    language: 'App Language',
    notification: 'Notifications',
    privacy: 'Privacy',
    about: 'About',
    help: 'Help',
    logout: 'Logout',
    logoutConfirm: 'Are you sure you want to logout?',
    logoutBtn: 'Logout',
    cancel: 'Cancel',
    confirm: 'Confirm'
  },
  
  // Language options
  languages: {
    zh: '中文',
    en: 'English'
  },
  
  // Common
  common: {
    back: 'Back',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    cancel: 'Cancel',
    confirm: 'Confirm',
    success: 'Success',
    failed: 'Failed',
    loading: 'Loading...',
    noData: 'No Data'
  }
}
