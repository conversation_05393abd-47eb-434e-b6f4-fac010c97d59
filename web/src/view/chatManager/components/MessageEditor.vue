<template>
  <div class="message-editor">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button type="text" size="small" @click="handleEmojiClick" class="toolbar-btn">
          <el-icon :size="20">
            <PictureRounded />
          </el-icon>
        </el-button>

        <el-upload :show-file-list="false" :before-upload="handleImageUpload" accept="image/*" class="upload-btn">
          <el-button type="text" size="small" class="toolbar-btn">
            <el-icon :size="20">
              <Picture />
            </el-icon>
          </el-button>
        </el-upload>

        <el-button type="text" size="small" @click="handleVoice" class="toolbar-btn">
          <el-icon :size="20">
            <Microphone />
          </el-icon>
        </el-button>
        <!-- <el-upload
          :show-file-list="false"
          :before-upload="handleVoice"
          class="upload-btn"
        >
          <el-button type="text" size="small" class="toolbar-btn">
            <el-icon :size="20"><Microphone /></el-icon>
          </el-button>
        </el-upload> -->
      </div>

      <div class="toolbar-right">
        <span class="char-count">{{ messageContent.length }}/500</span>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="editor-input">
      <el-input v-model="messageContent" type="textarea" :rows="3" resize="none" placeholder="参与讨论，请输入内容（按Ctrl+Enter发送）"
        @keydown="handleKeydown" @input="handleInput" class="message-textarea" maxlength="500" show-word-limit />
    </div>

    <!-- 发送按钮 -->
    <div class="editor-actions">
      <div class="actions-left">
        <el-text size="small" type="info">
          按 Ctrl + Enter 快速发送
        </el-text>
      </div>
      <div class="actions-right">
        <el-button size="small" @click="handleClear" :disabled="!messageContent.trim()">
          清空
        </el-button>
        <el-button type="primary" size="small" @click="handleSend" :disabled="!messageContent.trim() || sending"
          :loading="sending">
          发送
        </el-button>
      </div>
    </div>

    <!-- 表情选择器 -->
    <div v-if="showEmojiPicker" class="emoji-picker">
      <div class="emoji-grid">
        <span v-for="emoji in emojiList" :key="emoji" class="emoji-item" @click="insertEmoji(emoji)">
          {{ emoji }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { getFriendList } from '@/api/chat.js'


defineOptions({
  name: 'MessageEditor'
})

const emit = defineEmits(['send'])

// 响应式数据
const messageContent = ref('')
const sending = ref(false)
const showEmojiPicker = ref(false)

// 常用表情列表
const emojiList = [
  "😀",
  "😃",
  "😄",
  "😁",
  "😆",
  "😅",
  "😂",
  "🤣",
  "😊",
  "😇",
  "🙂",
  "🙃",
  "😉",
  "😌",
  "😍",
  "🥰",
  "😘",
  "😗",
  "😙",
  "😚",
  "😋",
  "😛",
  "😝",
  "😜",
  "🤪",
  "🤨",
  "🧐",
  "🤓",
  "😎",
  "🤩",
  "🥳",
  "😏",
  "😒",
  "😞",
  "😔",
  "😟",
  "😕",
  "🙁",
  "☹️",
  "😣",
  "😖",
  "😫",
  "😩",
  "🥺",
  "😢",
  "😭",
  "😤",
  "😠",
  "😡",
  "🤬",
  "🤯",
  "😳",
  "🥵",
  "🥶",
  "😱",
  "😨",
  "😰",
  "😥",
  "🤗",
]


// 处理键盘事件
const handleKeydown = (event) => {
  // Ctrl + Enter 发送消息
  if (event.ctrlKey && event.key === 'Enter') {
    event.preventDefault()
    handleSend()
  }
  // Esc 关闭表情选择器
  if (event.key === 'Escape') {
    showEmojiPicker.value = false
  }
}

// 处理输入
const handleInput = (value) => {
  // 限制字符数
  if (value.length > 500) {
    messageContent.value = value.slice(0, 500)
  }
}

// 发送消息
const handleSend = async () => {
  const res = await getFriendList()
  console.log(res);
  /* if (!messageContent.value.trim()) {
    return
  }

  sending.value = true

  try {
    // 模拟发送延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    emit('send', messageContent.value.trim())
    messageContent.value = ''
    showEmojiPicker.value = false

    ElMessage.success('消息发送成功')
  } catch (error) {
    ElMessage.error('消息发送失败')
  } finally {
    sending.value = false
  } */
}

// 清空内容
const handleClear = () => {
  messageContent.value = ''
}

// 表情点击
const handleEmojiClick = () => {
  showEmojiPicker.value = !showEmojiPicker.value
}

// 插入表情
const insertEmoji = (emoji) => {
  messageContent.value += emoji
  showEmojiPicker.value = false
}

// 图片上传
const handleImageUpload = (file) => {
  // 这里应该实现图片上传逻辑
  console.log('上传图片:', file)
  ElMessage.info('图片上传功能待实现')
  return false // 阻止默认上传
}

// 文件上传
const handleVoice = (file) => {
  // 这里应该实现文件上传逻辑
  console.log('发送语音:', file)
  return false // 阻止默认上传
}
</script>

<style lang="scss" scoped>
.message-editor {
  position: relative;
  background: #4b5563;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #6b7280;

  .toolbar-left {
    display: flex;
    gap: 4px;

    .toolbar-btn {
      padding: 4px 8px;
      color: #9ca3af;

      &:hover {
        color: #22c55e;
        background: #374151;
      }
    }

    .upload-btn {
      display: inline-block;
    }
  }

  .toolbar-right {
    .char-count {
      font-size: 12px;
      color: #9ca3af;
    }
  }
}

.editor-input {
  padding: 0 16px;

  .message-textarea {
    :deep(.el-textarea__inner) {
      border: none;
      box-shadow: none;
      resize: none;
      padding: 12px 0;
      font-size: 14px;
      line-height: 1.5;
      background: #4b5563;
      color: #f3f4f6;

      &:focus {
        box-shadow: none;
      }

      &::placeholder {
        color: #9ca3af;
      }
    }

    :deep(.el-input__count) {
      display: none;
    }
  }
}

.editor-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-top: 1px solid #6b7280;

  .actions-left {
    font-size: 12px;
    color: #9ca3af;
  }

  .actions-right {
    display: flex;
    gap: 8px;
  }
}

.emoji-picker {
  position: absolute;
  bottom: 100%;
  left: 16px;
  background: #374151;
  border: 1px solid #6b7280;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  padding: 12px;
  max-width: 300px;

  .emoji-grid {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 4px;

    .emoji-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      cursor: pointer;
      border-radius: 4px;
      font-size: 16px;
      transition: background-color 0.2s;

      &:hover {
        background: #4b5563;
      }
    }
  }
}
</style>
