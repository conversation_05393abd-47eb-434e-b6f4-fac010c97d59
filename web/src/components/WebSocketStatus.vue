<template>
  <div class="websocket-status">
    <el-badge 
      :value="unreadCount" 
      :hidden="unreadCount === 0"
      class="status-badge"
    >
      <el-tooltip 
        :content="statusText" 
        placement="bottom"
      >
        <div 
          :class="['status-indicator', statusClass]"
          @click="handleStatusClick"
        >
          <el-icon>
            <component :is="statusIcon" />
          </el-icon>
        </div>
      </el-tooltip>
    </el-badge>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useWebSocketStore } from '@/pinia/modules/websocket'
import { useUserStore } from '@/pinia/modules/user'
import { ElMessage } from 'element-plus'
import { 
  Connection, 
  Loading, 
  Close 
} from '@element-plus/icons-vue'

const webSocketStore = useWebSocketStore()
const userStore = useUserStore()

// 计算属性
const connectionStatus = computed(() => webSocketStore.connectionStatus)
const unreadCount = computed(() => webSocketStore.unreadCount)

const statusClass = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return 'connected'
    case 'connecting':
      return 'connecting'
    case 'disconnected':
    default:
      return 'disconnected'
  }
})

const statusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return 'WebSocket已连接'
    case 'connecting':
      return 'WebSocket连接中...'
    case 'disconnected':
    default:
      return 'WebSocket未连接'
  }
})

const statusIcon = computed(() => {
  switch (connectionStatus.value) {
    case 'connected':
      return Connection
    case 'connecting':
      return Loading
    case 'disconnected':
    default:
      return Close
  }
})

// 点击状态指示器
const handleStatusClick = async () => {
  if (connectionStatus.value === 'disconnected') {
    try {
      ElMessage.info('正在尝试重新连接...')
      await webSocketStore.reconnect(userStore.token)
      ElMessage.success('WebSocket重连成功')
    } catch (error) {
      ElMessage.error('WebSocket重连失败')
    }
  } else if (connectionStatus.value === 'connected') {
    // 清除未读消息计数
    webSocketStore.clearUnreadCount()
  }
}
</script>

<style scoped>
.websocket-status {
  display: inline-block;
}

.status-badge {
  cursor: pointer;
}

.status-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.status-indicator:hover {
  transform: scale(1.1);
}

.status-indicator.connected {
  background-color: #67c23a;
  color: white;
}

.status-indicator.connecting {
  background-color: #e6a23c;
  color: white;
  animation: pulse 1.5s infinite;
}

.status-indicator.disconnected {
  background-color: #f56c6c;
  color: white;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
