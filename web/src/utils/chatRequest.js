import axios from 'axios'
import { useUserStore } from '@/pinia/modules/user'

let baseURL = 'http://**************:82'
const BASEAPI = '/api/'

const service = axios.create({
  baseURL: `${baseURL}${BASEAPI}`,
  timeout: 999999
})
service.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers = {
        // 'x-token': userStore.token,
        'x-token': `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJEYXRhIjp7ImlwaG9uZV9jb2RlIjoiIiwidXNlcl9pZCI6MTAwMDMsImlzX2xvZ2luIjp0cnVlfSwiZXhwIjoxNzU2NDM3ODQ3LCJuYmYiOjE3NTM4NDU4NDcsImlhdCI6MTc1Mzg0NTg0N30.OflBjUMWWU9_eG_349GkCtSqqKOevZpsm_f_mW6YClY`,
        'Content-Type': 'application/json'
      }
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    return Promise.reject(error)
  }
)

export default service