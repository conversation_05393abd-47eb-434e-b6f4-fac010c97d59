# 即时通讯系统核心逻辑详细文档

## 1. 通讯建立方式

### 1.1 WebSocket连接配置
- **完整连接地址**: `ws://**************:82/api/msgSocket?token=${token}`
- **服务器IP**: `**************`
- **端口**: `82`
- **协议**: WebSocket (ws://)
- **认证参数**: token通过URL查询参数传递
- **建立位置**: `utils/connectionService.js` - `initWebSocket()` 方法

### 1.2 HTTP接口配置
- **基础URL**: `http://**************:81`
- **API前缀**: `/api/`
- **配置文件**: `api/http.js`
- **完整HTTP地址示例**: `http://**************:81/api/[具体接口]`

### 1.3 通信接口列表
| 接口类型 | 地址 | 用途 | 文件位置 |
|---------|------|------|----------|
| WebSocket | `ws://**************:82/api/msgSocket` | 实时消息通信 | `utils/connectionService.js` |
| HTTP API | `http://**************:81/api/` | RESTful接口 | `api/http.js` |
| 配置获取 | `http://**************:81/api/getConfig` | 获取系统配置 | `api/api.js` |
| 用户信息 | `http://**************:81/api/GetUserByID` | 获取用户详情 | `api/api.js` |

## 2. 发送消息接口

### 2.1 WebSocket发送接口
**方法**: `uni.sendSocketMessage()`
**完整路径**: 通过已建立的WebSocket连接 `ws://**************:82/api/msgSocket`

### 2.2 HTTP备用发送接口
**接口路径**: `http://**************:81/api/chatPush`
**请求方法**: POST
**文件位置**: `api/chat.js`

### 2.3 发送消息入参结构
```javascript
const messageParams = {
    fromid: Number,           // 发送者ID
    toId: Number,            // 接收者ID (私聊为用户ID，群聊为群组ID)
    msg: String,             // 消息内容 (已加密)
    typecode: Number,        // 消息类型：1-好友消息，2-群组消息，3-通知消息
    typecode2: Number,       // 内容类型：0-文本，1-音频，2-图片，3-视频，9-语音通话
    t: String,               // 时间戳 (ISO格式)
    groupID: Number          // 群组ID (仅群聊消息)
}
```

### 2.4 发送处理逻辑
1. **消息加密**: 
   - 路径: `utils/decrypt.js` - `encryptAESBase64()`
   - 除 `typecode2=10` 外所有消息都需加密
2. **发送策略**:
   - 主要: WebSocket `uni.sendSocketMessage()`
   - 备用: HTTP POST `http://**************:81/api/chatPush`
3. **状态管理**: 
   - 发送中状态存储在 `sendingMessages` 集合
   - 成功/失败状态更新到Vuex store

### 2.5 返回结果
- **WebSocket**: 无直接返回，通过消息监听器接收确认
- **HTTP**: 标准HTTP响应格式
```javascript
{
    code: Number,     // 0-成功，其他-失败
    data: Object,     // 响应数据
    message: String   // 响应消息
}
```

## 3. 接收消息接口

### 3.1 WebSocket消息接收
**监听地址**: `ws://**************:82/api/msgSocket`
**监听方法**: `uni.onSocketMessage()`
**处理位置**: `utils/connectionService.js` - `setupMessageListener()`

### 3.2 接收消息数据结构
```javascript
const receivedMessage = {
    id: String,              // 消息唯一ID
    typecode: Number,        // 消息类型码
    typecode2: Number,       // 内容类型码
    fromid: Number,          // 发送者ID
    toid: Number,           // 接收者ID
    groupID: Number,        // 群组ID (群聊消息)
    msg: String,            // 加密的消息内容
    t: String,              // 服务器时间戳
}
```

### 3.3 消息解密与处理
1. **解密路径**: `utils/decrypt.js` - `decryptAESBase64()`
2. **处理流程**:
   ```javascript
   // 1. 解析JSON
   const message = JSON.parse(res.data)
   
   // 2. 解密消息内容
   const decryptedMsg = decryptAESBase64(message.msg)
   
   // 3. 确定聊天ID
   const chatid = message.typecode == 2 ? message.groupID : 
                  (message.toid == currentUserId ? message.fromid : message.toid)
   ```

### 3.4 数据库存储结构
**存储接口**: `utils/db.js` - `addTabItem()`
**数据库路径**: indexDB本地数据库
```javascript
const dbMessageParams = {
    id: String,              // 消息ID
    typecode: Number,        // 消息类型
    typecode2: Number,       // 内容类型
    toid: Number,           // 接收者ID
    fromid: Number,         // 发送者ID
    chatid: Number,         // 聊天对象ID
    t: String,              // 时间戳
    msg: String,            // 解密后消息内容
    isRedRead: Number,      // 已读状态 (0-未读，1-已读)
    idDel: Number,          // 删除状态 (0-正常，1-已删除)
    avatar: String,         // 发送者头像URL
    nickname: String,       // 发送者昵称
    senderAvatar: String,   // 群聊中发送者头像
    senderNickname: String, // 群聊中发送者昵称
    lastMessage: String,    // 聊天列表显示文本
    timestamp: Number,      // 时间戳(数字格式)
    unreadCount: Number     // 未读消息数
}
```

## 4. 用户信息获取接口

### 4.1 获取用户详情
**接口路径**: `http://**************:81/api/GetUserByID`
**请求方法**: GET/POST
**文件位置**: `api/api.js`
**参数**: `userId: Number`

### 4.2 用户信息返回结构
```javascript
{
    code: 0,
    data: {
        id: Number,           // 用户ID
        name: String,         // 用户昵称
        head_img: String,     // 头像URL
        iphone_num: String,   // 手机号
        // ... 其他用户信息
    }
}
```

## 5. 私聊消息逻辑

### 5.1 私聊发送参数
```javascript
const privateChatParams = {
    fromid: currentUserId,    // 当前用户ID
    toId: friendId,          // 好友ID
    msg: encryptedMessage,   // 加密消息内容
    typecode: 1,             // 私聊类型码
    typecode2: 0,            // 文本消息
    chatid: friendId,        // 使用好友ID作为聊天标识
    t: new Date().toISOString()
}
```

### 5.2 私聊接收处理
- **聊天ID确定**: `chatid = message.fromid` (对方ID)
- **用户信息获取**: 
  - 优先从 `store.state.friendList` 获取
  - 备用调用 `http://**************:81/api/GetUserByID`
- **头像逻辑**: 使用对方头像显示在聊天列表

## 6. 群聊消息逻辑

### 6.1 群聊发送参数
```javascript
const groupChatParams = {
    fromid: currentUserId,    // 当前用户ID
    toId: groupId,           // 群组ID
    groupID: groupId,        // 群组ID (必须)
    msg: encryptedMessage,   // 加密消息内容
    typecode: 2,             // 群聊类型码
    typecode2: 0,            // 文本消息
    chatid: groupId,         // 使用群组ID作为聊天标识
    t: new Date().toISOString()
}
```

### 6.2 群聊接收处理
- **聊天ID确定**: `chatid = message.groupID`
- **群组信息获取**: 从 `store.state.groupList` 获取群组信息
- **发送者信息**: 调用 `http://**************:81/api/GetUserByID` 获取发送者详情
- **头像逻辑**: 
  - 聊天列表: 使用群组头像
  - 消息显示: 使用发送者头像 (`senderAvatar`)

## 7. 聊天列表管理

### 7.1 聊天列表数据结构
**存储位置**: Vuex Store - `store.state.chatList`
```javascript
const chatListItem = {
    chatid: Number,         // 聊天对象ID
    avatar: String,         // 头像URL
    nickname: String,       // 显示名称
    unreadCount: Number,    // 未读消息数
    lastMessage: String,    // 最后消息内容
    timestamp: Number,      // 最后消息时间戳
    typecode: Number,       // 聊天类型 (1-私聊，2-群聊)
    lastMessageTime: String // 格式化的时间显示
}
```

### 7.2 列表更新接口
**Vuex方法**: `store.commit('setChatList', params)`
**更新逻辑**:
1. 检查列表中是否存在该聊天
2. 存在则更新，不存在则新增
3. 按时间戳排序显示

## 8. 特殊消息类型处理

### 8.1 语音通话消息 (typecode2=9)
**处理路径**: `utils/connectionService.js` - `toVoiceCall()`
**跳转页面**: `/pages/call/voice-call`
**参数传递**:
```javascript
const callParams = {
    newMessage: JSON.stringify(message),
    toId: message.fromid,
    isCaller: false,
    id: message.id
}
```

### 8.2 WebRTC信令消息 (typecode2=11,12)
**事件触发**: `uni.$emit('chat-voice-received', message)`
**处理位置**: 语音通话页面监听该事件

### 8.3 消息撤回 (typecode2=5)
**数据库更新**: `utils/db.js` - `recallMessageInDB()`
**更新字段**: `idDel = 1`, `msg = '[撤回消息]'`

## 9. 数据持久化路径

### 9.1 本地数据库
**数据库类型**: SQLite
**数据库文件**: `_doc/${name}.db`
**表名**: 通过 `tabName()` 函数生成
**操作接口**: `utils/db.js`

### 9.2 Vuex状态管理
**Store文件**: `store/index.js`
**主要状态**:
- `chatList`: 聊天列表
- `friendList`: 好友列表
- `groupList`: 群组列表
- `friendMsg`: 好友消息
- `notifyMsg`: 通知消息

### 9.3 本地存储
**存储方式**: `uni.getStorageSync()` / `uni.setStorageSync()`
**主要数据**:
- `token`: 用户认证令牌
- `userId`: 当前用户ID
- `userInfo`: 用户基本信息

## 10. 错误处理与重连机制

### 10.1 WebSocket重连
**重连方法**: `utils/connectionService.js` - `scheduleReconnect()`
**重连间隔**: 5秒
**重连条件**: token存在且连接未初始化

### 10.2 超时处理
**连接超时**: 20秒
**处理策略**: 超时后仍标记为已初始化，允许应用继续运行

### 10.3 错误日志
**日志输出**: `console.log/error/warn`
**关键节点**: 连接建立、消息收发、数据库操作



示例token：

eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJEYXRhIjp7ImlwaG9uZV9jb2RlIjoiIiwidXNlcl9pZCI6MTAwMDMsImlzX2xvZ2luIjp0cnVlfSwiZXhwIjoxNzU2NDM3ODQ3LCJuYmYiOjE3NTM4NDU4NDcsImlhdCI6MTc1Mzg0NTg0N30.OflBjUMWWU9_eG_349GkCtSqqKOevZpsm_f_mW6YClY